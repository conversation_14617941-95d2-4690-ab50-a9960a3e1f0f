// 前端单元测试 - 故障分析智能助手
// 使用简单的测试框架进行前端功能测试

class FrontendTestSuite {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            total: 0
        };
    }

    // 添加测试用例
    addTest(name, testFunction) {
        this.tests.push({ name, testFunction });
    }

    // 运行所有测试
    async runAllTests() {
        console.log('🧪 开始运行前端单元测试...');
        
        for (const test of this.tests) {
            await this.runSingleTest(test);
        }
        
        this.printResults();
        return this.results;
    }

    // 运行单个测试
    async runSingleTest(test) {
        try {
            console.log(`🔍 测试: ${test.name}`);
            await test.testFunction();
            console.log(`✅ 通过: ${test.name}`);
            this.results.passed++;
        } catch (error) {
            console.error(`❌ 失败: ${test.name}`, error);
            this.results.failed++;
        }
        this.results.total++;
    }

    // 打印测试结果
    printResults() {
        console.log('\n📊 测试结果汇总:');
        console.log(`总计: ${this.results.total}`);
        console.log(`通过: ${this.results.passed}`);
        console.log(`失败: ${this.results.failed}`);
        console.log(`成功率: ${((this.results.passed / this.results.total) * 100).toFixed(2)}%`);
    }

    // 断言函数
    assert(condition, message) {
        if (!condition) {
            throw new Error(message || '断言失败');
        }
    }

    assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(message || `期望 ${expected}，实际 ${actual}`);
        }
    }

    assertNotNull(value, message) {
        if (value === null || value === undefined) {
            throw new Error(message || '值不应为null或undefined');
        }
    }

    assertInstanceOf(obj, constructor, message) {
        if (!(obj instanceof constructor)) {
            throw new Error(message || `对象不是 ${constructor.name} 的实例`);
        }
    }
}

// 创建测试套件实例
const testSuite = new FrontendTestSuite();

// ==================== 状态管理测试 ====================
testSuite.addTest('AppStateManager - 基本功能', () => {
    const stateManager = new AppStateManager();
    
    // 测试初始状态
    testSuite.assertNotNull(stateManager.getState(), '状态管理器应有初始状态');
    testSuite.assertEqual(stateManager.getState('currentTab'), 'fault-analysis', '默认标签页应为fault-analysis');
    
    // 测试状态更新
    stateManager.setState('currentTab', 'equipment-management');
    testSuite.assertEqual(stateManager.getState('currentTab'), 'equipment-management', '状态更新应成功');
    
    // 测试嵌套状态
    stateManager.setState('ui.modals.test', { visible: true });
    testSuite.assertEqual(stateManager.getState('ui.modals.test.visible'), true, '嵌套状态应正确设置');
});

testSuite.addTest('AppStateManager - 监听器', () => {
    const stateManager = new AppStateManager();
    let callbackCalled = false;
    let receivedValue = null;
    
    // 添加监听器
    const unsubscribe = stateManager.subscribe('testValue', (newValue) => {
        callbackCalled = true;
        receivedValue = newValue;
    });
    
    // 触发状态变化
    stateManager.setState('testValue', 'test123');
    
    testSuite.assert(callbackCalled, '监听器回调应被调用');
    testSuite.assertEqual(receivedValue, 'test123', '监听器应接收到正确的值');
    
    // 取消订阅
    unsubscribe();
});

// ==================== DOM管理测试 ====================
testSuite.addTest('DOMManager - 元素获取', () => {
    const domManager = new DOMManager();
    
    // 创建测试元素
    const testDiv = document.createElement('div');
    testDiv.id = 'test-element';
    testDiv.textContent = 'Test Content';
    document.body.appendChild(testDiv);
    
    // 测试元素获取
    const element = domManager.getElement('#test-element');
    testSuite.assertNotNull(element, '应能获取到测试元素');
    testSuite.assertEqual(element.id, 'test-element', '获取的元素ID应正确');
    
    // 清理
    document.body.removeChild(testDiv);
});

testSuite.addTest('DOMManager - 内容设置', () => {
    const domManager = new DOMManager();
    
    // 创建测试元素
    const testDiv = document.createElement('div');
    testDiv.id = 'test-content';
    document.body.appendChild(testDiv);
    
    // 测试内容设置
    const success = domManager.setContent('#test-content', 'New Content');
    testSuite.assert(success, '内容设置应成功');
    testSuite.assertEqual(testDiv.innerHTML, 'New Content', '内容应正确设置');
    
    // 清理
    document.body.removeChild(testDiv);
});

testSuite.addTest('DOMManager - 安全操作', () => {
    const domManager = new DOMManager();
    
    // 测试不存在的元素
    const result = domManager.setContent('#non-existent', 'content');
    testSuite.assertEqual(result, false, '操作不存在的元素应返回false');
    
    // 测试HTML清理
    const cleanHtml = domManager.sanitizeHTML('<script>alert("xss")</script>Hello');
    testSuite.assert(!cleanHtml.includes('<script>'), 'HTML应被清理');
    testSuite.assert(cleanHtml.includes('Hello'), '正常内容应保留');
});

// ==================== 错误处理测试 ====================
testSuite.addTest('ErrorHandler - 错误分类', () => {
    const errorHandler = new ErrorHandler();
    
    // 测试API错误分类
    const apiError = { message: 'fetch failed' };
    const apiCategory = errorHandler.categorizeError(apiError);
    testSuite.assertEqual(apiCategory, 'apiErrors', 'API错误应正确分类');
    
    // 测试流式错误分类
    const streamError = { message: 'stream connection lost' };
    const streamCategory = errorHandler.categorizeError(streamError);
    testSuite.assertEqual(streamCategory, 'streamingErrors', '流式错误应正确分类');
    
    // 测试UI错误分类
    const uiError = { message: 'button click failed' };
    const uiCategory = errorHandler.categorizeError(uiError);
    testSuite.assertEqual(uiCategory, 'uiErrors', 'UI错误应正确分类');
});

testSuite.addTest('ErrorHandler - 用户友好消息', () => {
    const errorHandler = new ErrorHandler();
    
    // 测试网络错误消息
    const networkError = { message: 'fetch error' };
    const networkMessage = errorHandler.getUserFriendlyMessage(networkError);
    testSuite.assert(networkMessage.includes('网络'), '网络错误应显示友好消息');
    
    // 测试流式错误消息
    const streamError = { message: 'stream failed' };
    const streamMessage = errorHandler.getUserFriendlyMessage(streamError);
    testSuite.assert(streamMessage.includes('实时分析'), '流式错误应显示友好消息');
});

// ==================== 流式管理测试 ====================
testSuite.addTest('StreamingManager - 基本功能', () => {
    const streamingManager = new StreamingManager();
    
    // 测试初始状态
    testSuite.assertNotNull(streamingManager.activeStreams, '应有活跃流映射');
    testSuite.assertNotNull(streamingManager.streamHistory, '应有流历史记录');
    
    // 测试流ID生成
    const streamId1 = streamingManager.generateStreamId();
    const streamId2 = streamingManager.generateStreamId();
    testSuite.assertNotNull(streamId1, '应能生成流ID');
    testSuite.assert(streamId1 !== streamId2, '生成的流ID应唯一');
});

testSuite.addTest('StreamingManager - HTML生成', () => {
    const streamingManager = new StreamingManager();
    
    // 测试思考模式HTML
    const thinkingHtml = streamingManager.generateStreamingHTML(true);
    testSuite.assert(thinkingHtml.includes('reasoning-stream'), '思考模式应包含推理流元素');
    testSuite.assert(thinkingHtml.includes('final-stream-r1'), '思考模式应包含R1最终流元素');
    
    // 测试标准模式HTML
    const standardHtml = streamingManager.generateStreamingHTML(false);
    testSuite.assert(standardHtml.includes('final-stream-v3'), '标准模式应包含V3最终流元素');
    testSuite.assert(!standardHtml.includes('reasoning-stream'), '标准模式不应包含推理流元素');
});

// ==================== 移动端适配测试 ====================
testSuite.addTest('MobileAdaptationManager - 设备检测', () => {
    const mobileManager = new MobileAdaptationManager();
    
    // 测试设备信息获取
    const deviceInfo = mobileManager.getDeviceInfo();
    testSuite.assertNotNull(deviceInfo, '应能获取设备信息');
    testSuite.assert(typeof deviceInfo.isMobile === 'boolean', 'isMobile应为布尔值');
    testSuite.assert(typeof deviceInfo.touchSupport === 'boolean', 'touchSupport应为布尔值');
    testSuite.assert(typeof deviceInfo.viewportWidth === 'number', 'viewportWidth应为数字');
});

testSuite.addTest('MobileAdaptationManager - 方向检测', () => {
    const mobileManager = new MobileAdaptationManager();
    
    // 测试方向获取
    const orientation = mobileManager.getCurrentOrientation();
    testSuite.assert(['portrait', 'landscape'].includes(orientation), '方向应为portrait或landscape');
});

// ==================== 工具函数测试 ====================
testSuite.addTest('工具函数 - 防抖', async () => {
    let callCount = 0;
    const debouncedFn = debounce(() => callCount++, 100);
    
    // 快速调用多次
    debouncedFn();
    debouncedFn();
    debouncedFn();
    
    // 等待防抖延迟
    await new Promise(resolve => setTimeout(resolve, 150));
    
    testSuite.assertEqual(callCount, 1, '防抖函数应只执行一次');
});

testSuite.addTest('工具函数 - 本地存储', () => {
    // 测试存储
    const success = storage.set('test-key', { value: 'test-data' });
    testSuite.assert(success, '数据存储应成功');
    
    // 测试读取
    const data = storage.get('test-key');
    testSuite.assertNotNull(data, '应能读取存储的数据');
    testSuite.assertEqual(data.value, 'test-data', '读取的数据应正确');
    
    // 测试删除
    const deleteSuccess = storage.remove('test-key');
    testSuite.assert(deleteSuccess, '数据删除应成功');
    
    // 验证删除
    const deletedData = storage.get('test-key');
    testSuite.assertEqual(deletedData, null, '删除后应返回null');
});

// ==================== 集成测试 ====================
testSuite.addTest('集成测试 - 状态与DOM联动', () => {
    // 创建测试元素
    const testDiv = document.createElement('div');
    testDiv.id = 'integration-test';
    document.body.appendChild(testDiv);
    
    // 测试状态变化触发DOM更新
    window.appState.subscribe('integrationTest', (value) => {
        window.domManager.setContent('#integration-test', `Value: ${value}`);
    });
    
    window.appState.setState('integrationTest', 'test-value');
    
    // 验证DOM更新
    testSuite.assertEqual(testDiv.innerHTML, 'Value: test-value', 'DOM应根据状态变化更新');
    
    // 清理
    document.body.removeChild(testDiv);
});

// 导出测试套件
window.frontendTestSuite = testSuite;

// 自动运行测试（如果在测试环境中）
if (window.location.search.includes('runTests=true')) {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            testSuite.runAllTests().then(results => {
                console.log('🎉 前端测试完成!', results);
            });
        }, 1000);
    });
}
