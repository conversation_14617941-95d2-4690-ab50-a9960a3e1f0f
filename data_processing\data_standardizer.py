#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据标注标准化工具
用于统一处理和清洗电力故障分析数据
解决数据标注不一致和格式混乱问题
"""

import re
import json
import os
from typing import Dict, List, Any, Optional
from pathlib import Path
from loguru import logger


class DataStandardizer:
    """数据标注标准化器"""
    
    def __init__(self):
        # 电力专业术语标准化映射
        self.power_terms_mapping = {
            # 设备类型标准化
            '变压器': ['变压器', '主变', '配变', '所变', 'transformer'],
            '断路器': ['断路器', '开关', 'breaker', 'CB'],
            '隔离开关': ['隔离开关', '刀闸', 'isolator', 'DS'],
            '电流互感器': ['电流互感器', 'CT', 'current transformer'],
            '电压互感器': ['电压互感器', 'PT', 'VT', 'voltage transformer'],
            '避雷器': ['避雷器', '氧化锌避雷器', 'arrester'],
            '电容器': ['电容器', '并联电容器', 'capacitor'],
            '电抗器': ['电抗器', 'reactor'],
            '母线': ['母线', 'busbar', 'bus'],
            
            # 故障类型标准化
            '短路故障': ['短路', '接地短路', '相间短路', 'short circuit'],
            '接地故障': ['接地', '单相接地', 'ground fault'],
            '过载故障': ['过载', '过负荷', 'overload'],
            '绝缘故障': ['绝缘击穿', '绝缘老化', 'insulation fault'],
            '机械故障': ['机械卡涩', '操作机构故障', 'mechanical fault'],
            
            # 保护类型标准化
            '差动保护': ['差动保护', '主保护', 'differential protection'],
            '距离保护': ['距离保护', 'distance protection'],
            '过流保护': ['过流保护', 'overcurrent protection'],
            '零序保护': ['零序保护', 'zero sequence protection'],
        }
        
        # 技术参数标准化规则
        self.parameter_patterns = {
            'voltage': r'(\d+(?:\.\d+)?)\s*[kKmM]?[vV]',
            'current': r'(\d+(?:\.\d+)?)\s*[aA]',
            'power': r'(\d+(?:\.\d+)?)\s*[kKmM]?[wW]',
            'frequency': r'(\d+(?:\.\d+)?)\s*[hH][zZ]',
            'resistance': r'(\d+(?:\.\d+)?)\s*[mM]?[ΩΩ]',
            'temperature': r'(\d+(?:\.\d+)?)\s*℃',
            'percentage': r'(\d+(?:\.\d+)?)\s*%',
        }
        
        # 单位标准化映射
        self.unit_standardization = {
            'kv': 'kV', 'KV': 'kV', 'kV': 'kV',
            'mv': 'MV', 'MV': 'MV', 'Mv': 'MV',
            'kw': 'kW', 'KW': 'kW', 'kW': 'kW',
            'mw': 'MW', 'MW': 'MW', 'Mw': 'MW',
            'mva': 'MVA', 'MVA': 'MVA', 'Mva': 'MVA',
            'hz': 'Hz', 'HZ': 'Hz', 'Hz': 'Hz',
            'a': 'A', 'A': 'A',
            'v': 'V', 'V': 'V',
            'ω': 'Ω', 'Ω': 'Ω', 'ohm': 'Ω',
        }
    
    def standardize_text(self, text: str) -> str:
        """标准化文本内容"""
        if not text:
            return ""
        
        logger.info(f"开始标准化文本，长度: {len(text)}")
        
        # 1. 标准化电力术语
        standardized_text = self._standardize_power_terms(text)
        
        # 2. 标准化技术参数
        standardized_text = self._standardize_technical_parameters(standardized_text)
        
        # 3. 标准化单位
        standardized_text = self._standardize_units(standardized_text)
        
        # 4. 清理格式
        standardized_text = self._clean_format(standardized_text)
        
        logger.info(f"文本标准化完成，新长度: {len(standardized_text)}")
        return standardized_text
    
    def _standardize_power_terms(self, text: str) -> str:
        """标准化电力专业术语"""
        result = text
        
        for standard_term, variants in self.power_terms_mapping.items():
            for variant in variants:
                if variant != standard_term:
                    # 使用词边界匹配，避免误替换
                    pattern = r'\b' + re.escape(variant) + r'\b'
                    result = re.sub(pattern, standard_term, result, flags=re.IGNORECASE)
        
        return result
    
    def _standardize_technical_parameters(self, text: str) -> str:
        """标准化技术参数格式"""
        result = text
        
        # 电压等级标准化
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[kK][vV]', r'\1kV', result)
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[mM][vV]', r'\1MV', result)
        
        # 功率标准化
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[kK][wW]', r'\1kW', result)
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[mM][wW]', r'\1MW', result)
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[mM][vV][aA]', r'\1MVA', result)
        
        # 电流标准化
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[aA](?![a-zA-Z])', r'\1A', result)
        
        # 频率标准化
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[hH][zZ]', r'\1Hz', result)
        
        # 阻抗标准化
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[mM][ΩΩ]', r'\1MΩ', result)
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[ΩΩ]', r'\1Ω', result)
        
        # 温度标准化
        result = re.sub(r'(\d+(?:\.\d+)?)\s*℃', r'\1℃', result)
        
        return result
    
    def _standardize_units(self, text: str) -> str:
        """标准化单位表示"""
        result = text
        
        for incorrect, correct in self.unit_standardization.items():
            if incorrect != correct:
                # 确保单位前有数字
                pattern = r'(\d+(?:\.\d+)?)\s*' + re.escape(incorrect) + r'\b'
                replacement = r'\1' + correct
                result = re.sub(pattern, replacement, result, flags=re.IGNORECASE)
        
        return result
    
    def _clean_format(self, text: str) -> str:
        """清理文本格式"""
        result = text
        
        # 移除多余的空格
        result = re.sub(r'\s+', ' ', result)
        
        # 标准化标点符号
        result = re.sub(r'[：:]{2,}', '：', result)
        result = re.sub(r'[；;]{2,}', '；', result)
        result = re.sub(r'[，,]{2,}', '，', result)
        result = re.sub(r'[。.]{2,}', '。', result)
        
        # 移除行首行尾空格
        lines = [line.strip() for line in result.split('\n')]
        result = '\n'.join(lines)
        
        return result.strip()
    
    def standardize_json_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化JSON数据"""
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                if isinstance(value, str):
                    result[key] = self.standardize_text(value)
                elif isinstance(value, (dict, list)):
                    result[key] = self.standardize_json_data(value)
                else:
                    result[key] = value
            return result
        elif isinstance(data, list):
            return [self.standardize_json_data(item) for item in data]
        elif isinstance(data, str):
            return self.standardize_text(data)
        else:
            return data
    
    def process_file(self, file_path: str, output_path: str = None) -> bool:
        """处理单个文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return False
            
            if output_path is None:
                output_path = file_path.parent / f"{file_path.stem}_standardized{file_path.suffix}"
            
            # 根据文件类型处理
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                standardized_data = self.standardize_json_data(data)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(standardized_data, f, ensure_ascii=False, indent=2)
            
            elif file_path.suffix.lower() in ['.md', '.txt']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                standardized_content = self.standardize_text(content)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(standardized_content)
            
            else:
                logger.warning(f"不支持的文件类型: {file_path.suffix}")
                return False
            
            logger.info(f"文件标准化完成: {file_path} -> {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            return False
    
    def batch_process_directory(self, directory: str, output_directory: str = None) -> int:
        """批量处理目录下的文件"""
        directory = Path(directory)
        if not directory.exists():
            logger.error(f"目录不存在: {directory}")
            return 0
        
        if output_directory is None:
            output_directory = directory / "standardized"
        
        output_directory = Path(output_directory)
        output_directory.mkdir(exist_ok=True)
        
        processed_count = 0
        supported_extensions = ['.json', '.md', '.txt']
        
        for file_path in directory.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                # 保持相对路径结构
                relative_path = file_path.relative_to(directory)
                output_path = output_directory / relative_path
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                if self.process_file(str(file_path), str(output_path)):
                    processed_count += 1
        
        logger.info(f"批量处理完成，共处理 {processed_count} 个文件")
        return processed_count


def main():
    """主函数 - 用于测试和批量处理"""
    standardizer = DataStandardizer()
    
    # 测试文本标准化
    test_text = """
    110 kV变压器差动保护动作，现场检查发现A相套管渗油，
    温度升高至85 ℃，绝缘电阻为150 MΩ，需要立即处理。
    """
    
    print("原始文本:")
    print(test_text)
    print("\n标准化后:")
    print(standardizer.standardize_text(test_text))
    
    # 批量处理示例
    # standardizer.batch_process_directory("./data/01_raw", "./data/standardized")


if __name__ == "__main__":
    main()
