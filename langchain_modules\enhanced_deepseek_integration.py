#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强DeepSeek集成模块 - 融合优化RAG检索
为DeepSeek-R1和DeepSeek-V3提供高质量的上下文信息
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from retriever.unified_professional_retriever import get_unified_retriever
    UNIFIED_RETRIEVER_AVAILABLE = True
except ImportError as e:
    UNIFIED_RETRIEVER_AVAILABLE = False
    logging.error(f"统一检索器不可用: {e}")


class EnhancedDeepSeekIntegration:
    """增强DeepSeek集成 - 融合统一专业检索"""

    def __init__(self):
        self.unified_retriever = None
        if UNIFIED_RETRIEVER_AVAILABLE:
            try:
                config = {
                    "chroma_path": "./embeddings/chroma_store_new",
                    "collection_name": "baiyin_power_fault_collection",
                    "cache_enabled": True
                }
                self.unified_retriever = get_unified_retriever(config)
            except Exception as e:
                logging.error(f"统一检索器初始化失败: {e}")

    def get_enhanced_context(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """获取增强的上下文信息"""
        try:
            if not self.unified_retriever:
                return {"context": "", "sources": [], "available": False}

            # 使用统一专业检索获取相关信息
            from retriever.unified_professional_retriever import RetrievalStrategy
            response = self.unified_retriever.search(query, strategy=RetrievalStrategy.HYBRID, top_k=max_results)

            if not response.success:
                return {"context": "", "sources": [], "available": False}

            # 转换结果格式
            search_results = {
                "success": True,
                "results": [
                    {
                        "content": result.content,
                        "score": result.score,
                        "metadata": result.metadata,
                        "id": getattr(result, 'id', ''),
                        "title": result.metadata.get('title', result.content[:100])
                    }
                    for result in response.results
                ],
                "intent": {
                    "equipment_types": response.query_analysis.equipment_types,
                    "fault_types": response.query_analysis.fault_indicators,
                    "voltage_levels": [],
                    "locations": ["白银"]
                },
                "analysis": response.query_analysis.__dict__
            }

            # 构建上下文信息
            context_parts = []
            sources = []

            for i, result in enumerate(search_results.get("results", []), 1):
                content = result.get("content", "")
                metadata = result.get("metadata", {})
                score = result.get("score", 0)

                # 构建上下文条目
                context_entry = f"""
【相关资料{i}】
设备类型: {metadata.get('equipment_type', '通用')}
电压等级: {metadata.get('voltage_level', '未知')}
故障类型: {metadata.get('fault_type', '一般')}
相关度: {score:.2f}
内容: {content[:500]}...
"""
                context_parts.append(context_entry)

                # 记录来源
                sources.append({
                    "id": result.get("id", ""),
                    "title": result.get("title", ""),
                    "score": score,
                    "metadata": metadata
                })

            enhanced_context = "\n".join(context_parts)

            return {
                "context": enhanced_context,
                "sources": sources,
                "available": True,
                "intent": search_results.get("intent", {}),
                "analysis": search_results.get("analysis", {}),
                "total_results": len(sources)
            }

        except Exception as e:
            logging.error(f"获取增强上下文失败: {e}")
            return {"context": "", "sources": [], "available": False, "error": str(e)}

    def build_enhanced_prompt_for_r1(self, query: str, context_info: Dict[str, Any]) -> str:
        """为DeepSeek-R1构建增强提示词"""
        try:
            context = context_info.get("context", "")
            intent = context_info.get("intent", {})

            enhanced_prompt = f"""你是白银市电力系统资深故障诊断专家，基于DeepSeek-R1深度推理架构，具备以下专业能力：

🧠 **推理模式要求：**
请使用<think>标签展示完整的专业推理过程，然后用<answer>标签给出最终结论。

⚡ **专业背景：**
- 国家电网白银供电公司高级工程师
- 20年以上变电站运维和故障分析经验
- 熟悉110kV/220kV电力设备运行特点
- 精通西北地区电力系统故障诊断

📊 **当前查询分析：**
查询内容: {query}
识别的设备类型: {intent.get('equipment_types', ['未识别'])}
识别的故障类型: {intent.get('fault_types', ['未识别'])}
识别的电压等级: {intent.get('voltage_levels', ['未识别'])}
识别的地区: {intent.get('locations', ['白银'])}

📚 **相关技术资料：**
{context}

🎯 **分析要求：**
1. 在<think>标签中展示专家级的推理过程
2. 结合提供的技术资料进行深度分析
3. 考虑白银地区的环境和设备特点
4. 在<answer>标签中给出专业的诊断结论和处理建议

请开始您的专业分析："""

            return enhanced_prompt

        except Exception as e:
            logging.error(f"构建R1增强提示词失败: {e}")
            return query

    def build_enhanced_prompt_for_v3(self, query: str, context_info: Dict[str, Any]) -> str:
        """为DeepSeek-V3构建增强提示词"""
        try:
            context = context_info.get("context", "")
            intent = context_info.get("intent", {})

            enhanced_prompt = f"""你是白银市电力系统故障诊断专家，请基于以下信息提供专业分析：

⚡ **专业身份：**
- 电力系统高级工程师，专业从事变电站设备故障诊断
- 熟悉110kV/220kV电力设备运行特性
- 具备丰富的白银地区电力系统故障处理经验

📊 **查询信息：**
用户查询: {query}
设备类型: {', '.join(intent.get('equipment_types', ['通用设备']))}
故障类型: {', '.join(intent.get('fault_types', ['一般故障']))}
电压等级: {', '.join(intent.get('voltage_levels', ['未指定']))}

📚 **相关技术资料：**
{context}

🎯 **分析要求：**
请结合提供的技术资料，用自然流畅的语言进行专业分析。分析应包括故障原因、处理方法和预防措施。请避免使用编号列表，采用连贯的段落形式表达。

请开始您的专业分析："""

            return enhanced_prompt

        except Exception as e:
            logging.error(f"构建V3增强提示词失败: {e}")
            return query


# 创建全局实例
enhanced_deepseek_integration = EnhancedDeepSeekIntegration()
