#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户反馈收集系统
"""

import time
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

@dataclass
class UserFeedback:
    """用户反馈数据类"""
    feedback_id: str
    timestamp: float
    user_id: str
    session_id: str
    feedback_type: str  # rating, comment, bug_report, feature_request
    content: str
    rating: Optional[int] = None  # 1-5星评分
    query: Optional[str] = None  # 相关查询
    response_quality: Optional[int] = None  # 响应质量评分
    response_time: Optional[float] = None  # 响应时间
    model_used: Optional[str] = None  # 使用的模型
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict:
        return asdict(self)

@dataclass
class SystemUsageStats:
    """系统使用统计"""
    timestamp: float
    total_queries: int
    unique_users: int
    avg_session_duration: float
    popular_query_types: Dict[str, int]
    model_usage_stats: Dict[str, int]
    user_satisfaction_avg: float

class FeedbackCollector:
    """用户反馈收集器"""
    
    def __init__(self, storage_path: str = "monitoring/feedback"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 反馈存储
        self.feedbacks: List[UserFeedback] = []
        self.max_feedbacks = 5000  # 最大存储反馈数量
        
        # 使用统计
        self.user_sessions: Dict[str, Dict] = {}  # user_id -> session_info
        self.query_stats: Dict[str, int] = {}  # query_type -> count
        self.model_stats: Dict[str, int] = {}  # model -> count
        
        # 自动保存配置
        self.auto_save_interval = 600  # 10分钟
        self.last_save_time = time.time()

    def collect_feedback(self, user_id: str, session_id: str, feedback_type: str, 
                        content: str, **kwargs) -> str:
        """收集用户反馈"""
        feedback_id = str(uuid.uuid4())
        
        feedback = UserFeedback(
            feedback_id=feedback_id,
            timestamp=time.time(),
            user_id=user_id,
            session_id=session_id,
            feedback_type=feedback_type,
            content=content,
            rating=kwargs.get('rating'),
            query=kwargs.get('query'),
            response_quality=kwargs.get('response_quality'),
            response_time=kwargs.get('response_time'),
            model_used=kwargs.get('model_used'),
            tags=kwargs.get('tags', []),
            metadata=kwargs.get('metadata', {})
        )
        
        self.feedbacks.append(feedback)
        
        # 限制反馈数量
        if len(self.feedbacks) > self.max_feedbacks:
            self.feedbacks = self.feedbacks[-self.max_feedbacks//2:]
        
        # 更新统计
        self._update_stats(feedback)
        
        logger.info(f"收集到用户反馈: {feedback_type} from {user_id}")
        
        # 检查是否需要自动保存
        current_time = time.time()
        if current_time - self.last_save_time > self.auto_save_interval:
            self.save_feedbacks()
            self.last_save_time = current_time
        
        return feedback_id

    def collect_rating(self, user_id: str, session_id: str, rating: int, 
                      query: str = None, model_used: str = None) -> str:
        """收集评分反馈"""
        return self.collect_feedback(
            user_id=user_id,
            session_id=session_id,
            feedback_type="rating",
            content=f"用户评分: {rating}/5",
            rating=rating,
            query=query,
            model_used=model_used
        )

    def collect_comment(self, user_id: str, session_id: str, comment: str,
                       query: str = None, response_quality: int = None) -> str:
        """收集评论反馈"""
        return self.collect_feedback(
            user_id=user_id,
            session_id=session_id,
            feedback_type="comment",
            content=comment,
            query=query,
            response_quality=response_quality
        )

    def collect_bug_report(self, user_id: str, session_id: str, bug_description: str,
                          steps_to_reproduce: str = None, expected_behavior: str = None) -> str:
        """收集错误报告"""
        metadata = {}
        if steps_to_reproduce:
            metadata['steps_to_reproduce'] = steps_to_reproduce
        if expected_behavior:
            metadata['expected_behavior'] = expected_behavior
        
        return self.collect_feedback(
            user_id=user_id,
            session_id=session_id,
            feedback_type="bug_report",
            content=bug_description,
            tags=["bug"],
            metadata=metadata
        )

    def collect_feature_request(self, user_id: str, session_id: str, feature_description: str,
                               priority: str = "medium") -> str:
        """收集功能请求"""
        return self.collect_feedback(
            user_id=user_id,
            session_id=session_id,
            feedback_type="feature_request",
            content=feature_description,
            tags=["feature_request", priority],
            metadata={"priority": priority}
        )

    def track_user_session(self, user_id: str, session_id: str, action: str, **kwargs):
        """跟踪用户会话"""
        current_time = time.time()
        
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = {
                'session_id': session_id,
                'start_time': current_time,
                'last_activity': current_time,
                'actions': [],
                'queries_count': 0,
                'models_used': set()
            }
        
        session = self.user_sessions[user_id]
        session['last_activity'] = current_time
        session['actions'].append({
            'timestamp': current_time,
            'action': action,
            'details': kwargs
        })
        
        # 更新统计
        if action == 'query':
            session['queries_count'] += 1
            query_type = kwargs.get('query_type', 'unknown')
            self.query_stats[query_type] = self.query_stats.get(query_type, 0) + 1
            
            model = kwargs.get('model')
            if model:
                session['models_used'].add(model)
                self.model_stats[model] = self.model_stats.get(model, 0) + 1

    def get_feedback_summary(self, days: int = 7) -> Dict[str, Any]:
        """获取反馈摘要"""
        current_time = time.time()
        start_time = current_time - (days * 24 * 3600)
        
        # 筛选时间范围内的反馈
        recent_feedbacks = [f for f in self.feedbacks if f.timestamp >= start_time]
        
        if not recent_feedbacks:
            return {
                'period_days': days,
                'total_feedbacks': 0,
                'feedback_breakdown': {},
                'average_rating': 0,
                'satisfaction_level': 'no_data'
            }
        
        # 统计反馈类型
        feedback_breakdown = {}
        ratings = []
        
        for feedback in recent_feedbacks:
            feedback_type = feedback.feedback_type
            feedback_breakdown[feedback_type] = feedback_breakdown.get(feedback_type, 0) + 1
            
            if feedback.rating:
                ratings.append(feedback.rating)
        
        # 计算平均评分
        avg_rating = sum(ratings) / len(ratings) if ratings else 0
        
        # 评估满意度
        if avg_rating >= 4.5:
            satisfaction_level = "excellent"
        elif avg_rating >= 4.0:
            satisfaction_level = "good"
        elif avg_rating >= 3.0:
            satisfaction_level = "fair"
        elif avg_rating >= 2.0:
            satisfaction_level = "poor"
        else:
            satisfaction_level = "very_poor"
        
        return {
            'period_days': days,
            'total_feedbacks': len(recent_feedbacks),
            'feedback_breakdown': feedback_breakdown,
            'average_rating': avg_rating,
            'total_ratings': len(ratings),
            'satisfaction_level': satisfaction_level,
            'unique_users': len(set(f.user_id for f in recent_feedbacks))
        }

    def get_usage_stats(self) -> SystemUsageStats:
        """获取使用统计"""
        current_time = time.time()
        
        # 计算活跃用户
        active_sessions = [s for s in self.user_sessions.values() 
                          if current_time - s['last_activity'] < 3600]  # 1小时内活跃
        
        # 计算平均会话时长
        session_durations = []
        for session in self.user_sessions.values():
            duration = session['last_activity'] - session['start_time']
            session_durations.append(duration)
        
        avg_session_duration = sum(session_durations) / len(session_durations) if session_durations else 0
        
        # 获取用户满意度
        recent_ratings = [f.rating for f in self.feedbacks[-100:] if f.rating]  # 最近100个评分
        avg_satisfaction = sum(recent_ratings) / len(recent_ratings) if recent_ratings else 0
        
        return SystemUsageStats(
            timestamp=current_time,
            total_queries=sum(self.query_stats.values()),
            unique_users=len(self.user_sessions),
            avg_session_duration=avg_session_duration,
            popular_query_types=dict(sorted(self.query_stats.items(), key=lambda x: x[1], reverse=True)[:5]),
            model_usage_stats=self.model_stats.copy(),
            user_satisfaction_avg=avg_satisfaction
        )

    def _update_stats(self, feedback: UserFeedback):
        """更新统计信息"""
        # 这里可以添加更多的统计逻辑
        pass

    def save_feedbacks(self, filename: str = None):
        """保存反馈到文件"""
        if not filename:
            filename = f"feedbacks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = self.storage_path / filename
        
        try:
            data = {
                'timestamp': time.time(),
                'summary': self.get_feedback_summary(),
                'usage_stats': asdict(self.get_usage_stats()),
                'feedbacks': [f.to_dict() for f in self.feedbacks[-1000:]]  # 保存最近1000个反馈
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"用户反馈已保存到: {filepath}")
            
        except Exception as e:
            logger.error(f"保存用户反馈失败: {e}")

    def generate_feedback_report(self) -> Dict[str, Any]:
        """生成反馈报告"""
        summary_7d = self.get_feedback_summary(7)
        summary_30d = self.get_feedback_summary(30)
        usage_stats = self.get_usage_stats()
        
        return {
            'report_timestamp': time.time(),
            'summary_7_days': summary_7d,
            'summary_30_days': summary_30d,
            'usage_statistics': asdict(usage_stats),
            'recommendations': self._generate_recommendations(summary_7d, usage_stats)
        }

    def _generate_recommendations(self, summary: Dict, usage_stats: SystemUsageStats) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if summary['average_rating'] < 3.5:
            recommendations.append("用户满意度较低，需要改进响应质量")
        
        if usage_stats.avg_session_duration < 300:  # 5分钟
            recommendations.append("用户会话时长较短，可能需要改进用户体验")
        
        if summary['feedback_breakdown'].get('bug_report', 0) > summary['total_feedbacks'] * 0.1:
            recommendations.append("错误报告较多，需要加强系统稳定性")
        
        if summary['total_feedbacks'] < 10:
            recommendations.append("反馈数量较少，建议增加反馈收集渠道")
        
        return recommendations

# 全局反馈收集实例
feedback_collector = FeedbackCollector()
