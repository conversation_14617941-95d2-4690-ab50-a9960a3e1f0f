#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
案例标签管理器
"""

import json
import os
import time
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter

from case_classification_system import CaseClassifier, CaseTag

@dataclass
class CaseMetadata:
    """案例元数据"""
    case_id: str
    title: str
    file_path: str
    tags: CaseTag
    created_time: float
    updated_time: float
    quality_score: float
    view_count: int = 0
    usage_count: int = 0

class CaseTagManager:
    """案例标签管理器"""
    
    def __init__(self, storage_path: str = "knowledge_base/metadata"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.metadata_file = self.storage_path / "case_metadata.json"
        self.tags_index_file = self.storage_path / "tags_index.json"
        
        self.classifier = CaseClassifier()
        self.case_metadata: Dict[str, CaseMetadata] = {}
        self.tags_index: Dict[str, Set[str]] = defaultdict(set)
        
        self._load_metadata()

    def add_case(self, case_id: str, title: str, file_path: str, content: str, 
                quality_score: float = 0.5) -> CaseMetadata:
        """添加新案例"""
        # 自动分类
        tags = self.classifier.classify_case(content, title)
        
        # 创建元数据
        metadata = CaseMetadata(
            case_id=case_id,
            title=title,
            file_path=file_path,
            tags=tags,
            created_time=time.time(),
            updated_time=time.time(),
            quality_score=quality_score
        )
        
        # 存储元数据
        self.case_metadata[case_id] = metadata
        
        # 更新标签索引
        self._update_tags_index(case_id, tags)
        
        # 保存到文件
        self._save_metadata()
        
        return metadata

    def update_case_tags(self, case_id: str, new_tags: CaseTag) -> bool:
        """更新案例标签"""
        if case_id not in self.case_metadata:
            return False
        
        # 移除旧标签索引
        old_tags = self.case_metadata[case_id].tags
        self._remove_from_tags_index(case_id, old_tags)
        
        # 更新标签
        self.case_metadata[case_id].tags = new_tags
        self.case_metadata[case_id].updated_time = time.time()
        
        # 添加新标签索引
        self._update_tags_index(case_id, new_tags)
        
        # 保存到文件
        self._save_metadata()
        
        return True

    def search_by_tags(self, **criteria) -> List[CaseMetadata]:
        """根据标签搜索案例"""
        matching_cases = set(self.case_metadata.keys())
        
        # 设备类型筛选
        if 'equipment_types' in criteria:
            equipment_cases = set()
            for equipment_type in criteria['equipment_types']:
                equipment_cases.update(self.tags_index.get(f"equipment:{equipment_type}", set()))
            matching_cases &= equipment_cases
        
        # 故障类型筛选
        if 'fault_types' in criteria:
            fault_cases = set()
            for fault_type in criteria['fault_types']:
                fault_cases.update(self.tags_index.get(f"fault:{fault_type}", set()))
            matching_cases &= fault_cases
        
        # 电压等级筛选
        if 'voltage_levels' in criteria:
            voltage_cases = set()
            for voltage_level in criteria['voltage_levels']:
                voltage_cases.update(self.tags_index.get(f"voltage:{voltage_level}", set()))
            matching_cases &= voltage_cases
        
        # 地理位置筛选
        if 'geographic_locations' in criteria:
            location_cases = set()
            for location in criteria['geographic_locations']:
                location_cases.update(self.tags_index.get(f"location:{location}", set()))
            matching_cases &= location_cases
        
        # 处理难度筛选
        if 'processing_difficulty' in criteria:
            difficulty = criteria['processing_difficulty']
            difficulty_cases = self.tags_index.get(f"difficulty:{difficulty}", set())
            matching_cases &= difficulty_cases
        
        # 影响程度筛选
        if 'impact_level' in criteria:
            impact = criteria['impact_level']
            impact_cases = self.tags_index.get(f"impact:{impact}", set())
            matching_cases &= impact_cases
        
        # 关键词筛选
        if 'keywords' in criteria:
            keyword_cases = set()
            for keyword in criteria['keywords']:
                keyword_cases.update(self.tags_index.get(f"keyword:{keyword}", set()))
            matching_cases &= keyword_cases
        
        # 返回匹配的案例元数据
        results = []
        for case_id in matching_cases:
            if case_id in self.case_metadata:
                results.append(self.case_metadata[case_id])
        
        # 按质量分数和使用次数排序
        results.sort(key=lambda x: (x.quality_score, x.usage_count), reverse=True)
        
        return results

    def get_similar_cases(self, case_id: str, limit: int = 5) -> List[Tuple[CaseMetadata, float]]:
        """获取相似案例"""
        if case_id not in self.case_metadata:
            return []
        
        target_case = self.case_metadata[case_id]
        target_tags = target_case.tags
        
        similarities = []
        
        for other_id, other_case in self.case_metadata.items():
            if other_id == case_id:
                continue
            
            similarity = self._calculate_tag_similarity(target_tags, other_case.tags)
            if similarity > 0.3:  # 相似度阈值
                similarities.append((other_case, similarity))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:limit]

    def get_tag_statistics(self) -> Dict[str, Dict]:
        """获取标签统计信息"""
        stats = {
            'equipment_types': Counter(),
            'fault_types': Counter(),
            'voltage_levels': Counter(),
            'geographic_locations': Counter(),
            'processing_difficulty': Counter(),
            'impact_level': Counter(),
            'urgency_level': Counter(),
            'keywords': Counter(),
            'technical_domains': Counter()
        }
        
        for case_metadata in self.case_metadata.values():
            tags = case_metadata.tags
            
            for equipment_type in tags.equipment_types:
                stats['equipment_types'][equipment_type.value] += 1
            
            for fault_type in tags.fault_types:
                stats['fault_types'][fault_type.value] += 1
            
            for voltage_level in tags.voltage_levels:
                stats['voltage_levels'][voltage_level.value] += 1
            
            for location in tags.geographic_locations:
                stats['geographic_locations'][location.value] += 1
            
            stats['processing_difficulty'][tags.processing_difficulty.value] += 1
            stats['impact_level'][tags.impact_level.value] += 1
            stats['urgency_level'][tags.urgency_level.value] += 1
            
            for keyword in tags.keywords:
                stats['keywords'][keyword] += 1
            
            for domain in tags.technical_domains:
                stats['technical_domains'][domain] += 1
        
        # 转换为普通字典
        return {key: dict(counter) for key, counter in stats.items()}

    def recommend_cases(self, query_tags: CaseTag, limit: int = 10) -> List[Tuple[CaseMetadata, float]]:
        """基于查询标签推荐案例"""
        recommendations = []
        
        for case_id, case_metadata in self.case_metadata.items():
            relevance = self._calculate_tag_similarity(query_tags, case_metadata.tags)
            
            # 考虑案例质量和使用频率
            quality_bonus = case_metadata.quality_score * 0.2
            usage_bonus = min(case_metadata.usage_count / 100, 0.1)
            
            final_score = relevance + quality_bonus + usage_bonus
            
            if final_score > 0.2:
                recommendations.append((case_metadata, final_score))
        
        # 按推荐分数排序
        recommendations.sort(key=lambda x: x[1], reverse=True)
        
        return recommendations[:limit]

    def update_case_usage(self, case_id: str):
        """更新案例使用统计"""
        if case_id in self.case_metadata:
            self.case_metadata[case_id].usage_count += 1
            self.case_metadata[case_id].view_count += 1
            self._save_metadata()

    def export_tags_report(self, output_file: str = None) -> str:
        """导出标签统计报告"""
        if not output_file:
            output_file = self.storage_path / f"tags_report_{int(time.time())}.json"
        
        stats = self.get_tag_statistics()
        
        report = {
            'generated_time': time.time(),
            'total_cases': len(self.case_metadata),
            'statistics': stats,
            'top_keywords': dict(Counter(stats['keywords']).most_common(20)),
            'coverage_analysis': self._analyze_coverage()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return str(output_file)

    def _update_tags_index(self, case_id: str, tags: CaseTag):
        """更新标签索引"""
        # 设备类型索引
        for equipment_type in tags.equipment_types:
            self.tags_index[f"equipment:{equipment_type.value}"].add(case_id)
        
        # 故障类型索引
        for fault_type in tags.fault_types:
            self.tags_index[f"fault:{fault_type.value}"].add(case_id)
        
        # 电压等级索引
        for voltage_level in tags.voltage_levels:
            self.tags_index[f"voltage:{voltage_level.value}"].add(case_id)
        
        # 地理位置索引
        for location in tags.geographic_locations:
            self.tags_index[f"location:{location.value}"].add(case_id)
        
        # 处理难度索引
        self.tags_index[f"difficulty:{tags.processing_difficulty.value}"].add(case_id)
        
        # 影响程度索引
        self.tags_index[f"impact:{tags.impact_level.value}"].add(case_id)
        
        # 紧急程度索引
        self.tags_index[f"urgency:{tags.urgency_level.value}"].add(case_id)
        
        # 关键词索引
        for keyword in tags.keywords:
            self.tags_index[f"keyword:{keyword}"].add(case_id)
        
        # 技术领域索引
        for domain in tags.technical_domains:
            self.tags_index[f"domain:{domain}"].add(case_id)

    def _remove_from_tags_index(self, case_id: str, tags: CaseTag):
        """从标签索引中移除"""
        # 实现移除逻辑（与更新逻辑相反）
        for equipment_type in tags.equipment_types:
            self.tags_index[f"equipment:{equipment_type.value}"].discard(case_id)
        
        for fault_type in tags.fault_types:
            self.tags_index[f"fault:{fault_type.value}"].discard(case_id)
        
        # ... 其他标签类型的移除逻辑

    def _calculate_tag_similarity(self, tags1: CaseTag, tags2: CaseTag) -> float:
        """计算标签相似度"""
        similarity = 0.0
        
        # 设备类型相似度 (权重: 0.3)
        equipment_sim = self._calculate_list_similarity(
            [e.value for e in tags1.equipment_types],
            [e.value for e in tags2.equipment_types]
        )
        similarity += equipment_sim * 0.3
        
        # 故障类型相似度 (权重: 0.25)
        fault_sim = self._calculate_list_similarity(
            [f.value for f in tags1.fault_types],
            [f.value for f in tags2.fault_types]
        )
        similarity += fault_sim * 0.25
        
        # 电压等级相似度 (权重: 0.15)
        voltage_sim = self._calculate_list_similarity(
            [v.value for v in tags1.voltage_levels],
            [v.value for v in tags2.voltage_levels]
        )
        similarity += voltage_sim * 0.15
        
        # 地理位置相似度 (权重: 0.1)
        location_sim = self._calculate_list_similarity(
            [l.value for l in tags1.geographic_locations],
            [l.value for l in tags2.geographic_locations]
        )
        similarity += location_sim * 0.1
        
        # 关键词相似度 (权重: 0.2)
        keyword_sim = self._calculate_list_similarity(tags1.keywords, tags2.keywords)
        similarity += keyword_sim * 0.2
        
        return min(similarity, 1.0)

    def _calculate_list_similarity(self, list1: List[str], list2: List[str]) -> float:
        """计算列表相似度"""
        if not list1 or not list2:
            return 0.0
        
        set1 = set(list1)
        set2 = set(list2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0

    def _analyze_coverage(self) -> Dict[str, any]:
        """分析标签覆盖情况"""
        stats = self.get_tag_statistics()
        
        return {
            'equipment_coverage': len(stats['equipment_types']),
            'fault_coverage': len(stats['fault_types']),
            'voltage_coverage': len(stats['voltage_levels']),
            'location_coverage': len(stats['geographic_locations']),
            'most_common_equipment': max(stats['equipment_types'].items(), key=lambda x: x[1]) if stats['equipment_types'] else None,
            'most_common_fault': max(stats['fault_types'].items(), key=lambda x: x[1]) if stats['fault_types'] else None,
            'coverage_gaps': self._identify_coverage_gaps(stats)
        }

    def _identify_coverage_gaps(self, stats: Dict) -> List[str]:
        """识别覆盖缺口"""
        gaps = []
        
        # 检查是否缺少某些重要设备类型的案例
        important_equipment = ['变压器', '断路器', '母线', '电缆', 'GIS设备']
        for equipment in important_equipment:
            if equipment not in stats['equipment_types'] or stats['equipment_types'][equipment] < 3:
                gaps.append(f"缺少{equipment}相关案例")
        
        # 检查电压等级覆盖
        important_voltages = ['500kV', '220kV', '110kV', '35kV']
        for voltage in important_voltages:
            if voltage not in stats['voltage_levels'] or stats['voltage_levels'][voltage] < 2:
                gaps.append(f"缺少{voltage}电压等级案例")
        
        return gaps

    def _save_metadata(self):
        """保存元数据到文件"""
        # 转换为可序列化格式
        serializable_metadata = {}
        for case_id, metadata in self.case_metadata.items():
            serializable_metadata[case_id] = {
                'case_id': metadata.case_id,
                'title': metadata.title,
                'file_path': metadata.file_path,
                'tags': metadata.tags.to_dict(),
                'created_time': metadata.created_time,
                'updated_time': metadata.updated_time,
                'quality_score': metadata.quality_score,
                'view_count': metadata.view_count,
                'usage_count': metadata.usage_count
            }
        
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_metadata, f, ensure_ascii=False, indent=2)
        
        # 保存标签索引
        serializable_index = {}
        for key, case_set in self.tags_index.items():
            serializable_index[key] = list(case_set)
        
        with open(self.tags_index_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_index, f, ensure_ascii=False, indent=2)

    def _load_metadata(self):
        """从文件加载元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 重建元数据对象
                for case_id, metadata_dict in data.items():
                    # 重建CaseTag对象
                    tags_dict = metadata_dict['tags']
                    # 这里需要从字典重建CaseTag对象的逻辑
                    # 为简化，暂时跳过完整的反序列化
                    
            except Exception as e:
                print(f"加载元数据失败: {e}")
        
        if self.tags_index_file.exists():
            try:
                with open(self.tags_index_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for key, case_list in data.items():
                    self.tags_index[key] = set(case_list)
                    
            except Exception as e:
                print(f"加载标签索引失败: {e}")

# 全局标签管理器实例
case_tag_manager = CaseTagManager()
