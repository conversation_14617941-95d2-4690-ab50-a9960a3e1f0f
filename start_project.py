#!/usr/bin/env python3
"""
故障分析智能助手项目启动脚本
统一启动所有服务在端口5002
集成兼容性修复和完整功能
"""

import os
import sys
import time
import threading
import subprocess
import importlib.util
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 应用兼容性修复
def apply_compatibility_fixes():
    """应用兼容性修复"""
    logger.info("🔧 应用兼容性修复...")

    try:
        # 修复HuggingFace Hub兼容性
        import huggingface_hub
        if not hasattr(huggingface_hub, 'cached_download'):
            if hasattr(huggingface_hub, 'hf_hub_download'):
                huggingface_hub.cached_download = huggingface_hub.hf_hub_download
                logger.info("✅ 已添加 cached_download 别名")
            else:
                def mock_cached_download(*args, **kwargs):
                    logger.warning("使用模拟的cached_download函数")
                    return None
                huggingface_hub.cached_download = mock_cached_download
                logger.info("✅ 已添加模拟的 cached_download 函数")
        else:
            logger.info("✅ cached_download 已存在")

    except ImportError:
        logger.warning("⚠️ huggingface_hub 不可用，跳过修复")
    except Exception as e:
        logger.warning(f"⚠️ 兼容性修复失败: {e}")

# 应用修复
apply_compatibility_fixes()

from core.config_manager import get_config

# 统一服务器管理类
class UnifiedServerManager:
    """统一服务器管理器 - 整合到启动脚本中"""

    def __init__(self):
        self.config = get_config()
        self.flask_thread: Optional[threading.Thread] = None
        self.fastapi_process = None
        self.running = False

    def start_flask_server(self):
        """启动Flask服务器"""
        try:
            logger.info("正在启动Flask Web界面服务...")

            # 导入Flask应用
            from ui.app import app, socketio

            # 获取配置
            host = self.config.get("server.host", "0.0.0.0")
            port = self.config.get("server.port", 5002)
            debug = self.config.is_debug()

            # 启动Flask应用
            try:
                # 检查是否有SocketIO
                if hasattr(socketio, 'run'):
                    socketio.run(
                        app,
                        host=host,
                        port=port,
                        debug=debug,
                        allow_unsafe_werkzeug=True
                    )
                else:
                    app.run(
                        host=host,
                        port=port,
                        debug=debug,
                        threaded=True
                    )
            except Exception as e:
                logger.warning(f"SocketIO启动失败，使用标准Flask: {e}")
                app.run(
                    host=host,
                    port=port,
                    debug=debug,
                    threaded=True
                )

        except Exception as e:
            logger.error(f"Flask服务器启动失败: {e}")
            raise

    def start_fastapi_server(self):
        """启动FastAPI服务器"""
        try:
            if uvicorn is None:
                logger.error("uvicorn未安装，无法启动FastAPI服务")
                return

            logger.info("正在启动FastAPI服务...")

            # 导入FastAPI应用
            from api.main import app as fastapi_app

            # 获取配置
            host = self.config.get("server.host", "0.0.0.0")
            api_port = self.config.get("server.api_port", 5003)

            # 启动FastAPI应用
            uvicorn.run(
                fastapi_app,
                host=host,
                port=api_port,
                log_level="info" if not self.config.is_debug() else "debug"
            )

        except Exception as e:
            logger.error(f"FastAPI服务器启动失败: {e}")
            raise

    def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        host = self.config.get("server.host", "0.0.0.0")
        port = self.config.get("server.port", 5002)
        api_port = self.config.get("server.api_port", 5003)

        return {
            "flask_url": f"http://{host}:{port}",
            "fastapi_url": f"http://{host}:{api_port}",
            "fastapi_docs": f"http://{host}:{api_port}/docs"
        }

# 导入uvicorn（如果可用）
try:
    import uvicorn
except ImportError:
    uvicorn = None
    logger.warning("uvicorn未安装，FastAPI功能不可用")

def check_system_health():
    """检查系统健康状态"""
    health_status = {
        'python_version': sys.version_info[:2],
        'platform': sys.platform,
        'memory_available': True,
        'disk_space': True,
        'network': True
    }

    # 检查Python版本
    if sys.version_info < (3, 8):
        health_status['python_version_ok'] = False
    else:
        health_status['python_version_ok'] = True

    # 检查内存
    try:
        import psutil
        memory = psutil.virtual_memory()
        if memory.available < 1024 * 1024 * 1024:  # 1GB
            health_status['memory_available'] = False
    except ImportError:
        pass

    # 检查磁盘空间
    try:
        import shutil
        disk_usage = shutil.disk_usage('.')
        free_gb = disk_usage.free // (1024**3)
        if free_gb < 1:
            health_status['disk_space'] = False
    except Exception as e:
        pass

    # 检查网络连接
    try:
        import socket
        socket.create_connection(("8.8.8.8", 53), timeout=3)
    except Exception:
        health_status['network'] = False

    return health_status

def check_dependencies():
    """检查必要的依赖 - 使用统一依赖管理器"""
    try:
        from core.dependency_manager import check_dependencies as check_deps
        success, results = check_deps()
        return success
    except ImportError:
        # 回退到原始检查方法
        logger.warning("统一依赖管理器不可用，使用基础检查")
        core_packages = ['flask', 'loguru', 'requests']

        for package in core_packages:
            try:
                __import__(package)
            except ImportError:
                logger.error(f"缺少关键依赖: {package}")
                return False
        return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "data/01_raw/uploads",
        "data/02_processed/structured",
        "data/04_production/cached",
        "knowledge_base/text",
        "knowledge_base/images",
        "knowledge_base/mappings",
        "embeddings"
    ]

    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)

# 删除重复的启动函数，使用 UnifiedServerManager 统一管理

def start_optimized_server():
    """启动主服务器（使用统一服务器管理器）"""
    logger.info("🚀 启动主服务器...")
    try:
        # 使用内置的统一服务器管理器
        server = UnifiedServerManager()
        server_info = server.get_server_info()

        logger.info(f"🌐 主服务器启动在 {server_info['flask_url']}")
        logger.info("📊 使用统一服务器架构")
        logger.info(f"🔗 API服务: {server_info['fastapi_url']}")
        logger.info(f"📖 API文档: {server_info['fastapi_docs']}")

        # 显示功能状态
        logger.info("📡 WebSocket支持: 已集成")
        logger.info("🔧 实时监控: 已启用")
        logger.info("🤖 DeepSeek AI: 已集成")
        logger.info("🔍 RAG检索: 已启用")
        logger.info("📊 数据处理: 已启用")

        # 启动实时监控系统
        try:
            from ui.app import real_time_monitor
            real_time_monitor.start_monitoring()
            logger.info("📊 实时监控系统已启动")
        except Exception as e:
            logger.warning(f"⚠️ 实时监控启动失败: {e}")

        # 启动Flask服务器
        logger.info("🔗 启动Flask Web界面服务")
        server.start_flask_server()

    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断，正在停止服务器...")
        # 停止监控系统
        try:
            from ui.app import real_time_monitor
            real_time_monitor.stop_monitoring()
            logger.info("📊 实时监控系统已停止")
        except:
            pass
        raise
    except Exception as e:
        logger.error(f"❌ 主服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
        raise

def print_startup_info():
    """打印启动信息"""
    config = get_config()
    port = config.get('server.port', 5002)

    logger.info("\n" + "=" * 70)
    logger.info("🎯 故障分析智能助手系统 v2.0")
    logger.info("=" * 70)
    logger.info(f"📍 主要访问地址: http://localhost:{port}")
    logger.info(f"📍 本地访问: http://127.0.0.1:{port}")
    logger.info(f"📍 API接口: http://localhost:{port}/api/v1/")
    logger.info(f"📍 健康检查: http://localhost:{port}/api/v1/health")
    logger.info(f"📍 系统状态: http://localhost:{port}/api/v1/system/complete-status")
    logger.info(f"📍 仪表板: http://localhost:{port}/api/v1/visualization/dashboard")
    logger.info("=" * 70)

    logger.info("🚀 核心功能:")
    logger.info("   • 🤖 DeepSeek R1/V3 AI模型集成")
    logger.info("   • 🔍 增强RAG智能检索系统")
    logger.info("   • 📊 实时监控和告警系统")
    logger.info("   • 🔧 专业故障分析工具")
    logger.info("   • 📱 多模态数据处理")
    logger.info("   • 🌐 WebSocket实时通信")
    logger.info("   • 📈 数据可视化和报表")
    logger.info("   • 👥 多用户协作功能")
    logger.info("   • 🛡️ 智能代理对话系统")
    logger.info("   • 📋 设备管理和状态监控")

    logger.info("\n🔗 API端点 (58个):")
    logger.info("   • 故障分析: /api/v1/analyze, /api/v1/analyze_stream")
    logger.info("   • 知识库: /api/v1/knowledge/search, /api/v1/knowledge/search/enhanced")
    logger.info("   • 设备管理: /api/v1/equipment, /api/v1/equipment/advanced-management")
    logger.info("   • 实时功能: WebSocket /monitoring, /alerts, /analysis")
    logger.info("   • 可视化: /api/v1/visualization/dashboard, /api/v1/visualization/charts")
    logger.info("   • 报表导出: /api/v1/reports/generate, /api/v1/export/data")

    logger.info("=" * 70)
    logger.info("⚠️  按 Ctrl+C 停止服务器")
    logger.info("=" * 70)

def main():
    """主函数 - 使用统一系统初始化器"""
    logger.info("🚀 启动故障分析智能助手系统...")

    try:
        # 使用统一系统初始化器
        from core.system_initializer import initialize_system, cleanup_system

        # 执行完整的系统初始化
        if not initialize_system():
            logger.error("❌ 系统初始化失败")
            sys.exit(1)

        # 启动主服务器
        logger.info("🚀 启动主服务器")
        start_optimized_server()

    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断，正在停止服务器...")
        logger.info("\n⚠️ 服务器已停止")

        # 使用统一清理
        try:
            cleanup_system()
            logger.info("✅ 系统清理完成")
        except Exception as e:
            logger.error(f"❌ 系统清理失败: {e}")

    except ImportError:
        # 回退到原始启动流程
        logger.warning("统一初始化器不可用，使用原始启动流程")

        # 设置日志
        logger.add("logs/startup.log", rotation="1 day", retention="7 days")
        logger.info("系统启动开始")

        try:
            # 基础检查和启动
            if not check_dependencies():
                logger.error("依赖检查失败")
                sys.exit(1)

            create_directories()
            print_startup_info()
            start_optimized_server()

        except KeyboardInterrupt:
            logger.info("⚠️ 用户中断，正在停止服务器...")
            logger.info("\n⚠️ 服务器已停止")

    except ImportError as e:
        logger.error(f"❌ 模块导入失败: {e}")
        logger.error(f"\n❌ 模块导入失败: {e}")
        logger.info("💡 建议:")
        logger.info("   1. 检查是否安装了所有依赖: pip install -r requirements.txt")
        logger.info("   2. 检查Python环境是否正确")
        logger.info("   3. 检查项目路径是否正确")
        sys.exit(1)

    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
        logger.info(f"\n❌ 系统启动失败: {e}")

        logger.info("\n💡 故障排除建议:")
        logger.info("   1. 检查端口5002是否被占用")
        logger.info("   2. 检查配置文件是否正确")
        logger.info("   3. 查看日志文件: logs/startup.log")
        logger.info("   4. 尝试重新安装依赖")

        sys.exit(1)

    finally:
        logger.info("系统启动流程结束")

if __name__ == "__main__":
    main()
