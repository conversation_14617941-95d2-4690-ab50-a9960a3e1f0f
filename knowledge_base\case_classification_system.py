#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电力系统故障案例分类和标签体系
"""

from enum import Enum
from typing import Dict, List, Set, Optional
from dataclasses import dataclass
import json
import re

class EquipmentType(Enum):
    """设备类型枚举"""
    TRANSFORMER = "变压器"
    CIRCUIT_BREAKER = "断路器"
    BUSBAR = "母线"
    TRANSMISSION_LINE = "输电线路"
    CABLE = "电缆"
    GIS = "GIS设备"
    REACTOR = "电抗器"
    CAPACITOR = "电容器"
    GENERATOR = "发电机"
    MOTOR = "电动机"
    PROTECTION_RELAY = "继电保护"
    MEASUREMENT = "计量设备"
    COMMUNICATION = "通信设备"
    SCADA = "SCADA系统"
    OTHER = "其他设备"

class FaultType(Enum):
    """故障类型枚举"""
    SHORT_CIRCUIT = "短路故障"
    GROUND_FAULT = "接地故障"
    INSULATION_FAULT = "绝缘故障"
    OVERLOAD = "过载故障"
    OVERVOLTAGE = "过电压"
    UNDERVOLTAGE = "欠电压"
    FREQUENCY_DEVIATION = "频率偏差"
    HARMONIC_POLLUTION = "谐波污染"
    PROTECTION_MALFUNCTION = "保护误动"
    PROTECTION_FAILURE = "保护拒动"
    MECHANICAL_FAULT = "机械故障"
    THERMAL_FAULT = "热故障"
    AGING_FAULT = "老化故障"
    EXTERNAL_FAULT = "外力故障"
    HUMAN_ERROR = "人为故障"
    DESIGN_DEFECT = "设计缺陷"
    MAINTENANCE_ISSUE = "维护问题"
    ENVIRONMENTAL_FAULT = "环境故障"

class VoltageLevel(Enum):
    """电压等级枚举"""
    UHV_AC = "1000kV交流"
    UHV_DC = "±800kV直流"
    EHV_500 = "500kV"
    EHV_330 = "330kV"
    EHV_220 = "220kV"
    HV_110 = "110kV"
    HV_66 = "66kV"
    MV_35 = "35kV"
    MV_20 = "20kV"
    MV_10 = "10kV"
    MV_6 = "6kV"
    LV_380 = "380V"
    LV_220 = "220V"

class GeographicLocation(Enum):
    """地理位置枚举"""
    BAIYIN_MAIN = "白银主城区"
    BAIYIN_INDUSTRIAL = "白银工业园区"
    BAIYIN_RURAL = "白银农村地区"
    BAIYIN_MINING = "白银矿区"
    BAIYIN_SUBSTATION_1 = "白银第一变电站"
    BAIYIN_SUBSTATION_2 = "白银第二变电站"
    BAIYIN_SUBSTATION_3 = "白银第三变电站"
    OTHER_LOCATION = "其他地区"

class ProcessingDifficulty(Enum):
    """处理难度枚举"""
    SIMPLE = "简单"
    MEDIUM = "中等"
    COMPLEX = "复杂"
    VERY_COMPLEX = "非常复杂"

class ImpactLevel(Enum):
    """影响程度枚举"""
    MINOR = "轻微"
    MODERATE = "一般"
    MAJOR = "较大"
    SEVERE = "严重"
    CRITICAL = "特别严重"

class UrgencyLevel(Enum):
    """紧急程度枚举"""
    LOW = "低"
    MEDIUM = "中"
    HIGH = "高"
    CRITICAL = "紧急"

class SeasonalFactor(Enum):
    """季节因素枚举"""
    SPRING = "春季"
    SUMMER = "夏季"
    AUTUMN = "秋季"
    WINTER = "冬季"
    RAINY_SEASON = "雨季"
    DRY_SEASON = "旱季"

@dataclass
class CaseTag:
    """案例标签数据类"""
    equipment_types: List[EquipmentType]
    fault_types: List[FaultType]
    voltage_levels: List[VoltageLevel]
    geographic_locations: List[GeographicLocation]
    processing_difficulty: ProcessingDifficulty
    impact_level: ImpactLevel
    urgency_level: UrgencyLevel
    seasonal_factors: List[SeasonalFactor]
    keywords: List[str]
    technical_domains: List[str]
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'equipment_types': [e.value for e in self.equipment_types],
            'fault_types': [f.value for f in self.fault_types],
            'voltage_levels': [v.value for v in self.voltage_levels],
            'geographic_locations': [g.value for g in self.geographic_locations],
            'processing_difficulty': self.processing_difficulty.value,
            'impact_level': self.impact_level.value,
            'urgency_level': self.urgency_level.value,
            'seasonal_factors': [s.value for s in self.seasonal_factors],
            'keywords': self.keywords,
            'technical_domains': self.technical_domains
        }

class CaseClassifier:
    """案例分类器"""
    
    def __init__(self):
        # 关键词映射表
        self.equipment_keywords = {
            EquipmentType.TRANSFORMER: ['变压器', '主变', '配变', '电力变压器', '油浸式变压器', '干式变压器'],
            EquipmentType.CIRCUIT_BREAKER: ['断路器', '开关', '断路开关', 'SF6断路器', '真空断路器'],
            EquipmentType.BUSBAR: ['母线', '汇流排', '母排', '主母线', '分段母线'],
            EquipmentType.TRANSMISSION_LINE: ['输电线路', '线路', '架空线', '输电线'],
            EquipmentType.CABLE: ['电缆', '电力电缆', '高压电缆', '地下电缆'],
            EquipmentType.GIS: ['GIS', 'GIS设备', '气体绝缘开关设备'],
            EquipmentType.PROTECTION_RELAY: ['继电保护', '保护装置', '保护系统', '差动保护', '距离保护']
        }
        
        self.fault_keywords = {
            FaultType.SHORT_CIRCUIT: ['短路', '相间短路', '三相短路', '两相短路'],
            FaultType.GROUND_FAULT: ['接地', '单相接地', '接地故障', '对地短路'],
            FaultType.INSULATION_FAULT: ['绝缘', '击穿', '闪络', '绝缘老化', '绝缘损坏'],
            FaultType.PROTECTION_MALFUNCTION: ['保护误动', '误动', '误跳', '保护误动作'],
            FaultType.HARMONIC_POLLUTION: ['谐波', '谐波污染', '谐波超标', '电能质量']
        }
        
        self.voltage_keywords = {
            VoltageLevel.EHV_500: ['500kV', '500千伏'],
            VoltageLevel.EHV_220: ['220kV', '220千伏'],
            VoltageLevel.HV_110: ['110kV', '110千伏'],
            VoltageLevel.MV_35: ['35kV', '35千伏'],
            VoltageLevel.MV_10: ['10kV', '10千伏']
        }
        
        self.location_keywords = {
            GeographicLocation.BAIYIN_MAIN: ['白银主城', '白银市区'],
            GeographicLocation.BAIYIN_INDUSTRIAL: ['白银工业园区', '工业园区', '园区'],
            GeographicLocation.BAIYIN_SUBSTATION_1: ['白银第一变电站', '第一变电站'],
            GeographicLocation.BAIYIN_SUBSTATION_2: ['白银第二变电站', '第二变电站'],
            GeographicLocation.BAIYIN_SUBSTATION_3: ['白银第三变电站', '第三变电站']
        }

    def classify_case(self, case_content: str, case_title: str = "") -> CaseTag:
        """对案例进行自动分类"""
        content = (case_title + " " + case_content).lower()
        
        # 识别设备类型
        equipment_types = self._identify_equipment_types(content)
        
        # 识别故障类型
        fault_types = self._identify_fault_types(content)
        
        # 识别电压等级
        voltage_levels = self._identify_voltage_levels(content)
        
        # 识别地理位置
        geographic_locations = self._identify_geographic_locations(content)
        
        # 评估处理难度
        processing_difficulty = self._assess_processing_difficulty(content)
        
        # 评估影响程度
        impact_level = self._assess_impact_level(content)
        
        # 评估紧急程度
        urgency_level = self._assess_urgency_level(content)
        
        # 识别季节因素
        seasonal_factors = self._identify_seasonal_factors(content)
        
        # 提取关键词
        keywords = self._extract_keywords(content)
        
        # 识别技术领域
        technical_domains = self._identify_technical_domains(content)
        
        return CaseTag(
            equipment_types=equipment_types,
            fault_types=fault_types,
            voltage_levels=voltage_levels,
            geographic_locations=geographic_locations,
            processing_difficulty=processing_difficulty,
            impact_level=impact_level,
            urgency_level=urgency_level,
            seasonal_factors=seasonal_factors,
            keywords=keywords,
            technical_domains=technical_domains
        )

    def _identify_equipment_types(self, content: str) -> List[EquipmentType]:
        """识别设备类型"""
        identified = []
        for equipment_type, keywords in self.equipment_keywords.items():
            if any(keyword in content for keyword in keywords):
                identified.append(equipment_type)
        return identified or [EquipmentType.OTHER]

    def _identify_fault_types(self, content: str) -> List[FaultType]:
        """识别故障类型"""
        identified = []
        for fault_type, keywords in self.fault_keywords.items():
            if any(keyword in content for keyword in keywords):
                identified.append(fault_type)
        return identified

    def _identify_voltage_levels(self, content: str) -> List[VoltageLevel]:
        """识别电压等级"""
        identified = []
        for voltage_level, keywords in self.voltage_keywords.items():
            if any(keyword in content for keyword in keywords):
                identified.append(voltage_level)
        return identified

    def _identify_geographic_locations(self, content: str) -> List[GeographicLocation]:
        """识别地理位置"""
        identified = []
        for location, keywords in self.location_keywords.items():
            if any(keyword in content for keyword in keywords):
                identified.append(location)
        
        # 如果包含"白银"但没有具体位置，标记为其他位置
        if '白银' in content and not identified:
            identified.append(GeographicLocation.OTHER_LOCATION)
            
        return identified

    def _assess_processing_difficulty(self, content: str) -> ProcessingDifficulty:
        """评估处理难度"""
        complex_indicators = ['复杂', '困难', '多重', '系统性', '协调', '综合治理']
        simple_indicators = ['简单', '常规', '标准', '直接']
        
        if any(indicator in content for indicator in complex_indicators):
            return ProcessingDifficulty.COMPLEX
        elif any(indicator in content for indicator in simple_indicators):
            return ProcessingDifficulty.SIMPLE
        else:
            return ProcessingDifficulty.MEDIUM

    def _assess_impact_level(self, content: str) -> ImpactLevel:
        """评估影响程度"""
        severe_indicators = ['全停', '大面积停电', '重要用户', '严重影响']
        major_indicators = ['停电', '跳闸', '影响生产']
        minor_indicators = ['轻微', '局部', '短时']
        
        if any(indicator in content for indicator in severe_indicators):
            return ImpactLevel.SEVERE
        elif any(indicator in content for indicator in major_indicators):
            return ImpactLevel.MAJOR
        elif any(indicator in content for indicator in minor_indicators):
            return ImpactLevel.MINOR
        else:
            return ImpactLevel.MODERATE

    def _assess_urgency_level(self, content: str) -> UrgencyLevel:
        """评估紧急程度"""
        critical_indicators = ['紧急', '事故', '应急', '立即']
        high_indicators = ['尽快', '及时', '迅速']
        
        if any(indicator in content for indicator in critical_indicators):
            return UrgencyLevel.CRITICAL
        elif any(indicator in content for indicator in high_indicators):
            return UrgencyLevel.HIGH
        else:
            return UrgencyLevel.MEDIUM

    def _identify_seasonal_factors(self, content: str) -> List[SeasonalFactor]:
        """识别季节因素"""
        seasonal_keywords = {
            SeasonalFactor.SUMMER: ['夏季', '高温', '负荷高峰'],
            SeasonalFactor.WINTER: ['冬季', '低温', '结冰'],
            SeasonalFactor.RAINY_SEASON: ['雨季', '降雨', '潮湿', '雾霾'],
            SeasonalFactor.SPRING: ['春季'],
            SeasonalFactor.AUTUMN: ['秋季']
        }
        
        identified = []
        for season, keywords in seasonal_keywords.items():
            if any(keyword in content for keyword in keywords):
                identified.append(season)
        
        return identified

    def _extract_keywords(self, content: str) -> List[str]:
        """提取关键词"""
        # 电力专业术语
        power_terms = [
            '变压器', '断路器', '母线', '电缆', '继电保护', '短路', '接地', 
            '绝缘', '谐波', '电能质量', '故障', '跳闸', '保护', '维护',
            '白银', 'GIS', 'SF6', '差动保护', '距离保护'
        ]
        
        found_keywords = []
        for term in power_terms:
            if term in content:
                found_keywords.append(term)
        
        return found_keywords

    def _identify_technical_domains(self, content: str) -> List[str]:
        """识别技术领域"""
        domains = {
            '继电保护': ['保护', '继电保护', '差动', '距离', '过流'],
            '电能质量': ['谐波', '电能质量', '功率因数', '电压波动'],
            '绝缘技术': ['绝缘', '击穿', '闪络', '污闪'],
            '设备维护': ['维护', '检修', '试验', '状态监测'],
            '系统运行': ['调度', '运行', '负荷', '潮流'],
            '故障分析': ['故障', '事故', '分析', '诊断']
        }
        
        identified_domains = []
        for domain, keywords in domains.items():
            if any(keyword in content for keyword in keywords):
                identified_domains.append(domain)
        
        return identified_domains

# 预定义的案例分类模板
CASE_CLASSIFICATION_TEMPLATES = {
    "transformer_fault": {
        "name": "变压器故障",
        "equipment_types": [EquipmentType.TRANSFORMER],
        "common_faults": [FaultType.INSULATION_FAULT, FaultType.OVERLOAD, FaultType.SHORT_CIRCUIT],
        "typical_keywords": ["变压器", "主变", "配变", "绝缘", "过载", "短路"]
    },
    "protection_malfunction": {
        "name": "继电保护误动",
        "equipment_types": [EquipmentType.PROTECTION_RELAY],
        "common_faults": [FaultType.PROTECTION_MALFUNCTION],
        "typical_keywords": ["继电保护", "保护误动", "误跳", "差动保护", "距离保护"]
    },
    "cable_fault": {
        "name": "电缆故障",
        "equipment_types": [EquipmentType.CABLE],
        "common_faults": [FaultType.GROUND_FAULT, FaultType.INSULATION_FAULT],
        "typical_keywords": ["电缆", "接地故障", "绝缘击穿", "电缆隧道"]
    },
    "power_quality": {
        "name": "电能质量问题",
        "equipment_types": [EquipmentType.OTHER],
        "common_faults": [FaultType.HARMONIC_POLLUTION],
        "typical_keywords": ["谐波", "电能质量", "功率因数", "滤波器"]
    }
}

def create_case_classifier():
    """创建案例分类器实例"""
    return CaseClassifier()

if __name__ == "__main__":
    # 测试案例分类器
    classifier = CaseClassifier()
    
    test_case = """
    白银第一变电站500kV主变压器差动保护误动案例
    故障时间: 2024年6月15日 14:30
    故障设备: 500kV主变压器T1
    故障类型: 差动保护误动
    """
    
    tags = classifier.classify_case(test_case)
    print("案例分类结果:")
    print(json.dumps(tags.to_dict(), ensure_ascii=False, indent=2))
