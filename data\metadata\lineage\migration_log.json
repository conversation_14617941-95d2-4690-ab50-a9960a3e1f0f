{"migration_timestamp": "2025-07-23T09:22:51.727102", "total_items": 29, "migrations": [{"source": "raw/equipment_photo_001.txt", "target": "01_raw/equipment/equipment_photo_001.txt", "type": "file", "timestamp": "2025-07-23T09:22:50.912610"}, {"source": "raw/fault_cases", "target": "01_raw/fault_cases", "type": "directory", "timestamp": "2025-07-23T09:22:50.942123"}, {"source": "raw/sensor_data", "target": "01_raw/sensor_data", "type": "directory", "timestamp": "2025-07-23T09:22:51.003900"}, {"source": "raw/maintenance_log_001.json", "target": "01_raw/maintenance_logs/maintenance_log_001.json", "type": "file", "timestamp": "2025-07-23T09:22:51.007210"}, {"source": "raw/inspection_checklist_001.txt", "target": "01_raw/inspection_reports/inspection_checklist_001.txt", "type": "file", "timestamp": "2025-07-23T09:22:51.010411"}, {"source": "raw/uploads", "target": "01_raw/uploads", "type": "directory", "timestamp": "2025-07-23T09:22:51.046286"}, {"source": "raw", "target": "01_raw", "type": "directory", "timestamp": "2025-07-23T09:22:51.243009"}, {"source": "annotations", "target": "02_processed/annotated", "type": "directory", "timestamp": "2025-07-23T09:22:51.262789"}, {"source": "structured", "target": "02_processed/structured", "type": "directory", "timestamp": "2025-07-23T09:22:51.364295"}, {"source": "cleaned", "target": "02_processed/cleaned", "type": "directory", "timestamp": "2025-07-23T09:22:51.375466"}, {"source": "generated", "target": "03_enhanced/generated", "type": "directory", "timestamp": "2025-07-23T09:22:51.408975"}, {"source": "massive_generated", "target": "03_enhanced/generated/advanced", "type": "directory", "timestamp": "2025-07-23T09:22:51.447486"}, {"source": "integrated", "target": "03_enhanced/integrated", "type": "directory", "timestamp": "2025-07-23T09:22:51.466485"}, {"source": "equipment", "target": "04_production/active/equipment", "type": "directory", "timestamp": "2025-07-23T09:22:51.490000"}, {"source": "processed", "target": "04_production/cached", "type": "directory", "timestamp": "2025-07-23T09:22:51.677561"}, {"source": "processed\\README.md", "target": "05_outputs\\reports\\README.md", "type": "file", "timestamp": "2025-07-23T09:22:51.681562"}, {"source": "processed\\vector_database_build_report.md", "target": "05_outputs\\reports\\vector_database_build_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.684081"}, {"source": "processed\\retrieval_system_test_report.md", "target": "05_outputs\\reports\\retrieval_system_test_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.687081"}, {"source": "processed\\final_system_validation_report.md", "target": "05_outputs\\reports\\final_system_validation_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.691084"}, {"source": "processed\\optimized_vector_db_build_report.md", "target": "05_outputs\\reports\\optimized_vector_db_build_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.694934"}, {"source": "processed\\advanced_retrieval_test_report.md", "target": "05_outputs\\reports\\advanced_retrieval_test_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.697946"}, {"source": "processed\\100_percent_completion_report.md", "target": "05_outputs\\reports\\100_percent_completion_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.701452"}, {"source": "processed\\rag_integration_report.md", "target": "05_outputs\\reports\\rag_integration_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.705457"}, {"source": "processed\\main_page_integration_report.md", "target": "05_outputs\\reports\\main_page_integration_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.708457"}, {"source": "processed\\integration_issues_fix_report.md", "target": "05_outputs\\reports\\integration_issues_fix_report.md", "type": "file", "timestamp": "2025-07-23T09:22:51.712458"}, {"source": "processed\\vector_database_test_report.json", "target": "05_outputs\\logs\\vector_database_test_report.json", "type": "file", "timestamp": "2025-07-23T09:22:51.715458"}, {"source": "processed\\detailed_test_results.json", "target": "05_outputs\\logs\\detailed_test_results.json", "type": "file", "timestamp": "2025-07-23T09:22:51.720108"}, {"source": "processed\\final_validation_data.json", "target": "05_outputs\\logs\\final_validation_data.json", "type": "file", "timestamp": "2025-07-23T09:22:51.723102"}, {"source": "processed\\advanced_test_detailed_data.json", "target": "05_outputs\\logs\\advanced_test_detailed_data.json", "type": "file", "timestamp": "2025-07-23T09:22:51.727102"}]}