"""
文本处理模块

负责文档解析、文本清洗、分块等功能
"""

import re
import os
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
import pandas as pd
from docx import Document
import PyPDF2
from loguru import logger


class TextProcessor:
    """文本处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.chunk_size = config.get("chunk_size", 1000)
        self.chunk_overlap = config.get("chunk_overlap", 200)
        self.supported_formats = config.get("supported_formats", {}).get("text", [])
        
    def process_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            处理后的文档块列表
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            # 根据文件扩展名选择处理方法
            extension = file_path.suffix.lower()
            
            if extension == ".pdf":
                content = self._extract_pdf(file_path)
            elif extension == ".docx":
                content = self._extract_docx(file_path)
            elif extension in [".txt", ".md"]:
                content = self._extract_text(file_path)
            elif extension in [".csv", ".xlsx"]:
                content = self._extract_structured(file_path)
            else:
                logger.warning(f"不支持的文件格式: {extension}")
                return []
                
            # 清洗文本
            cleaned_content = self._clean_text(content)
            
            # 分块处理
            chunks = self._split_text(cleaned_content)
            
            # 构建文档块
            documents = []
            for i, chunk in enumerate(chunks):
                doc = {
                    "content": chunk,
                    "source": str(file_path),
                    "chunk_id": i,
                    "metadata": {
                        "file_name": file_path.name,
                        "file_type": extension,
                        "chunk_index": i,
                        "total_chunks": len(chunks)
                    }
                }
                documents.append(doc)
                
            logger.info(f"成功处理文件 {file_path.name}, 生成 {len(documents)} 个文档块")
            return documents
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
            return []
    
    def _extract_pdf(self, file_path: Path) -> str:
        """提取PDF文本"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            logger.error(f"PDF提取失败: {str(e)}")
            return ""
    
    def _extract_docx(self, file_path: Path) -> str:
        """提取DOCX文本"""
        try:
            logger.info(f"开始提取DOCX文件: {file_path}")
            doc = Document(file_path)
            text = ""
            paragraph_count = 0

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():  # 只添加非空段落
                    text += paragraph.text + "\n"
                    paragraph_count += 1

            # 如果段落为空，尝试提取表格内容
            if not text.strip():
                logger.info("段落内容为空，尝试提取表格内容")
                for table in doc.tables:
                    for row in table.rows:
                        row_text = []
                        for cell in row.cells:
                            if cell.text.strip():
                                row_text.append(cell.text.strip())
                        if row_text:
                            text += " | ".join(row_text) + "\n"

            logger.info(f"DOCX提取完成: {paragraph_count} 个段落, 总长度: {len(text)} 字符")

            if not text.strip():
                logger.warning(f"DOCX文件内容为空: {file_path}")

            return text

        except Exception as e:
            logger.error(f"DOCX提取失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return ""
    
    def _extract_text(self, file_path: Path) -> str:
        """提取纯文本"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    return file.read()
            except Exception as e:
                logger.error(f"文本提取失败: {str(e)}")
                return ""
    
    def _extract_structured(self, file_path: Path) -> str:
        """提取结构化数据"""
        try:
            if file_path.suffix.lower() == ".csv":
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            # 将DataFrame转换为文本描述
            text = f"数据表: {file_path.name}\n"
            text += f"行数: {len(df)}, 列数: {len(df.columns)}\n"
            text += f"列名: {', '.join(df.columns.tolist())}\n\n"
            
            # 添加数据内容
            text += df.to_string(index=False)
            return text
            
        except Exception as e:
            logger.error(f"结构化数据提取失败: {str(e)}")
            return ""
    
    def _clean_text(self, text: str) -> str:
        """增强的文本清洗功能 - 专门针对电力设备故障诊断文档优化"""
        if not text:
            return ""

        # 1. 标准化换行符和空白字符
        text = re.sub(r'\r\n|\r', '\n', text)
        text = re.sub(r'[ \t]+', ' ', text)  # 合并空格和制表符
        text = re.sub(r'\n\s*\n', '\n\n', text)  # 标准化段落分隔

        # 2. 保留电力专业术语的特殊字符
        # 保留：中文、英文、数字、常用标点、电力单位符号、技术符号
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】\-_/\\°℃%‰μΩΩkVMVAkWMWVarMVarHzrpmΦΔΣ≤≥±×÷√∞∠∟⊥∥∝∈∉∪∩⊆⊇⊂⊃∅∀∃∧∨¬→↔≡≈≠<>≤≥]', '', text)

        # 3. 处理电力专业数据格式
        # 标准化电压等级表示
        text = re.sub(r'(\d+)\s*[kK][vV]', r'\1kV', text)
        text = re.sub(r'(\d+)\s*[mM][vV]', r'\1MV', text)

        # 标准化功率单位
        text = re.sub(r'(\d+)\s*[kK][wW]', r'\1kW', text)
        text = re.sub(r'(\d+)\s*[mM][wW]', r'\1MW', text)
        text = re.sub(r'(\d+)\s*[mM][vV][aA]', r'\1MVA', text)

        # 标准化频率和转速
        text = re.sub(r'(\d+)\s*[hH][zZ]', r'\1Hz', text)
        text = re.sub(r'(\d+)\s*[rR][pP][mM]', r'\1rpm', text)

        # 4. 移除过短或无意义的行，但保留重要的技术参数行
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            # 保留包含技术参数的短行
            if (len(line) > 3 or
                re.search(r'\d+[kKmM]?[VvWwAaΩΩ]', line) or  # 包含电气单位
                re.search(r'[0-9]+[.][0-9]+', line) or        # 包含小数
                re.search(r'[\u4e00-\u9fa5]{2,}', line)):     # 包含中文词汇
                cleaned_lines.append(line)

        # 5. 重新组合文本，保持段落结构
        result = '\n'.join(cleaned_lines)

        # 6. 最终清理：移除多余的空行
        result = re.sub(r'\n{3,}', '\n\n', result)

        return result.strip()

    def split_text(self, text: str) -> List[str]:
        """公开的文本分块方法"""
        return self._split_text(text)

    def _split_text(self, text: str) -> List[str]:
        """智能文本分块 - 基于语义和结构进行分割"""
        if not text:
            return []

        chunks = []

        # 1. 首先按段落分割
        paragraphs = text.split('\n\n')

        current_chunk = ""
        current_size = 0

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 2. 检查段落是否包含重要的结构标识
            is_section_header = self._is_section_header(paragraph)
            is_technical_data = self._is_technical_data(paragraph)

            paragraph_size = len(paragraph)

            # 3. 智能分块策略
            if current_size + paragraph_size <= self.chunk_size:
                # 可以添加到当前块
                if current_chunk:
                    current_chunk += '\n\n' + paragraph
                else:
                    current_chunk = paragraph
                current_size += paragraph_size + 2  # +2 for \n\n

            else:
                # 需要开始新块
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())

                # 处理超长段落
                if paragraph_size > self.chunk_size:
                    # 按句子进一步分割超长段落
                    sub_chunks = self._split_long_paragraph(paragraph)
                    chunks.extend(sub_chunks)
                    current_chunk = ""
                    current_size = 0
                else:
                    current_chunk = paragraph
                    current_size = paragraph_size

        # 添加最后一个块
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        # 4. 后处理：确保每个块都有足够的上下文
        enhanced_chunks = self._enhance_chunks_with_context(chunks)

        return enhanced_chunks

    def _is_section_header(self, text: str) -> bool:
        """判断是否为章节标题"""
        # 检查常见的标题模式
        patterns = [
            r'^[一二三四五六七八九十\d]+[、\.]\s*[\u4e00-\u9fa5]+',  # 中文编号标题
            r'^第[一二三四五六七八九十\d]+[章节条款]\s*[\u4e00-\u9fa5]+',  # 第X章/节
            r'^[\d]+\.[\d]*\s*[\u4e00-\u9fa5]+',  # 数字编号标题
            r'^[A-Z\d]+\.\s*[A-Za-z\u4e00-\u9fa5]+',  # 英文编号标题
        ]

        for pattern in patterns:
            if re.match(pattern, text.strip()):
                return True

        # 检查是否为短标题（通常标题较短且不包含句号）
        if len(text.strip()) < 50 and '。' not in text and '.' not in text[-5:]:
            return True

        return False

    def _is_technical_data(self, text: str) -> bool:
        """判断是否包含重要的技术数据"""
        # 检查是否包含电力技术参数
        technical_patterns = [
            r'\d+[kKmM]?[VvWwAaΩΩ]',  # 电气单位
            r'\d+[.]\d+[kKmM]?[VvWwAaΩΩ]',  # 带小数的电气单位
            r'\d+[°℃%‰]',  # 温度、百分比等
            r'额定|运行|故障|保护|动作|跳闸|合闸',  # 电力专业术语
            r'变压器|断路器|隔离开关|电流互感器|电压互感器',  # 设备名称
        ]

        for pattern in technical_patterns:
            if re.search(pattern, text):
                return True

        return False

    def _split_long_paragraph(self, paragraph: str) -> List[str]:
        """分割超长段落"""
        chunks = []

        # 按句子分割（中文句号、英文句号、感叹号、问号）
        sentences = re.split(r'[。！？.!?]\s*', paragraph)

        current_chunk = ""

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # 恢复句号
            if not sentence.endswith(('。', '！', '？', '.', '!', '?')):
                if re.search(r'[\u4e00-\u9fa5]', sentence):
                    sentence += '。'
                else:
                    sentence += '.'

            if len(current_chunk) + len(sentence) <= self.chunk_size:
                current_chunk += sentence
            else:
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                current_chunk = sentence

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _enhance_chunks_with_context(self, chunks: List[str]) -> List[str]:
        """为文本块添加上下文信息"""
        if len(chunks) <= 1:
            return chunks

        enhanced_chunks = []

        for i, chunk in enumerate(chunks):
            enhanced_chunk = chunk

            # 为非首块添加前文上下文（如果前一块是标题）
            if i > 0:
                prev_chunk = chunks[i-1]
                if self._is_section_header(prev_chunk) and len(prev_chunk) < 100:
                    enhanced_chunk = f"[上文：{prev_chunk}]\n\n{enhanced_chunk}"

            enhanced_chunks.append(enhanced_chunk)

        return enhanced_chunks

    def batch_process(self, directory: str) -> List[Dict[str, Any]]:
        """
        批量处理目录下的文件
        
        Args:
            directory: 目录路径
            
        Returns:
            所有文档块的列表
        """
        directory = Path(directory)
        if not directory.exists():
            logger.error(f"目录不存在: {directory}")
            return []
        
        all_documents = []
        
        # 遍历目录下的所有支持的文件
        for file_path in directory.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                documents = self.process_file(str(file_path))
                all_documents.extend(documents)
        
        logger.info(f"批量处理完成，共生成 {len(all_documents)} 个文档块")
        return all_documents

    def process_text(self, text: str, metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        处理纯文本内容（无文件）

        Args:
            text: 文本内容
            metadata: 文档元数据

        Returns:
            处理后的文档块列表
        """
        try:
            if not text:
                logger.warning("输入文本为空")
                return []

            # 分块处理文本
            chunks = self._split_text(text)

            # 构建文档块
            documents = []
            doc_id = metadata.get('doc_id', f"text_{int(time.time())}")
            title = metadata.get('title', "Text Document")

            for i, chunk in enumerate(chunks):
                # 构建文档块
                doc = {
                    'content': chunk,
                    'doc_id': f"{doc_id}_chunk_{i+1}",
                    'title': f"{title} - 第{i+1}部分",
                    'chunk_id': i,
                    'source': metadata.get('source', 'direct_input'),
                }

                # 添加元数据
                if metadata:
                    doc['metadata'] = metadata
                    # 复制重要字段到顶层
                    for key in ['equipment_type', 'voltage_level', 'fault_type', 'tags']:
                        if key in metadata:
                            doc[key] = metadata[key]

                documents.append(doc)

            logger.info(f"文本处理完成，共生成 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理文本内容时出错: {str(e)}")
            return []
