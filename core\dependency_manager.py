#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一依赖管理器
集中管理所有模块依赖检查，消除重复的依赖检查逻辑
提供生产环境友好的依赖管理和错误处理
"""

import importlib
import sys
import traceback
from typing import Dict, List, Optional, Tuple, Any, Union
from loguru import logger
from dataclasses import dataclass
from enum import Enum
from pathlib import Path


class DependencyLevel(Enum):
    """依赖级别"""
    CRITICAL = "critical"      # 关键依赖，缺失会导致系统无法启动
    IMPORTANT = "important"    # 重要依赖，缺失会影响主要功能
    OPTIONAL = "optional"      # 可选依赖，缺失不影响基础功能


@dataclass
class DependencyInfo:
    """依赖信息"""
    name: str
    import_name: str
    level: DependencyLevel
    description: str
    fallback: Optional[str] = None
    version_check: Optional[str] = None
    error_handler: Optional[callable] = None
    retry_count: int = 0
    max_retries: int = 3


class DependencyManager:
    """统一依赖管理器"""
    
    def __init__(self):
        self._dependencies = self._define_dependencies()
        self._check_results: Dict[str, bool] = {}
        self._loaded_modules: Dict[str, Any] = {}
        
    def _define_dependencies(self) -> Dict[str, DependencyInfo]:
        """定义所有系统依赖"""
        return {
            # 关键依赖
            "flask": DependencyInfo(
                name="Flask",
                import_name="flask",
                level=DependencyLevel.CRITICAL,
                description="Web框架"
            ),
            "loguru": DependencyInfo(
                name="Loguru",
                import_name="loguru",
                level=DependencyLevel.CRITICAL,
                description="日志系统"
            ),
            "requests": DependencyInfo(
                name="Requests",
                import_name="requests",
                level=DependencyLevel.CRITICAL,
                description="HTTP客户端"
            ),
            "pandas": DependencyInfo(
                name="Pandas",
                import_name="pandas",
                level=DependencyLevel.IMPORTANT,
                description="数据处理"
            ),
            "numpy": DependencyInfo(
                name="NumPy",
                import_name="numpy",
                level=DependencyLevel.IMPORTANT,
                description="数值计算"
            ),
            
            # 重要依赖
            "flask_cors": DependencyInfo(
                name="Flask-CORS",
                import_name="flask_cors",
                level=DependencyLevel.IMPORTANT,
                description="跨域支持"
            ),
            "flask_socketio": DependencyInfo(
                name="Flask-SocketIO",
                import_name="flask_socketio",
                level=DependencyLevel.IMPORTANT,
                description="WebSocket支持",
                fallback="基础HTTP通信"
            ),
            "fastapi": DependencyInfo(
                name="FastAPI",
                import_name="fastapi",
                level=DependencyLevel.IMPORTANT,
                description="API框架",
                fallback="仅Flask服务"
            ),
            "uvicorn": DependencyInfo(
                name="Uvicorn",
                import_name="uvicorn",
                level=DependencyLevel.IMPORTANT,
                description="ASGI服务器",
                fallback="仅Flask服务"
            ),
            
            # 可选依赖
            "langchain": DependencyInfo(
                name="LangChain",
                import_name="langchain",
                level=DependencyLevel.OPTIONAL,
                description="LLM框架",
                fallback="基础AI功能"
            ),
            "sentence_transformers": DependencyInfo(
                name="Sentence Transformers",
                import_name="sentence_transformers",
                level=DependencyLevel.OPTIONAL,
                description="文本嵌入",
                fallback="基础文本处理"
            ),
            "chromadb": DependencyInfo(
                name="ChromaDB",
                import_name="chromadb",
                level=DependencyLevel.OPTIONAL,
                description="向量数据库",
                fallback="内存存储"
            ),
            "opencv": DependencyInfo(
                name="OpenCV",
                import_name="cv2",
                level=DependencyLevel.OPTIONAL,
                description="图像处理",
                fallback="基础图像功能"
            ),
            "paddleocr": DependencyInfo(
                name="PaddleOCR",
                import_name="paddleocr",
                level=DependencyLevel.OPTIONAL,
                description="OCR识别",
                fallback="无OCR功能"
            )
        }
    
    def check_dependency(self, name: str) -> bool:
        """检查单个依赖 - 增强错误处理和重试机制"""
        if name not in self._dependencies:
            logger.warning(f"未知依赖: {name}")
            return False

        if name in self._check_results:
            return self._check_results[name]

        dep_info = self._dependencies[name]

        # 重试机制
        for attempt in range(dep_info.max_retries + 1):
            try:
                module = importlib.import_module(dep_info.import_name)

                # 版本检查
                if dep_info.version_check:
                    self._check_version(module, dep_info)

                self._loaded_modules[name] = module
                self._check_results[name] = True
                logger.debug(f"✅ {dep_info.name} 可用")
                return True

            except ImportError as e:
                if attempt < dep_info.max_retries:
                    logger.debug(f"重试加载 {dep_info.name} (尝试 {attempt + 1}/{dep_info.max_retries + 1})")
                    continue

                # 最终失败处理
                self._handle_dependency_failure(dep_info, e)
                return False

            except Exception as e:
                logger.error(f"加载 {dep_info.name} 时发生意外错误: {e}")
                logger.debug(f"错误详情: {traceback.format_exc()}")
                self._check_results[name] = False
                return False

        return False

    def _check_version(self, module: Any, dep_info: DependencyInfo) -> None:
        """检查模块版本"""
        try:
            if hasattr(module, '__version__'):
                version = module.__version__
                logger.debug(f"{dep_info.name} 版本: {version}")
        except Exception as e:
            logger.debug(f"无法获取 {dep_info.name} 版本信息: {e}")

    def _handle_dependency_failure(self, dep_info: DependencyInfo, error: Exception) -> None:
        """处理依赖加载失败"""
        self._check_results[dep_info.import_name] = False

        level_symbol = "❌" if dep_info.level == DependencyLevel.CRITICAL else "⚠️"
        logger.warning(f"{level_symbol} {dep_info.name} 不可用: {error}")

        # 调用自定义错误处理器
        if dep_info.error_handler:
            try:
                dep_info.error_handler(dep_info, error)
            except Exception as handler_error:
                logger.error(f"依赖错误处理器执行失败: {handler_error}")

        # 显示回退方案
        if dep_info.fallback:
            logger.info(f"   回退方案: {dep_info.fallback}")

        # 关键依赖失败时的特殊处理
        if dep_info.level == DependencyLevel.CRITICAL:
            logger.error(f"关键依赖 {dep_info.name} 加载失败，系统可能无法正常运行")
            self._suggest_installation(dep_info)
    
    def check_all_dependencies(self) -> Tuple[bool, Dict[str, Any]]:
        """检查所有依赖"""
        logger.info("🔍 开始依赖检查...")
        
        results = {
            "critical": {"available": [], "missing": []},
            "important": {"available": [], "missing": []},
            "optional": {"available": [], "missing": []}
        }
        
        critical_missing = False
        
        for name, dep_info in self._dependencies.items():
            is_available = self.check_dependency(name)
            
            level_key = dep_info.level.value
            if is_available:
                results[level_key]["available"].append(dep_info.name)
            else:
                results[level_key]["missing"].append(dep_info.name)
                if dep_info.level == DependencyLevel.CRITICAL:
                    critical_missing = True
        
        # 输出检查结果
        self._print_dependency_report(results)
        
        return not critical_missing, results
    
    def _suggest_installation(self, dep_info: DependencyInfo) -> None:
        """建议安装方法"""
        install_commands = {
            "flask": "pip install flask",
            "fastapi": "pip install fastapi uvicorn",
            "langchain": "pip install langchain",
            "chromadb": "pip install chromadb",
            "sentence_transformers": "pip install sentence-transformers",
            "paddleocr": "pip install paddleocr",
            "opencv": "pip install opencv-python",
            "pandas": "pip install pandas",
            "numpy": "pip install numpy",
            "requests": "pip install requests"
        }

        if dep_info.import_name in install_commands:
            logger.info(f"   安装建议: {install_commands[dep_info.import_name]}")

    def _print_dependency_report(self, results: Dict[str, Any]) -> None:
        """打印依赖检查报告 - 使用logger而非print"""
        logger.info("\n" + "="*60)
        logger.info("📋 依赖检查报告")
        logger.info("="*60)

        for level in ["critical", "important", "optional"]:
            level_name = {"critical": "关键", "important": "重要", "optional": "可选"}[level]
            available = results[level]["available"]
            missing = results[level]["missing"]

            logger.info(f"\n🔧 {level_name}依赖:")
            if available:
                logger.info(f"  ✅ 可用: {', '.join(available)}")
            if missing:
                symbol = "❌" if level == "critical" else "⚠️"
                logger.warning(f"  {symbol} 缺失: {', '.join(missing)}")

        logger.info("\n" + "="*60)
    
    def get_module(self, name: str) -> Optional[Any]:
        """获取已加载的模块"""
        return self._loaded_modules.get(name)
    
    def is_available(self, name: str) -> bool:
        """检查依赖是否可用"""
        if name not in self._check_results:
            return self.check_dependency(name)
        return self._check_results[name]
    
    def get_availability_status(self) -> Dict[str, bool]:
        """获取所有依赖的可用性状态"""
        return self._check_results.copy()
    
    def get_feature_availability(self) -> Dict[str, bool]:
        """获取功能可用性状态"""
        return {
            "web_interface": self.is_available("flask"),
            "api_service": self.is_available("fastapi") and self.is_available("uvicorn"),
            "websocket": self.is_available("flask_socketio"),
            "ai_analysis": self.is_available("langchain"),
            "vector_search": self.is_available("sentence_transformers"),
            "vector_database": self.is_available("chromadb"),
            "image_processing": self.is_available("opencv"),
            "ocr_recognition": self.is_available("paddleocr"),
            "data_processing": self.is_available("pandas") and self.is_available("numpy")
        }


# 全局依赖管理器实例
_dependency_manager = None


def get_dependency_manager() -> DependencyManager:
    """获取全局依赖管理器实例"""
    global _dependency_manager
    if _dependency_manager is None:
        _dependency_manager = DependencyManager()
    return _dependency_manager


def check_dependencies() -> Tuple[bool, Dict[str, Any]]:
    """检查所有依赖的便捷函数"""
    return get_dependency_manager().check_all_dependencies()


def is_dependency_available(name: str) -> bool:
    """检查依赖是否可用的便捷函数"""
    return get_dependency_manager().is_available(name)


def get_module(name: str) -> Optional[Any]:
    """获取模块的便捷函数"""
    return get_dependency_manager().get_module(name)


def get_feature_availability() -> Dict[str, bool]:
    """获取功能可用性的便捷函数"""
    return get_dependency_manager().get_feature_availability()
