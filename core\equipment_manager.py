"""
设备信息管理器

管理变电站设备信息、状态监控和历史记录
生产环境优化版本，支持缓存、性能监控、批量操作
"""

import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from loguru import logger

# 尝试导入缓存
try:
    from utils.optimized_cache import search_cache
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False
    logger.warning("缓存模块不可用，设备查询将不使用缓存")


class EquipmentManager:
    """设备信息管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.equipment_config = config.get("equipment", {})
        
        # 设备数据存储路径
        self.data_dir = self.equipment_config.get("data_dir", "data/04_production/active/equipment")
        self.equipment_db_path = os.path.join(self.data_dir, "equipment_database.json")
        self.status_log_path = os.path.join(self.data_dir, "status_logs")
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.status_log_path, exist_ok=True)
        
        # 设备数据库
        self.equipment_db = {}
        self.load_equipment_database()
    
    def load_equipment_database(self):
        """加载设备数据库"""
        try:
            if os.path.exists(self.equipment_db_path):
                with open(self.equipment_db_path, 'r', encoding='utf-8') as f:
                    self.equipment_db = json.load(f)
                logger.info(f"加载了 {len(self.equipment_db)} 个设备信息")
            else:
                # 创建默认设备数据库
                self._create_default_equipment_db()
                
        except Exception as e:
            logger.error(f"加载设备数据库失败: {str(e)}")
            self.equipment_db = {}
    
    def _create_default_equipment_db(self):
        """创建默认设备数据库"""
        try:
            default_equipment = {
                "T001": {
                    "id": "T001",
                    "name": "主变压器1号",
                    "type": "transformer",
                    "voltage_level": "220kV/110kV/10kV",
                    "capacity": "180MVA",
                    "manufacturer": "特变电工",
                    "install_date": "2020-01-15",
                    "location": "主变区域",
                    "status": "运行",
                    "last_maintenance": "2024-06-01",
                    "next_maintenance": "2024-12-01",
                    "specifications": {
                        "rated_voltage": "220kV",
                        "rated_current": "472A",
                        "impedance": "12.5%",
                        "cooling_type": "ONAN/ONAF"
                    }
                },
                "CB001": {
                    "id": "CB001", 
                    "name": "220kV进线断路器",
                    "type": "circuit_breaker",
                    "voltage_level": "220kV",
                    "manufacturer": "平高电气",
                    "install_date": "2020-02-10",
                    "location": "220kV配电装置",
                    "status": "运行",
                    "last_maintenance": "2024-05-15",
                    "next_maintenance": "2024-11-15",
                    "specifications": {
                        "rated_voltage": "220kV",
                        "rated_current": "2000A",
                        "breaking_current": "40kA",
                        "operating_mechanism": "弹簧机构"
                    }
                },
                "DS001": {
                    "id": "DS001",
                    "name": "220kV进线隔离开关",
                    "type": "disconnector",
                    "voltage_level": "220kV", 
                    "manufacturer": "西电集团",
                    "install_date": "2020-02-12",
                    "location": "220kV配电装置",
                    "status": "运行",
                    "last_maintenance": "2024-04-20",
                    "next_maintenance": "2024-10-20",
                    "specifications": {
                        "rated_voltage": "220kV",
                        "rated_current": "2000A",
                        "operating_mechanism": "电动机构"
                    }
                },
                "PT001": {
                    "id": "PT001",
                    "name": "220kV电压互感器",
                    "type": "voltage_transformer",
                    "voltage_level": "220kV",
                    "manufacturer": "大连北方",
                    "install_date": "2020-01-20",
                    "location": "220kV配电装置",
                    "status": "运行",
                    "last_maintenance": "2024-03-10",
                    "next_maintenance": "2024-09-10",
                    "specifications": {
                        "primary_voltage": "220kV",
                        "secondary_voltage": "100V",
                        "accuracy_class": "0.2",
                        "insulation_type": "油浸式"
                    }
                },
                "CT001": {
                    "id": "CT001",
                    "name": "220kV电流互感器",
                    "type": "current_transformer", 
                    "voltage_level": "220kV",
                    "manufacturer": "大连北方",
                    "install_date": "2020-01-22",
                    "location": "220kV配电装置",
                    "status": "运行",
                    "last_maintenance": "2024-03-12",
                    "next_maintenance": "2024-09-12",
                    "specifications": {
                        "primary_current": "1000A",
                        "secondary_current": "5A",
                        "accuracy_class": "0.2S",
                        "insulation_type": "油浸式"
                    }
                }
            }
            
            self.equipment_db = default_equipment
            self.save_equipment_database()
            logger.info("创建了默认设备数据库")
            
        except Exception as e:
            logger.error(f"创建默认设备数据库失败: {str(e)}")
    
    def save_equipment_database(self):
        """保存设备数据库"""
        try:
            with open(self.equipment_db_path, 'w', encoding='utf-8') as f:
                json.dump(self.equipment_db, f, ensure_ascii=False, indent=2)
            logger.info("设备数据库已保存")
            
        except Exception as e:
            logger.error(f"保存设备数据库失败: {str(e)}")
    
    def get_equipment_info(self, equipment_id: str) -> Optional[Dict[str, Any]]:
        """
        获取设备信息
        
        Args:
            equipment_id: 设备ID
            
        Returns:
            设备信息
        """
        return self.equipment_db.get(equipment_id)
    
    def search_equipment(self, query: str) -> List[Dict[str, Any]]:
        """
        搜索设备
        
        Args:
            query: 搜索关键词
            
        Returns:
            匹配的设备列表
        """
        try:
            results = []
            query_lower = query.lower()
            
            for equipment_id, equipment_info in self.equipment_db.items():
                # 搜索设备ID、名称、类型
                if (query_lower in equipment_id.lower() or 
                    query_lower in equipment_info.get("name", "").lower() or
                    query_lower in equipment_info.get("type", "").lower() or
                    query_lower in equipment_info.get("location", "").lower()):
                    
                    results.append(equipment_info)
            
            return results
            
        except Exception as e:
            logger.error(f"搜索设备失败: {str(e)}")
            return []
    
    def get_equipment_by_type(self, equipment_type: str) -> List[Dict[str, Any]]:
        """
        按类型获取设备
        
        Args:
            equipment_type: 设备类型
            
        Returns:
            设备列表
        """
        try:
            results = []
            
            for equipment_info in self.equipment_db.values():
                if equipment_info.get("type") == equipment_type:
                    results.append(equipment_info)
            
            return results
            
        except Exception as e:
            logger.error(f"按类型获取设备失败: {str(e)}")
            return []
    
    def get_equipment_by_voltage_level(self, voltage_level: str) -> List[Dict[str, Any]]:
        """
        按电压等级获取设备
        
        Args:
            voltage_level: 电压等级
            
        Returns:
            设备列表
        """
        try:
            results = []
            
            for equipment_info in self.equipment_db.values():
                if voltage_level in equipment_info.get("voltage_level", ""):
                    results.append(equipment_info)
            
            return results
            
        except Exception as e:
            logger.error(f"按电压等级获取设备失败: {str(e)}")
            return []
    
    def update_equipment_status(self, equipment_id: str, status: str, 
                              notes: Optional[str] = None) -> bool:
        """
        更新设备状态
        
        Args:
            equipment_id: 设备ID
            status: 新状态
            notes: 备注
            
        Returns:
            更新是否成功
        """
        try:
            if equipment_id not in self.equipment_db:
                logger.error(f"设备不存在: {equipment_id}")
                return False
            
            old_status = self.equipment_db[equipment_id].get("status", "unknown")
            
            # 更新状态
            self.equipment_db[equipment_id]["status"] = status
            self.equipment_db[equipment_id]["last_status_update"] = datetime.now().isoformat()
            
            # 记录状态变更日志
            self._log_status_change(equipment_id, old_status, status, notes)
            
            # 保存数据库
            self.save_equipment_database()
            
            logger.info(f"设备状态已更新: {equipment_id} {old_status} -> {status}")
            return True
            
        except Exception as e:
            logger.error(f"更新设备状态失败: {str(e)}")
            return False
    
    def _log_status_change(self, equipment_id: str, old_status: str, 
                          new_status: str, notes: Optional[str] = None):
        """记录状态变更日志"""
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "equipment_id": equipment_id,
                "old_status": old_status,
                "new_status": new_status,
                "notes": notes or ""
            }
            
            # 按日期创建日志文件
            log_date = datetime.now().strftime("%Y%m%d")
            log_file = os.path.join(self.status_log_path, f"status_log_{log_date}.json")
            
            # 读取现有日志
            logs = []
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            
            # 添加新日志
            logs.append(log_entry)
            
            # 保存日志
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"记录状态变更日志失败: {str(e)}")
    
    def get_equipment_status_history(self, equipment_id: str, 
                                   days: int = 30) -> List[Dict[str, Any]]:
        """
        获取设备状态历史
        
        Args:
            equipment_id: 设备ID
            days: 查询天数
            
        Returns:
            状态历史记录
        """
        try:
            history = []
            
            # 计算查询日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 遍历日期范围内的日志文件
            current_date = start_date
            while current_date <= end_date:
                log_date = current_date.strftime("%Y%m%d")
                log_file = os.path.join(self.status_log_path, f"status_log_{log_date}.json")
                
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        logs = json.load(f)
                    
                    # 筛选指定设备的记录
                    for log in logs:
                        if log.get("equipment_id") == equipment_id:
                            history.append(log)
                
                current_date += timedelta(days=1)
            
            # 按时间排序
            history.sort(key=lambda x: x.get("timestamp", ""))
            
            return history
            
        except Exception as e:
            logger.error(f"获取设备状态历史失败: {str(e)}")
            return []
    
    def get_maintenance_schedule(self, days_ahead: int = 30) -> List[Dict[str, Any]]:
        """
        获取维护计划
        
        Args:
            days_ahead: 提前天数
            
        Returns:
            维护计划列表
        """
        try:
            maintenance_schedule = []
            cutoff_date = datetime.now() + timedelta(days=days_ahead)
            
            for equipment_info in self.equipment_db.values():
                next_maintenance = equipment_info.get("next_maintenance")
                if next_maintenance:
                    try:
                        maintenance_date = datetime.strptime(next_maintenance, "%Y-%m-%d")
                        if maintenance_date <= cutoff_date:
                            schedule_item = {
                                "equipment_id": equipment_info.get("id"),
                                "equipment_name": equipment_info.get("name"),
                                "maintenance_date": next_maintenance,
                                "days_until": (maintenance_date - datetime.now()).days,
                                "type": equipment_info.get("type"),
                                "location": equipment_info.get("location"),
                                "last_maintenance": equipment_info.get("last_maintenance")
                            }
                            maintenance_schedule.append(schedule_item)
                    except ValueError:
                        logger.warning(f"无效的维护日期格式: {next_maintenance}")
            
            # 按维护日期排序
            maintenance_schedule.sort(key=lambda x: x.get("maintenance_date", ""))
            
            return maintenance_schedule
            
        except Exception as e:
            logger.error(f"获取维护计划失败: {str(e)}")
            return []
    
    def add_equipment(self, equipment_info: Dict[str, Any]) -> bool:
        """
        添加设备

        Args:
            equipment_info: 设备信息

        Returns:
            添加是否成功
        """
        try:
            equipment_id = equipment_info.get("id")
            if not equipment_id:
                logger.error("设备ID不能为空")
                return False

            if equipment_id in self.equipment_db:
                logger.error(f"设备ID已存在: {equipment_id}")
                return False

            # 添加创建时间
            equipment_info["created_at"] = datetime.now().isoformat()

            # 添加到数据库
            self.equipment_db[equipment_id] = equipment_info

            # 保存数据库
            self.save_equipment_database()

            logger.info(f"设备已添加: {equipment_id}")
            return True

        except Exception as e:
            logger.error(f"添加设备失败: {str(e)}")
            return False
    
    def update_equipment(self, equipment_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新设备信息

        Args:
            equipment_id: 设备ID
            updates: 更新内容

        Returns:
            更新是否成功
        """
        try:
            if equipment_id not in self.equipment_db:
                logger.error(f"设备不存在: {equipment_id}")
                return False

            # 更新信息
            self.equipment_db[equipment_id].update(updates)
            self.equipment_db[equipment_id]["updated_at"] = datetime.now().isoformat()

            # 保存数据库
            self.save_equipment_database()

            logger.info(f"设备信息已更新: {equipment_id}")
            return True

        except Exception as e:
            logger.error(f"更新设备信息失败: {str(e)}")
            return False
    


    def get_equipment_statistics(self) -> Dict[str, Any]:
        """
        获取设备统计信息

        Returns:
            统计信息
        """
        try:
            stats = {
                "total_equipment": len(self.equipment_db),
                "by_type": {},
                "by_status": {},
                "by_voltage_level": {},
                "maintenance_due": 0
            }

            # 按类型统计
            for equipment_info in self.equipment_db.values():
                equipment_type = equipment_info.get("type", "unknown")
                stats["by_type"][equipment_type] = stats["by_type"].get(equipment_type, 0) + 1

                # 按状态统计
                status = equipment_info.get("status", "unknown")
                stats["by_status"][status] = stats["by_status"].get(status, 0) + 1

                # 按电压等级统计
                voltage_level = equipment_info.get("voltage_level", "unknown")
                stats["by_voltage_level"][voltage_level] = stats["by_voltage_level"].get(voltage_level, 0) + 1

                # 维护到期统计
                next_maintenance = equipment_info.get("next_maintenance")
                if next_maintenance:
                    try:
                        maintenance_date = datetime.strptime(next_maintenance, "%Y-%m-%d")
                        if maintenance_date <= datetime.now():
                            stats["maintenance_due"] += 1
                    except ValueError:
                        pass

            return stats

        except Exception as e:
            logger.error(f"获取设备统计信息失败: {str(e)}")
            return {}


