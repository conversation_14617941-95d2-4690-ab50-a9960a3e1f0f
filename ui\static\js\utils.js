// 工具函数模块 - 故障分析智能助手

// ==================== 统一状态管理 ====================
class AppStateManager {
    constructor() {
        this.state = {
            currentTab: 'fault-analysis',
            analysisHistory: [],
            equipmentList: [],
            isAnalyzing: false,
            streamingActive: false,
            errorStats: {
                streamingErrors: 0,
                apiErrors: 0,
                uiErrors: 0
            },
            ui: {
                modals: new Map(),
                activeStreams: new Set(),
                loadingStates: new Map()
            }
        };
        this.listeners = new Map();
        this.init();
    }

    init() {
        // 从本地存储恢复状态
        this.loadFromStorage();

        // 设置自动保存
        setInterval(() => this.saveToStorage(), 30000);

        // 页面卸载时保存状态
        window.addEventListener('beforeunload', () => this.saveToStorage());
    }

    // 获取状态
    getState(path = null) {
        if (!path) return this.state;
        return path.split('.').reduce((obj, key) => obj?.[key], this.state);
    }

    // 更新状态
    setState(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, key) => {
            if (!obj[key]) obj[key] = {};
            return obj[key];
        }, this.state);

        const oldValue = target[lastKey];
        target[lastKey] = value;

        // 触发监听器
        this.notifyListeners(path, value, oldValue);
    }

    // 添加监听器
    subscribe(path, callback) {
        if (!this.listeners.has(path)) {
            this.listeners.set(path, new Set());
        }
        this.listeners.get(path).add(callback);

        // 返回取消订阅函数
        return () => {
            const pathListeners = this.listeners.get(path);
            if (pathListeners) {
                pathListeners.delete(callback);
            }
        };
    }

    // 通知监听器
    notifyListeners(path, newValue, oldValue) {
        const pathListeners = this.listeners.get(path);
        if (pathListeners) {
            pathListeners.forEach(callback => {
                try {
                    callback(newValue, oldValue, path);
                } catch (error) {
                    console.error('状态监听器执行失败:', error);
                }
            });
        }
    }

    // 保存到本地存储
    saveToStorage() {
        try {
            const saveData = {
                currentTab: this.state.currentTab,
                analysisHistory: this.state.analysisHistory.slice(-50), // 只保存最近50条
                equipmentList: this.state.equipmentList,
                errorStats: this.state.errorStats
            };
            localStorage.setItem('appState', JSON.stringify(saveData));
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }

    // 从本地存储加载
    loadFromStorage() {
        try {
            const saved = localStorage.getItem('appState');
            if (saved) {
                const data = JSON.parse(saved);
                Object.assign(this.state, data);
            }
        } catch (error) {
            console.error('加载状态失败:', error);
        }
    }

    // 重置状态
    reset() {
        this.state = {
            currentTab: 'fault-analysis',
            analysisHistory: [],
            equipmentList: [],
            isAnalyzing: false,
            streamingActive: false,
            errorStats: { streamingErrors: 0, apiErrors: 0, uiErrors: 0 },
            ui: {
                modals: new Map(),
                activeStreams: new Set(),
                loadingStates: new Map()
            }
        };
        localStorage.removeItem('appState');
    }
}

// 全局状态管理器实例
window.appState = new AppStateManager();

// ==================== 统一错误处理 ====================
class ErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.maxErrors = 100;
        this.init();
    }

    init() {
        // 全局错误捕获
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
        });

        // Promise错误捕获
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || String(event.reason),
                stack: event.reason?.stack
            });
        });
    }

    handleError(error) {
        // 记录错误
        this.logError(error);

        // 更新错误统计
        const errorType = this.categorizeError(error);
        const currentStats = window.appState.getState('errorStats');
        currentStats[errorType] = (currentStats[errorType] || 0) + 1;
        window.appState.setState('errorStats', currentStats);

        // 显示用户友好的错误信息
        this.showUserError(error);
    }

    logError(error) {
        console.error('应用错误:', error);

        // 添加到错误队列
        this.errorQueue.push({
            ...error,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        });

        // 保持队列大小
        if (this.errorQueue.length > this.maxErrors) {
            this.errorQueue.shift();
        }
    }

    categorizeError(error) {
        if (error.message?.includes('fetch') || error.message?.includes('network')) {
            return 'apiErrors';
        }
        if (error.message?.includes('stream') || error.message?.includes('EventSource')) {
            return 'streamingErrors';
        }
        return 'uiErrors';
    }

    showUserError(error) {
        const userMessage = this.getUserFriendlyMessage(error);
        showAlert(userMessage, 'warning', 8000);
    }

    getUserFriendlyMessage(error) {
        if (error.message?.includes('fetch')) {
            return '网络连接异常，请检查网络后重试';
        }
        if (error.message?.includes('bootstrap')) {
            return '界面组件加载异常，页面功能可能受影响';
        }
        if (error.message?.includes('stream')) {
            return '实时分析连接中断，请重新开始分析';
        }
        return '系统遇到异常，请刷新页面后重试';
    }

    getErrorReport() {
        return {
            stats: window.appState.getState('errorStats'),
            recentErrors: this.errorQueue.slice(-10)
        };
    }
}

// 全局错误处理器实例
window.errorHandler = new ErrorHandler();

// ==================== 统一DOM操作管理 ====================
class DOMManager {
    constructor() {
        this.elementCache = new Map();
        this.observers = new Map();
        this.init();
    }

    init() {
        // 设置MutationObserver监听DOM变化
        this.setupMutationObserver();

        // 清理缓存的定时器
        setInterval(() => this.cleanCache(), 60000);
    }

    setupMutationObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 清理已删除元素的缓存
                    mutation.removedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.clearElementFromCache(node);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 安全获取元素
    getElement(selector, useCache = true) {
        if (useCache && this.elementCache.has(selector)) {
            const cached = this.elementCache.get(selector);
            // 检查元素是否仍在DOM中
            if (document.contains(cached.element)) {
                return cached.element;
            } else {
                this.elementCache.delete(selector);
            }
        }

        const element = document.querySelector(selector);
        if (element && useCache) {
            this.elementCache.set(selector, {
                element,
                timestamp: Date.now()
            });
        }
        return element;
    }

    // 安全获取多个元素
    getElements(selector) {
        return Array.from(document.querySelectorAll(selector));
    }

    // 安全设置内容
    setContent(selector, content, sanitize = true) {
        const element = this.getElement(selector);
        if (!element) {
            console.warn(`元素不存在: ${selector}`);
            return false;
        }

        try {
            if (sanitize && typeof content === 'string') {
                content = this.sanitizeHTML(content);
            }
            element.innerHTML = content;
            return true;
        } catch (error) {
            console.error('设置内容失败:', error);
            return false;
        }
    }

    // 安全添加内容
    appendContent(selector, content, sanitize = true) {
        const element = this.getElement(selector);
        if (!element) {
            console.warn(`元素不存在: ${selector}`);
            return false;
        }

        try {
            if (sanitize && typeof content === 'string') {
                content = this.sanitizeHTML(content);
            }
            element.insertAdjacentHTML('beforeend', content);
            return true;
        } catch (error) {
            console.error('添加内容失败:', error);
            return false;
        }
    }

    // 安全显示/隐藏元素
    toggleVisibility(selector, show = null) {
        const element = this.getElement(selector);
        if (!element) {
            console.warn(`元素不存在: ${selector}`);
            return false;
        }

        if (show === null) {
            show = element.style.display === 'none';
        }

        element.style.display = show ? '' : 'none';
        return true;
    }

    // 安全添加/移除类
    toggleClass(selector, className, add = null) {
        const element = this.getElement(selector);
        if (!element) {
            console.warn(`元素不存在: ${selector}`);
            return false;
        }

        if (add === null) {
            element.classList.toggle(className);
        } else if (add) {
            element.classList.add(className);
        } else {
            element.classList.remove(className);
        }
        return true;
    }

    // 安全事件绑定
    addEventListener(selector, event, handler, options = {}) {
        const element = this.getElement(selector);
        if (!element) {
            console.warn(`元素不存在: ${selector}`);
            return null;
        }

        const wrappedHandler = (e) => {
            try {
                handler(e);
            } catch (error) {
                console.error('事件处理器执行失败:', error);
                window.errorHandler.handleError({
                    type: 'event',
                    message: error.message,
                    stack: error.stack,
                    selector,
                    event
                });
            }
        };

        element.addEventListener(event, wrappedHandler, options);

        // 返回清理函数
        return () => element.removeEventListener(event, wrappedHandler, options);
    }

    // HTML内容清理
    sanitizeHTML(html) {
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }

    // 清理缓存
    cleanCache() {
        const now = Date.now();
        const maxAge = 300000; // 5分钟

        for (const [selector, cached] of this.elementCache.entries()) {
            if (now - cached.timestamp > maxAge || !document.contains(cached.element)) {
                this.elementCache.delete(selector);
            }
        }
    }

    // 从缓存中清理元素
    clearElementFromCache(element) {
        for (const [selector, cached] of this.elementCache.entries()) {
            if (cached.element === element || cached.element.contains(element)) {
                this.elementCache.delete(selector);
            }
        }
    }

    // 等待元素出现
    waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = this.getElement(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = this.getElement(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素未找到: ${selector}`));
            }, timeout);
        });
    }

    // 批量操作
    batch(operations) {
        const results = [];
        operations.forEach(op => {
            try {
                const result = this[op.method](...op.args);
                results.push({ success: true, result });
            } catch (error) {
                results.push({ success: false, error: error.message });
            }
        });
        return results;
    }
}

// 全局DOM管理器实例
window.domManager = new DOMManager();

// ==================== 流式分析处理管理 ====================
class StreamingManager {
    constructor() {
        this.activeStreams = new Map();
        this.streamHistory = [];
        this.maxHistory = 50;
        this.retryConfig = {
            maxRetries: 3,
            baseDelay: 1000,
            maxDelay: 10000
        };
    }

    // 开始流式分析
    async startStreaming(query, options = {}) {
        const streamId = this.generateStreamId();
        const config = {
            query,
            thinking_mode: options.thinkingMode || false,
            retryCount: 0,
            startTime: Date.now(),
            ...options
        };

        try {
            // 设置目标容器
            this.currentTargetContainer = options.targetContainer;

            // 更新状态
            window.appState.setState('streamingActive', true);
            window.appState.setState('isAnalyzing', true);

            // 记录流开始
            this.activeStreams.set(streamId, config);

            // 准备UI元素
            await this.prepareStreamingUI(config);

            // 开始实际的流式处理
            const result = await this.performStreaming(streamId, config);

            // 记录成功
            this.recordStreamResult(streamId, 'success', result);

            return result;

        } catch (error) {
            console.error('流式分析失败:', error);

            // 记录失败
            this.recordStreamResult(streamId, 'error', error);

            // 尝试重试
            if (config.retryCount < this.retryConfig.maxRetries) {
                return this.retryStreaming(streamId, config, error);
            }

            throw error;
        } finally {
            // 清理状态
            this.activeStreams.delete(streamId);
            window.appState.setState('streamingActive', false);
            window.appState.setState('isAnalyzing', false);
        }
    }

    // 执行流式处理
    async performStreaming(streamId, config) {
        console.log('🚀 开始流式处理:', {
            streamId,
            query: config.query,
            thinking_mode: config.thinking_mode
        });

        const response = await fetch('/api/v1/analyze_stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: config.query,
                thinking_mode: config.thinking_mode
            })
        });

        console.log('📡 API响应状态:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return this.processStreamResponse(streamId, response, config);
    }

    // 处理流式响应
    async processStreamResponse(streamId, response, config) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        let buffer = '';
        const state = {
            reasoningContent: '',
            finalContent: '',
            isInThinking: false,
            isInAnswer: false
        };

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                // 检查流是否被取消
                if (!this.activeStreams.has(streamId)) {
                    break;
                }

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.trim() === '') continue;

                    try {
                        await this.processStreamLine(streamId, line, config, state);
                    } catch (error) {
                        console.error('处理流式数据行失败:', error);
                    }
                }
            }

            return {
                reasoning: state.reasoningContent,
                final: state.finalContent,
                duration: Date.now() - config.startTime
            };

        } finally {
            reader.releaseLock();
        }
    }

    // 处理单行流式数据
    async processStreamLine(streamId, line, config, state) {
        if (!line.startsWith('data: ')) return;

        const dataStr = line.substring(6);
        if (dataStr === '[DONE]') {
            console.log('🏁 流式处理完成');
            return;
        }

        try {
            const data = JSON.parse(dataStr);
            console.log('📦 收到流式数据:', data.type, data.content?.substring(0, 50) + '...');

            // 处理思考过程数据（DeepSeek-R1）
            if ((data.type === 'thinking' || data.type === 'reasoning_chunk') && config.thinking_mode) {
                state.isInThinking = true;
                state.reasoningContent += data.content || '';
                this.updateThinkingDisplay(data.content || '');
            }
            // 处理最终答案数据
            else if (data.type === 'answer' || data.type === 'answer_chunk') {
                console.log('📝 收到最终答案数据:', data.content?.substring(0, 100) + '...');
                state.isInAnswer = true;
                state.finalContent += data.content || '';
                this.updateFinalDisplay(data.content || '', config.thinking_mode);
            }
            // 处理内容数据（DeepSeek-V3）
            else if (data.type === 'content' || data.type === 'content_chunk') {
                state.isInAnswer = true;
                state.finalContent += data.content || '';
                this.updateFinalDisplay(data.content || '', config.thinking_mode);
            }
            // 处理表格数据
            else if (data.type === 'table_summary') {
                state.finalContent += '\n\n' + (data.content || '');
                this.updateFinalDisplay('\n\n' + (data.content || ''), config.thinking_mode);
            }
            // 处理进度数据
            else if (data.type === 'progress') {
                this.updateProgress(data.progress, data.message);
            }
            // 处理完成信号
            else if (data.type === 'complete') {
                console.log('🏁 流式处理完成，模型:', data.model);
            }
            // 处理错误
            else if (data.type === 'error') {
                console.error('❌ 服务器错误:', data.message);
                throw new Error(data.message || '服务器处理错误');
            }
        } catch (parseError) {
            console.error('❌ JSON解析失败:', parseError, '原始数据:', dataStr);
        }
    }

    // 更新思考过程显示
    updateThinkingDisplay(content) {
        // 优先使用传入的目标容器，否则使用默认容器
        const targetContainer = this.currentTargetContainer || window.domManager.getElement('#ai-analysis-content');

        if (targetContainer && content) {
            // 清除加载状态（如果存在）
            const loadingDiv = targetContainer.querySelector('.spinner-border');
            if (loadingDiv && loadingDiv.parentElement) {
                loadingDiv.parentElement.remove();
            }

            // 如果还没有思考过程容器，创建一个
            let thinkingDiv = targetContainer.querySelector('.thinking-process');
            if (!thinkingDiv) {
                thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'thinking-process';
                thinkingDiv.innerHTML = '<h5><i class="bi bi-gear"></i> 思考过程:</h5><div class="thinking-content"></div>';
                targetContainer.appendChild(thinkingDiv);
            }

            const thinkingContent = thinkingDiv.querySelector('.thinking-content');
            if (thinkingContent) {
                thinkingContent.innerHTML += this.formatThinkingContent(content);
                this.scrollToBottom(thinkingContent);
            }
        }
    }

    // 更新最终结果显示
    updateFinalDisplay(content, thinkingMode) {
        // 优先使用传入的目标容器，否则使用默认容器
        const targetContainer = this.currentTargetContainer || window.domManager.getElement('#ai-analysis-content');

        if (targetContainer && content) {
            // 清除加载状态（如果存在）
            const loadingDiv = targetContainer.querySelector('.spinner-border');
            if (loadingDiv && loadingDiv.parentElement) {
                loadingDiv.parentElement.remove();
            }

            // 如果还没有最终结果容器，创建一个
            let finalDiv = targetContainer.querySelector('.final-result');
            if (!finalDiv) {
                finalDiv = document.createElement('div');
                finalDiv.className = 'final-result';
                finalDiv.innerHTML = '<h5><i class="bi bi-check-circle"></i> 分析结果:</h5><div class="final-content"></div>';
                targetContainer.appendChild(finalDiv);
            }

            const finalContent = finalDiv.querySelector('.final-content');
            if (finalContent) {
                finalContent.innerHTML += this.formatFinalContent(content);
                this.scrollToBottom(finalContent);
            }
        }
    }

    // 更新进度
    updateProgress(progress, message) {
        if (typeof window.updateAnalysisProgress === 'function') {
            window.updateAnalysisProgress(progress, message);
        }
    }

    // 格式化思考内容
    formatThinkingContent(content) {
        return content.replace(/\n/g, '<br>');
    }

    // 格式化最终内容
    formatFinalContent(content) {
        return content.replace(/\n/g, '<br>');
    }

    // 滚动到底部
    scrollToBottom(element) {
        element.scrollTop = element.scrollHeight;
    }

    // 准备流式UI
    async prepareStreamingUI(config) {
        const showThinking = config.thinking_mode;

        // 使用当前目标容器或默认容器
        const targetContainer = this.currentTargetContainer || window.domManager.getElement('#ai-analysis-content');

        if (targetContainer) {
            // 清空容器内容
            targetContainer.innerHTML = '';

            // 添加加载提示
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'text-center p-3';
            loadingDiv.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">分析中...</span>
                </div>
                <p class="mt-2">正在进行${showThinking ? 'DeepSeek-R1' : 'DeepSeek-V3'}分析...</p>
            `;
            targetContainer.appendChild(loadingDiv);

            // 等待DOM更新
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    // 生成流式HTML结构
    generateStreamingHTML(showThinking) {
        if (showThinking) {
            return `
                <div class="streaming-container">
                    <div class="thinking-section">
                        <h6><i class="bi bi-brain"></i> 推理过程</h6>
                        <div id="reasoning-stream" class="reasoning-content"></div>
                    </div>
                    <div id="final-analysis-container" class="final-section" style="display: none;">
                        <h6><i class="bi bi-check-circle"></i> 分析结论</h6>
                        <div id="final-stream-r1" class="final-content"></div>
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="streaming-container">
                    <div class="final-section">
                        <div id="final-stream-v3" class="final-content"></div>
                    </div>
                </div>
            `;
        }
    }

    // 重试流式处理
    async retryStreaming(streamId, config, lastError) {
        config.retryCount++;
        const delay = Math.min(
            this.retryConfig.baseDelay * Math.pow(2, config.retryCount - 1),
            this.retryConfig.maxDelay
        );

        console.log(`流式分析重试 ${config.retryCount}/${this.retryConfig.maxRetries}，延迟 ${delay}ms`);

        await new Promise(resolve => setTimeout(resolve, delay));

        return this.startStreaming(config.query, config);
    }

    // 取消流式处理
    cancelStreaming(streamId) {
        if (this.activeStreams.has(streamId)) {
            this.activeStreams.delete(streamId);
            window.appState.setState('streamingActive', false);
            window.appState.setState('isAnalyzing', false);
            return true;
        }
        return false;
    }

    // 取消所有流式处理
    cancelAllStreaming() {
        const streamIds = Array.from(this.activeStreams.keys());
        streamIds.forEach(id => this.cancelStreaming(id));
        return streamIds.length;
    }

    // 生成流ID
    generateStreamId() {
        return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // 记录流结果
    recordStreamResult(streamId, status, result) {
        const record = {
            streamId,
            status,
            result: status === 'error' ? result.message : result,
            timestamp: new Date().toISOString()
        };

        this.streamHistory.push(record);

        // 保持历史记录大小
        if (this.streamHistory.length > this.maxHistory) {
            this.streamHistory.shift();
        }
    }

    // 获取流状态
    getStreamStatus() {
        return {
            activeCount: this.activeStreams.size,
            activeStreams: Array.from(this.activeStreams.keys()),
            recentHistory: this.streamHistory.slice(-10)
        };
    }
}

// 全局流式管理器实例
window.streamingManager = new StreamingManager();

// ==================== 移动端适配管理 ====================
class MobileAdaptationManager {
    constructor() {
        this.isMobile = false;
        this.isTablet = false;
        this.orientation = 'portrait';
        this.viewportWidth = window.innerWidth;
        this.viewportHeight = window.innerHeight;
        this.touchSupport = 'ontouchstart' in window;
        this.init();
    }

    init() {
        this.detectDevice();
        this.setupEventListeners();
        this.applyMobileOptimizations();
        this.setupTouchGestures();
    }

    detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const width = window.innerWidth;

        // 检测移动设备
        this.isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent) || width <= 768;
        this.isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || (width > 768 && width <= 1024);

        // 检测方向
        this.orientation = width > window.innerHeight ? 'landscape' : 'portrait';

        // 更新状态
        window.appState.setState('ui.mobile', {
            isMobile: this.isMobile,
            isTablet: this.isTablet,
            orientation: this.orientation,
            touchSupport: this.touchSupport,
            viewportWidth: width,
            viewportHeight: window.innerHeight
        });
    }

    setupEventListeners() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 监听方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleOrientationChange(), 100);
        });

        // 监听触摸事件
        if (this.touchSupport) {
            this.setupTouchEvents();
        }
    }

    handleResize() {
        const oldWidth = this.viewportWidth;
        const oldHeight = this.viewportHeight;

        this.viewportWidth = window.innerWidth;
        this.viewportHeight = window.innerHeight;

        // 重新检测设备类型
        this.detectDevice();

        // 应用响应式调整
        this.applyResponsiveAdjustments();

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('mobileViewportChange', {
            detail: {
                oldWidth,
                oldHeight,
                newWidth: this.viewportWidth,
                newHeight: this.viewportHeight
            }
        }));
    }

    handleOrientationChange() {
        const newOrientation = this.viewportWidth > this.viewportHeight ? 'landscape' : 'portrait';

        if (newOrientation !== this.orientation) {
            this.orientation = newOrientation;
            window.appState.setState('ui.mobile.orientation', newOrientation);

            // 应用方向特定的优化
            this.applyOrientationOptimizations();

            // 触发自定义事件
            window.dispatchEvent(new CustomEvent('mobileOrientationChange', {
                detail: { orientation: newOrientation }
            }));
        }
    }

    applyMobileOptimizations() {
        if (!this.isMobile) return;

        // 添加移动端CSS类
        document.body.classList.add('mobile-device');

        if (this.isTablet) {
            document.body.classList.add('tablet-device');
        }

        // 优化触摸目标大小
        this.optimizeTouchTargets();

        // 优化滚动性能
        this.optimizeScrolling();

        // 优化表单输入
        this.optimizeFormInputs();

        // 优化模态框
        this.optimizeModals();
    }

    optimizeTouchTargets() {
        const style = document.createElement('style');
        style.textContent = `
            .mobile-device button,
            .mobile-device .btn,
            .mobile-device .nav-link,
            .mobile-device .form-control {
                min-height: 44px;
                min-width: 44px;
            }

            .mobile-device .btn-sm {
                min-height: 36px;
                min-width: 36px;
            }

            .mobile-device .table td,
            .mobile-device .table th {
                padding: 12px 8px;
            }
        `;
        document.head.appendChild(style);
    }

    optimizeScrolling() {
        // 启用平滑滚动
        document.documentElement.style.scrollBehavior = 'smooth';

        // 优化滚动容器
        const scrollContainers = document.querySelectorAll('.overflow-auto, .overflow-y-auto');
        scrollContainers.forEach(container => {
            container.style.webkitOverflowScrolling = 'touch';
        });
    }

    optimizeFormInputs() {
        // 优化输入框在移动端的表现
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // 防止缩放
            if (input.type !== 'file') {
                input.style.fontSize = '16px';
            }

            // 优化数字输入
            if (input.type === 'number') {
                input.setAttribute('inputmode', 'numeric');
            }
        });
    }

    optimizeModals() {
        const style = document.createElement('style');
        style.textContent = `
            @media (max-width: 768px) {
                .modal-dialog {
                    margin: 10px;
                    max-width: calc(100vw - 20px);
                }

                .modal-content {
                    max-height: calc(100vh - 20px);
                    overflow-y: auto;
                }

                .modal-header {
                    padding: 12px 16px;
                }

                .modal-body {
                    padding: 16px;
                    max-height: calc(100vh - 120px);
                    overflow-y: auto;
                }

                .modal-footer {
                    padding: 12px 16px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    setupTouchEvents() {
        let touchStartX = 0;
        let touchStartY = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;

            // 检测滑动手势
            if (Math.abs(deltaX) > 50 || Math.abs(deltaY) > 50) {
                this.handleSwipeGesture(deltaX, deltaY);
            }
        }, { passive: true });
    }

    setupTouchGestures() {
        // 双击缩放禁用（防止意外缩放）
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (e) => {
            const now = Date.now();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }

    handleSwipeGesture(deltaX, deltaY) {
        // 水平滑动
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            if (deltaX > 0) {
                // 向右滑动
                this.handleSwipeRight();
            } else {
                // 向左滑动
                this.handleSwipeLeft();
            }
        }
        // 垂直滑动
        else {
            if (deltaY > 0) {
                // 向下滑动
                this.handleSwipeDown();
            } else {
                // 向上滑动
                this.handleSwipeUp();
            }
        }
    }

    handleSwipeRight() {
        // 可以实现返回上一页等功能
        window.dispatchEvent(new CustomEvent('swipeRight'));
    }

    handleSwipeLeft() {
        // 可以实现前进下一页等功能
        window.dispatchEvent(new CustomEvent('swipeLeft'));
    }

    handleSwipeDown() {
        // 可以实现下拉刷新等功能
        window.dispatchEvent(new CustomEvent('swipeDown'));
    }

    handleSwipeUp() {
        // 可以实现上拉加载等功能
        window.dispatchEvent(new CustomEvent('swipeUp'));
    }

    applyResponsiveAdjustments() {
        // 根据屏幕大小调整布局
        if (this.viewportWidth <= 576) {
            document.body.classList.add('screen-xs');
        } else {
            document.body.classList.remove('screen-xs');
        }

        if (this.viewportWidth <= 768) {
            document.body.classList.add('screen-sm');
        } else {
            document.body.classList.remove('screen-sm');
        }
    }

    applyOrientationOptimizations() {
        if (this.orientation === 'landscape' && this.isMobile) {
            // 横屏优化
            document.body.classList.add('landscape-mode');
            document.body.classList.remove('portrait-mode');
        } else {
            // 竖屏优化
            document.body.classList.add('portrait-mode');
            document.body.classList.remove('landscape-mode');
        }
    }

    // 获取设备信息
    getDeviceInfo() {
        return {
            isMobile: this.isMobile,
            isTablet: this.isTablet,
            orientation: this.orientation,
            touchSupport: this.touchSupport,
            viewportWidth: this.viewportWidth,
            viewportHeight: this.viewportHeight
        };
    }

    // 检查是否为移动设备
    isMobileDevice() {
        return this.isMobile;
    }

    // 检查是否为平板设备
    isTabletDevice() {
        return this.isTablet;
    }

    // 获取当前方向
    getCurrentOrientation() {
        return this.orientation;
    }
}

// 全局移动端适配管理器实例
window.mobileManager = new MobileAdaptationManager();

// 显示提示消息
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    
    const alertId = 'alert-' + Date.now();
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="bi bi-${getAlertIcon(type)}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // 自动消失
    if (duration > 0) {
        setTimeout(() => {
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                // 安全的Alert关闭方式
                if (window.bootstrap && window.bootstrap.Alert) {
                    const bsAlert = new window.bootstrap.Alert(alertElement);
                    bsAlert.close();
                } else {
                    // 备用关闭方式
                    alertElement.style.opacity = '0';
                    setTimeout(() => {
                        if (alertElement.parentNode) {
                            alertElement.parentNode.removeChild(alertElement);
                        }
                    }, 300);
                }
            }
        }, duration);
    }
}

// 创建提示消息容器
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// 获取提示图标
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 格式化日期时间
function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return '';
    
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 防抖函数
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 深拷贝对象
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

// 生成UUID
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 验证表单
function validateForm(formId, rules) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    let isValid = true;
    const errors = [];
    
    for (const fieldName in rules) {
        const field = form.querySelector(`[name="${fieldName}"], #${fieldName}`);
        if (!field) continue;
        
        const rule = rules[fieldName];
        const value = field.value.trim();
        
        // 必填验证
        if (rule.required && !value) {
            isValid = false;
            errors.push(`${rule.label || fieldName}不能为空`);
            field.classList.add('is-invalid');
            continue;
        }
        
        // 长度验证
        if (rule.minLength && value.length < rule.minLength) {
            isValid = false;
            errors.push(`${rule.label || fieldName}长度不能少于${rule.minLength}个字符`);
            field.classList.add('is-invalid');
            continue;
        }
        
        if (rule.maxLength && value.length > rule.maxLength) {
            isValid = false;
            errors.push(`${rule.label || fieldName}长度不能超过${rule.maxLength}个字符`);
            field.classList.add('is-invalid');
            continue;
        }
        
        // 正则验证
        if (rule.pattern && !rule.pattern.test(value)) {
            isValid = false;
            errors.push(rule.message || `${rule.label || fieldName}格式不正确`);
            field.classList.add('is-invalid');
            continue;
        }
        
        // 清除错误状态
        field.classList.remove('is-invalid');
    }
    
    if (!isValid) {
        showAlert(errors.join('<br>'), 'danger');
    }
    
    return isValid;
}

// 导出数据为CSV
function exportToCSV(data, filename) {
    if (!data || !data.length) {
        showAlert('没有数据可导出', 'warning');
        return;
    }
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', filename || 'export.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 导出数据为JSON
function exportToJSON(data, filename) {
    if (!data) {
        showAlert('没有数据可导出', 'warning');
        return;
    }
    
    const jsonContent = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', filename || 'export.json');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 加载状态管理
function showLoading(elementId, message = '加载中...') {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">${message}</span>
                </div>
                <p class="mt-2 text-muted">${message}</p>
            </div>
        `;
    }
}

function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = '';
    }
}

// 本地存储工具
const storage = {
    set: function(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (e) {
            console.error('存储数据失败:', e);
            return false;
        }
    },
    
    get: function(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            console.error('读取数据失败:', e);
            return defaultValue;
        }
    },
    
    remove: function(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            console.error('删除数据失败:', e);
            return false;
        }
    },
    
    clear: function() {
        try {
            localStorage.clear();
            return true;
        } catch (e) {
            console.error('清空数据失败:', e);
            return false;
        }
    }
};

// 网络请求工具
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    };
    
    const config = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, config);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            return await response.text();
        }
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 图片预览
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById(previewId);
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}
