#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI工具函数模块
提取ui/app.py中的工具函数，保持功能完整
"""

import os
import json
import uuid
import hashlib
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from werkzeug.utils import secure_filename
import logging

logger = logging.getLogger(__name__)

def generate_unique_filename(original_filename: str) -> str:
    """生成唯一文件名"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    name, ext = os.path.splitext(original_filename)
    return f"{timestamp}_{unique_id}_{secure_filename(name)}{ext}"

def calculate_file_hash(file_path: str) -> str:
    """计算文件哈希值"""
    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"计算文件哈希失败: {e}")
        return ""

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def validate_file_type(filename: str, allowed_extensions: List[str]) -> bool:
    """验证文件类型"""
    if not filename:
        return False
    
    ext = os.path.splitext(filename)[1].lower()
    return ext in [e.lower() for e in allowed_extensions]

def clean_text_content(text: str) -> str:
    """清理文本内容"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = ' '.join(text.split())
    
    # 移除特殊字符（保留中文、英文、数字、常用标点）
    import re
    text = re.sub(r'[^\u4e00-\u9fff\w\s\.,;:!?()[\]{}"\'-]', '', text)
    
    return text.strip()

def format_timestamp(timestamp: Optional[float] = None) -> str:
    """格式化时间戳"""
    if timestamp is None:
        timestamp = time.time()
    
    return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")

def create_response(success: bool = True, message: str = "", data: Any = None, 
                   error: str = "", status_code: int = 200) -> Dict[str, Any]:
    """创建标准响应格式"""
    response = {
        "success": success,
        "timestamp": format_timestamp(),
        "status_code": status_code
    }
    
    if message:
        response["message"] = message
    
    if data is not None:
        response["data"] = data
    
    if error:
        response["error"] = error
        response["success"] = False
    
    return response

def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """安全的JSON解析"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError) as e:
        logger.warning(f"JSON解析失败: {e}")
        return default

def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """安全的JSON序列化"""
    try:
        return json.dumps(obj, ensure_ascii=False, indent=2)
    except (TypeError, ValueError) as e:
        logger.warning(f"JSON序列化失败: {e}")
        return default

def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """提取关键词"""
    if not text:
        return []
    
    # 简单的关键词提取（基于词频）
    import re
    from collections import Counter
    
    # 移除标点符号，分词
    words = re.findall(r'\b\w+\b', text.lower())
    
    # 过滤短词和常用词
    stop_words = {'的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '着', '过',
                  'the', 'is', 'in', 'and', 'or', 'but', 'a', 'an', 'to', 'of', 'for'}
    
    words = [word for word in words if len(word) > 1 and word not in stop_words]
    
    # 统计词频
    word_counts = Counter(words)
    
    # 返回最常见的词
    return [word for word, count in word_counts.most_common(max_keywords)]

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """截断文本"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def merge_dicts(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """合并多个字典"""
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result

def get_client_ip(request) -> str:
    """获取客户端IP地址"""
    # 检查代理头
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr or 'unknown'

def validate_json_structure(data: Dict[str, Any], required_fields: List[str]) -> tuple[bool, str]:
    """验证JSON结构"""
    if not isinstance(data, dict):
        return False, "数据必须是字典格式"
    
    missing_fields = []
    for field in required_fields:
        if field not in data:
            missing_fields.append(field)
    
    if missing_fields:
        return False, f"缺少必需字段: {', '.join(missing_fields)}"
    
    return True, ""

def create_pagination_info(page: int, per_page: int, total: int) -> Dict[str, Any]:
    """创建分页信息"""
    total_pages = (total + per_page - 1) // per_page
    
    return {
        "page": page,
        "per_page": per_page,
        "total": total,
        "total_pages": total_pages,
        "has_prev": page > 1,
        "has_next": page < total_pages,
        "prev_page": page - 1 if page > 1 else None,
        "next_page": page + 1 if page < total_pages else None
    }

def log_performance(func_name: str, start_time: float, end_time: float = None) -> None:
    """记录性能日志"""
    if end_time is None:
        end_time = time.time()
    
    duration = end_time - start_time
    logger.info(f"性能监控 - {func_name}: {duration:.3f}秒")

class PerformanceTimer:
    """性能计时器上下文管理器"""
    
    def __init__(self, name: str):
        self.name = name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            log_performance(self.name, self.start_time)
