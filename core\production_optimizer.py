#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境优化器
针对生产环境进行性能优化、安全加固、监控配置等
提供生产级别的系统配置和优化建议
"""

import os
import gc
import threading
from typing import Dict, Any, Optional, List
from loguru import logger
from dataclasses import dataclass
from enum import Enum

from .config_manager import get_config


class OptimizationLevel(Enum):
    """优化级别"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class OptimizationConfig:
    """优化配置"""
    level: OptimizationLevel
    enable_caching: bool = True
    enable_compression: bool = True
    enable_monitoring: bool = True
    max_workers: int = 4
    memory_limit_mb: int = 1024
    request_timeout: int = 30
    enable_rate_limiting: bool = True
    enable_security_headers: bool = True


class ProductionOptimizer:
    """生产环境优化器"""
    
    def __init__(self):
        self.config = get_config()
        self.optimization_level = self._detect_environment()
        self.optimization_config = self._get_optimization_config()
        self._applied_optimizations = set()
        
    def _detect_environment(self) -> OptimizationLevel:
        """检测运行环境"""
        env = self.config.get("system.environment", "development").lower()
        
        if env in ["prod", "production"]:
            return OptimizationLevel.PRODUCTION
        elif env in ["stage", "staging"]:
            return OptimizationLevel.STAGING
        else:
            return OptimizationLevel.DEVELOPMENT
    
    def _get_optimization_config(self) -> OptimizationConfig:
        """获取优化配置"""
        if self.optimization_level == OptimizationLevel.PRODUCTION:
            return OptimizationConfig(
                level=OptimizationLevel.PRODUCTION,
                enable_caching=True,
                enable_compression=True,
                enable_monitoring=True,
                max_workers=8,
                memory_limit_mb=2048,
                request_timeout=30,
                enable_rate_limiting=True,
                enable_security_headers=True
            )
        elif self.optimization_level == OptimizationLevel.STAGING:
            return OptimizationConfig(
                level=OptimizationLevel.STAGING,
                enable_caching=True,
                enable_compression=False,
                enable_monitoring=True,
                max_workers=4,
                memory_limit_mb=1024,
                request_timeout=60,
                enable_rate_limiting=False,
                enable_security_headers=True
            )
        else:
            return OptimizationConfig(
                level=OptimizationLevel.DEVELOPMENT,
                enable_caching=False,
                enable_compression=False,
                enable_monitoring=False,
                max_workers=2,
                memory_limit_mb=512,
                request_timeout=120,
                enable_rate_limiting=False,
                enable_security_headers=False
            )
    
    def optimize_memory_usage(self) -> None:
        """优化内存使用"""
        if "memory" in self._applied_optimizations:
            return
            
        logger.info("🧠 优化内存使用...")
        
        try:
            # 强制垃圾回收
            gc.collect()
            
            # 设置垃圾回收阈值
            if self.optimization_level == OptimizationLevel.PRODUCTION:
                gc.set_threshold(700, 10, 10)
            else:
                gc.set_threshold(700, 10, 10)
            
            # 启用垃圾回收调试（仅开发环境）
            if self.optimization_level == OptimizationLevel.DEVELOPMENT:
                gc.set_debug(gc.DEBUG_STATS)
            
            self._applied_optimizations.add("memory")
            logger.info("✅ 内存优化完成")
            
        except Exception as e:
            logger.error(f"❌ 内存优化失败: {e}")
    
    def optimize_threading(self) -> None:
        """优化线程配置"""
        if "threading" in self._applied_optimizations:
            return
            
        logger.info("🧵 优化线程配置...")
        
        try:
            # 设置线程栈大小
            if self.optimization_level == OptimizationLevel.PRODUCTION:
                threading.stack_size(1024 * 1024)  # 1MB
            
            self._applied_optimizations.add("threading")
            logger.info("✅ 线程优化完成")
            
        except Exception as e:
            logger.error(f"❌ 线程优化失败: {e}")
    
    def optimize_logging(self) -> None:
        """优化日志配置"""
        if "logging" in self._applied_optimizations:
            return
            
        logger.info("📝 优化日志配置...")
        
        try:
            # 根据环境调整日志级别
            if self.optimization_level == OptimizationLevel.PRODUCTION:
                logger.remove()
                logger.add(
                    "logs/production.log",
                    level="INFO",
                    rotation="1 day",
                    retention="30 days",
                    compression="gz",
                    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
                )
                logger.add(
                    "logs/error.log",
                    level="ERROR",
                    rotation="1 day",
                    retention="90 days",
                    compression="gz"
                )
            elif self.optimization_level == OptimizationLevel.STAGING:
                logger.remove()
                logger.add(
                    "logs/staging.log",
                    level="DEBUG",
                    rotation="1 day",
                    retention="7 days"
                )
            
            self._applied_optimizations.add("logging")
            logger.info("✅ 日志优化完成")
            
        except Exception as e:
            logger.error(f"❌ 日志优化失败: {e}")
    
    def get_flask_config(self) -> Dict[str, Any]:
        """获取Flask优化配置"""
        base_config = {
            'SECRET_KEY': os.environ.get('SECRET_KEY', 'dev-secret-key'),
            'MAX_CONTENT_LENGTH': 100 * 1024 * 1024,  # 100MB
        }
        
        if self.optimization_level == OptimizationLevel.PRODUCTION:
            base_config.update({
                'DEBUG': False,
                'TESTING': False,
                'SEND_FILE_MAX_AGE_DEFAULT': 31536000,  # 1年缓存
                'PERMANENT_SESSION_LIFETIME': 3600,     # 1小时会话
                'SESSION_COOKIE_SECURE': True,
                'SESSION_COOKIE_HTTPONLY': True,
                'SESSION_COOKIE_SAMESITE': 'Lax',
            })
        elif self.optimization_level == OptimizationLevel.STAGING:
            base_config.update({
                'DEBUG': False,
                'TESTING': True,
                'SEND_FILE_MAX_AGE_DEFAULT': 300,       # 5分钟缓存
            })
        else:
            base_config.update({
                'DEBUG': True,
                'TESTING': False,
            })
        
        return base_config
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器优化配置"""
        return {
            'host': self.config.get('server.host', '0.0.0.0'),
            'port': self.config.get('server.port', 5002),
            'workers': self.optimization_config.max_workers,
            'timeout': self.optimization_config.request_timeout,
            'keepalive': 2 if self.optimization_level == OptimizationLevel.PRODUCTION else 0,
            'max_requests': 1000 if self.optimization_level == OptimizationLevel.PRODUCTION else 0,
            'preload_app': self.optimization_level == OptimizationLevel.PRODUCTION,
        }
    
    def apply_security_optimizations(self) -> Dict[str, str]:
        """应用安全优化"""
        if not self.optimization_config.enable_security_headers:
            return {}
        
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
        }
        
        logger.info("🔒 应用安全优化")
        return security_headers
    
    def get_cache_config(self) -> Dict[str, Any]:
        """获取缓存配置"""
        if not self.optimization_config.enable_caching:
            return {'enabled': False}
        
        return {
            'enabled': True,
            'type': 'memory',
            'max_size': 1000,
            'ttl': 3600 if self.optimization_level == OptimizationLevel.PRODUCTION else 300,
        }
    
    def apply_all_optimizations(self) -> None:
        """应用所有优化"""
        logger.info(f"🚀 应用 {self.optimization_level.value} 环境优化...")
        
        self.optimize_memory_usage()
        self.optimize_threading()
        self.optimize_logging()
        
        logger.info("✅ 所有优化应用完成")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        return {
            'environment': self.optimization_level.value,
            'applied_optimizations': list(self._applied_optimizations),
            'config': {
                'caching': self.optimization_config.enable_caching,
                'compression': self.optimization_config.enable_compression,
                'monitoring': self.optimization_config.enable_monitoring,
                'max_workers': self.optimization_config.max_workers,
                'memory_limit_mb': self.optimization_config.memory_limit_mb,
                'request_timeout': self.optimization_config.request_timeout,
                'rate_limiting': self.optimization_config.enable_rate_limiting,
                'security_headers': self.optimization_config.enable_security_headers,
            }
        }


# 全局优化器实例
_production_optimizer = None


def get_production_optimizer() -> ProductionOptimizer:
    """获取全局生产优化器实例"""
    global _production_optimizer
    if _production_optimizer is None:
        _production_optimizer = ProductionOptimizer()
    return _production_optimizer


def apply_production_optimizations() -> None:
    """应用生产优化的便捷函数"""
    get_production_optimizer().apply_all_optimizations()


def get_optimized_flask_config() -> Dict[str, Any]:
    """获取优化的Flask配置"""
    return get_production_optimizer().get_flask_config()


def get_optimized_server_config() -> Dict[str, Any]:
    """获取优化的服务器配置"""
    return get_production_optimizer().get_server_config()
