"""
数据处理模块 - 统一数据处理接口

提供文档处理、图像处理、OCR等功能
重构后使用统一的数据处理器接口，消除重复代码
"""

# 核心处理器 - 延迟导入避免依赖问题
def _lazy_import():
    """延迟导入模块"""
    global TextProcessor, ImageProcessor, VectorProcessor, OCRProcessor, UnifiedDataProcessor

    try:
        from .text_processor import TextProcessor
    except ImportError as e:
        print(f"Warning: TextProcessor导入失败: {e}")
        TextProcessor = None

    try:
        from .image_processor import ImageProcessor
    except ImportError as e:
        print(f"Warning: ImageProcessor导入失败: {e}")
        ImageProcessor = None

    try:
        from .vector_processor import VectorProcessor
    except ImportError as e:
        print(f"Warning: VectorProcessor导入失败: {e}")
        VectorProcessor = None

    try:
        from .ocr_processor import OCRProcessor
    except ImportError as e:
        print(f"Warning: OCRProcessor导入失败: {e}")
        OCRProcessor = None

    # 统一数据处理器 - 使用专业级处理器作为主要实现
    try:
        from .professional_data_processor import ProfessionalDataProcessor
        UnifiedDataProcessor = ProfessionalDataProcessor
    except ImportError as e:
        print(f"Warning: 统一数据处理器导入失败: {e}")
        UnifiedDataProcessor = None

# 初始化变量
TextProcessor = None
ImageProcessor = None
VectorProcessor = None
OCRProcessor = None
UnifiedDataProcessor = None
DATA_PROCESSOR_AVAILABLE = False

# 立即导入统一数据处理器
try:
    from .professional_data_processor import ProfessionalDataProcessor
    UnifiedDataProcessor = ProfessionalDataProcessor
    DATA_PROCESSOR_AVAILABLE = True
except ImportError as e:
    print(f"Warning: 统一数据处理器导入失败: {e}")

# 调用延迟导入
_lazy_import()

__all__ = [
    "TextProcessor",
    "ImageProcessor",
    "VectorProcessor",
    "OCRProcessor",
    "UnifiedDataProcessor",
    "DATA_PROCESSOR_AVAILABLE",
    "_lazy_import"
]

# 添加可用的处理器
if DATA_PROCESSOR_AVAILABLE:
    __all__.append('ProfessionalDataProcessor')
