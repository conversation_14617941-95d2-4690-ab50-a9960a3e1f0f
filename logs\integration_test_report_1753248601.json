{"test_summary": {"total_tests": 8, "overall_score": 0.6459217871755747, "total_time": 73.89603471755981, "test_date": "2025-07-23T13:30:01.387201"}, "test_results": {"unified_retrieval": {"success_rate": 1.0, "avg_response_time": 14.058523416519165, "avg_quality_score": 0.20333333333333334, "strategy_results": {"hybrid": {"success": true, "results_count": 0, "response_time": 23.969273805618286, "quality_score": 0.20333333333333334}, "professional": {"success": true, "results_count": 0, "response_time": 0.0010061264038085938, "quality_score": 0.20333333333333334}, "semantic": {"success": true, "results_count": 0, "response_time": 18.2052903175354, "quality_score": 0.20333333333333334}}, "score": 0.42482151962682035}, "professional_prompts": {"success_rate": 0.5, "avg_quality": 0.40549999999999997, "template_results": {"deepseek_r1_fault_analysis": {"success": false, "error": "'image_analysis'", "overall_quality": 0.0}, "deepseek_v3_fault_analysis": {"success": true, "prompt_length": 866, "quality_metrics": {"length": 0.433, "structure": 1.0, "professional": 1.0}, "overall_quality": 0.8109999999999999}}, "score": 0.45275}, "deepseek_r1_reasoning": {"optimization_time": 0.008510112762451172, "quality_score": 0.28833333333333333, "improvements_applied": true, "grpo_results": {"total_reward": 0.5527500000000001, "policy_parameters": {"exploration_rate": 0.1, "learning_rate": 0.01, "discount_factor": 0.95}, "improvement_trend": false}, "verification_results": {"verification_results": {"consistency_check": {"passed": true, "inconsistencies": [], "score": 1.0}, "completeness_check": {"passed": false, "missing_stages": ["analysis", "conclusion"], "score": 0.3333333333333333}, "logic_check": {"passed": false, "score": 0.2, "suggestions": ["增加更多逻辑连接词和因果关系说明"]}, "technical_check": {"passed": false, "score": 0.2, "professional_terms_used": 3.0}}, "overall_score": 0.25, "passed": false, "suggestions": ["补充缺失的推理阶段", "增强逻辑连接和因果关系说明", "增加专业术语和技术细节"]}, "score": 0.28833333333333333}, "advanced_rag": {"response_time": 0.0, "results_count": 6, "quality_score": 0.8098333333333334, "techniques_used": ["hyde", "self_rag", "raptor"], "reasoning_chain": ["应用HyDE技术生成假设性文档", "应用Self-RAG技术进行自我反思", "应用RAPTOR技术构建抽象树"], "score": 0.8098333333333334}, "data_processing": {"document_type": "fault_report", "entities_count": 8, "relationships_count": 5, "technical_terms_count": 2, "quality_metrics": {"overall_score": 0.691636111111111, "completeness": 0.47433333333333333, "accuracy": 0.5249999999999999, "professional_depth": 1.511111111111111}, "score": 0.691636111111111}, "output_formatting": {"success_rate": 1.0, "format_results": {"structured_text": {"success": true, "report_length": 900, "contains_structure": true}, "html": {"success": true, "report_length": 2151, "contains_structure": true}, "json": {"success": true, "report_length": 58, "contains_structure": false}}, "score": 1.0}, "system_configuration": {"config_completeness": 1.0, "component_completeness": 1.0, "config_summary": {"system_info": {"name": "白银市电力故障诊断系统", "version": "2.0.0", "environment": "development"}, "components_enabled": {"deepseek_api": true, "unified_retriever": true, "advanced_rag": true, "data_processing": true, "monitoring": true}, "config_files": {"main": true, "database": true, "api": true, "models": true, "retrieval": true, "processing": true, "ui": true, "monitoring": true}, "validation_status": "通过", "last_updated": "2025-07-23T13:30:01.380680"}, "score": 1.0}, "end_to_end_integration": {"retrieval_success": false, "prompt_success": true, "processing_success": false, "formatting_success": true, "integration_score": 0.5, "integration_details": {"retrieval_details": {"success": false, "results_count": 0}, "prompt_details": {"success": true, "prompt_length": 866}, "processing_details": {"success": false, "error": "cannot access local variable 'doc_id' where it is not associated with a value"}, "formatting_details": {"success": true, "report_length": 623}}, "score": 0.5}}, "recommendations": ["建议优化检索算法和索引结构", "建议完善提示词模板和Few-shot示例", "建议加强推理链验证和自我纠错机制", "建议改进数据质量评估标准和处理流程", "系统存在较多问题，建议全面检查和重构"], "system_status": "可接受 - 系统功能基本可用，需要改进"}