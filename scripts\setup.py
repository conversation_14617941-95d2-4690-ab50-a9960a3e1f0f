#!/usr/bin/env python3
"""
环境设置脚本

初始化项目环境和依赖
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"执行: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} 完成")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} 失败: {e.stderr}")
        return None

def create_directories():
    """创建必要的目录结构"""
    directories = [
        "logs",
        "data/01_raw",
        "data/04_production/cached", 
        "data/02_processed/structured",
        "embeddings/faiss_store",
        "embeddings/index",
        "knowledge_base/text",
        "knowledge_base/images",
        "knowledge_base/mappings",
        "uploads/temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录: {directory}")

def install_dependencies():
    """安装Python依赖"""
    print("安装Python依赖...")
    
    # 检查pip
    run_command("python -m pip install --upgrade pip", "升级pip")
    
    # 安装requirements.txt中的依赖
    if os.path.exists("requirements.txt"):
        run_command("pip install -r requirements.txt", "安装项目依赖")
    else:
        print("警告: requirements.txt 文件不存在")

def initialize_vector_db():
    """初始化向量数据库"""
    print("初始化向量数据库...")
    
    try:
        import faiss
        import numpy as np
        
        # 创建FAISS索引
        dimension = 768  # sentence-transformers默认维度
        index = faiss.IndexFlatIP(dimension)
        
        # 保存索引
        faiss.write_index(index, 'embeddings/faiss_store/index.faiss')
        print("✓ FAISS索引初始化完成")
        
    except ImportError:
        print("警告: faiss-cpu 未安装，跳过向量数据库初始化")

def initialize_knowledge_base():
    """初始化知识库"""
    print("初始化知识库...")
    
    # 创建示例知识库数据
    sample_data = {
        "documents": [
            {
                "id": "doc_001",
                "title": "变压器故障诊断手册",
                "content": "变压器是电力系统中的重要设备，常见故障包括绕组故障、铁心故障、套管故障等。诊断时需要综合考虑电气试验、油化分析、声学检测等多种手段。",
                "type": "manual",
                "source": "internal",
                "keywords": ["变压器", "故障诊断", "绕组", "铁心", "套管"]
            },
            {
                "id": "doc_002",
                "title": "断路器维护规程", 
                "content": "断路器应定期检查操作机构、触头系统、绝缘系统等关键部件。维护周期根据设备类型和运行环境确定，一般为1-3年。",
                "type": "procedure",
                "source": "standard",
                "keywords": ["断路器", "维护", "操作机构", "触头", "绝缘"]
            },
            {
                "id": "doc_003",
                "title": "电缆故障定位技术",
                "content": "电缆故障定位常用方法包括脉冲反射法、声测法、感应法等。不同故障类型需要选择合适的定位方法。",
                "type": "technical",
                "source": "research",
                "keywords": ["电缆", "故障定位", "脉冲反射", "声测法"]
            }
        ],
        "equipment_types": [
            {
                "type": "transformer",
                "name": "变压器",
                "common_faults": ["绕组故障", "铁心故障", "套管故障", "冷却系统故障"],
                "inspection_items": ["绝缘电阻", "变比", "直流电阻", "油化分析"]
            },
            {
                "type": "breaker", 
                "name": "断路器",
                "common_faults": ["拒动", "误动", "触头烧损", "操作机构故障"],
                "inspection_items": ["动作时间", "触头行程", "分合闸速度", "绝缘电阻"]
            }
        ]
    }
    
    # 保存到文件
    with open('knowledge_base/mappings/documents.json', 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("✓ 知识库初始化完成")

def create_env_file():
    """创建环境变量文件"""
    env_content = """# 故障分析智能助手环境变量配置

# DeepSeek API配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 数据库配置
VECTOR_DB_PATH=embeddings/faiss_store
KNOWLEDGE_BASE_PATH=knowledge_base

# 服务配置
API_HOST=0.0.0.0
API_PORT=8000
WEB_HOST=0.0.0.0
WEB_PORT=3000

# 文件上传配置
UPLOAD_MAX_SIZE=100MB
UPLOAD_TEMP_DIR=uploads/temp

# OCR配置
OCR_ENGINE=paddleocr
OCR_LANGUAGES=ch,en
"""
    
    if not os.path.exists('.env'):
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✓ 创建 .env 文件")
    else:
        print("✓ .env 文件已存在")

def main():
    """主函数"""
    print("=== 故障分析智能助手环境设置 ===")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✓ Python版本: {sys.version}")
    
    # 执行设置步骤
    create_directories()
    install_dependencies()
    initialize_vector_db()
    initialize_knowledge_base()
    create_env_file()
    
    print("\n=== 环境设置完成 ===")
    print("下一步:")
    print("1. 编辑 .env 文件，设置你的 DEEPSEEK_API_KEY")
    print("2. 运行 'python scripts/start.sh' 启动服务")
    print("3. 访问 http://localhost:3000 使用Web界面")
    print("4. 访问 http://localhost:8000/docs 查看API文档")

if __name__ == "__main__":
    main()
