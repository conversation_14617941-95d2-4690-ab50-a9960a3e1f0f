#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
嵌入模型训练脚本
用于微调电力领域的文本嵌入模型
"""

import os
import sys
import yaml
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

import torch
import numpy as np
from torch.utils.data import DataLoader
from sentence_transformers import SentenceTransformer, InputExample, losses
from sentence_transformers.evaluation import EmbeddingSimilarityEvaluator
from sentence_transformers.readers import InputExample

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PowerSystemEmbeddingTrainer:
    """电力系统嵌入模型训练器"""
    
    def __init__(self, config_path: str):
        """初始化训练器"""
        self.config = self.load_config(config_path)
        self.model = None
        self.train_examples = []
        self.val_examples = []
        
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    
    def prepare_model(self):
        """准备模型"""
        model_config = self.config['model']
        
        # 加载预训练模型
        self.model = SentenceTransformer(model_config['base_model'])
        
        # 设置最大序列长度
        self.model.max_seq_length = model_config['max_seq_length']
        
        logger.info(f"已加载模型: {model_config['base_model']}")
        logger.info(f"最大序列长度: {model_config['max_seq_length']}")
    
    def load_training_data(self):
        """加载训练数据"""
        data_config = self.config['data']
        
        # 加载训练数据
        self.train_examples = self.load_examples(data_config['train_data_path'])
        self.val_examples = self.load_examples(data_config['val_data_path'])
        
        logger.info(f"训练样本数量: {len(self.train_examples)}")
        logger.info(f"验证样本数量: {len(self.val_examples)}")
    
    def load_examples(self, data_path: str) -> List[InputExample]:
        """加载样本数据"""
        examples = []
        
        if not os.path.exists(data_path):
            logger.warning(f"数据文件不存在: {data_path}")
            return examples
        
        try:
            import json
            with open(data_path, 'r', encoding='utf-8') as f:
                for line in f:
                    data = json.loads(line.strip())
                    
                    # 创建正样本对
                    if 'query' in data and 'positive' in data:
                        example = InputExample(
                            texts=[data['query'], data['positive']],
                            label=1.0
                        )
                        examples.append(example)
                    
                    # 创建负样本对
                    if 'query' in data and 'negative' in data:
                        example = InputExample(
                            texts=[data['query'], data['negative']],
                            label=0.0
                        )
                        examples.append(example)
                        
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
        
        return examples
    
    def setup_training(self):
        """设置训练参数"""
        training_config = self.config['training']
        
        # 创建数据加载器
        train_dataloader = DataLoader(
            self.train_examples,
            shuffle=True,
            batch_size=training_config['batch_size']
        )
        
        # 设置损失函数
        loss_config = self.config['loss']
        if loss_config['type'] == 'MultipleNegativesRankingLoss':
            train_loss = losses.MultipleNegativesRankingLoss(
                model=self.model,
                scale=loss_config['scale'],
                similarity_fct=getattr(losses.util, loss_config['similarity_fct'])
            )
        else:
            train_loss = losses.CosineSimilarityLoss(model=self.model)
        
        # 设置评估器
        evaluator = None
        if self.val_examples:
            evaluator = EmbeddingSimilarityEvaluator.from_input_examples(
                self.val_examples,
                name='validation'
            )
        
        return train_dataloader, train_loss, evaluator
    
    def train(self):
        """开始训练"""
        logger.info("开始训练嵌入模型...")
        
        # 准备训练组件
        train_dataloader, train_loss, evaluator = self.setup_training()
        
        # 训练配置
        training_config = self.config['training']
        output_config = self.config['output']
        
        # 创建输出目录
        os.makedirs(output_config['output_dir'], exist_ok=True)
        os.makedirs(output_config['logging_dir'], exist_ok=True)
        
        # 开始训练
        self.model.fit(
            train_objectives=[(train_dataloader, train_loss)],
            evaluator=evaluator,
            epochs=training_config['num_epochs'],
            evaluation_steps=self.config['evaluation']['eval_steps'],
            warmup_steps=training_config['warmup_steps'],
            output_path=output_config['output_dir'],
            save_best_model=True,
            optimizer_params={
                'lr': training_config['learning_rate'],
                'weight_decay': training_config['weight_decay']
            }
        )
        
        logger.info("训练完成!")
    
    def evaluate(self):
        """评估模型"""
        logger.info("开始评估模型...")
        
        if not self.val_examples:
            logger.warning("没有验证数据，跳过评估")
            return
        
        # 创建评估器
        evaluator = EmbeddingSimilarityEvaluator.from_input_examples(
            self.val_examples,
            name='final_evaluation'
        )
        
        # 运行评估
        score = evaluator(self.model, output_path=self.config['output']['output_dir'])
        logger.info(f"最终评估分数: {score}")
        
        return score

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="训练电力系统嵌入模型")
    parser.add_argument(
        "--config",
        type=str,
        default="training/configs/embedding_config.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--eval-only",
        action="store_true",
        help="仅运行评估"
    )
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = PowerSystemEmbeddingTrainer(args.config)
    
    # 准备模型和数据
    trainer.prepare_model()
    trainer.load_training_data()
    
    if args.eval_only:
        # 仅评估
        trainer.evaluate()
    else:
        # 训练和评估
        trainer.train()
        trainer.evaluate()

if __name__ == "__main__":
    main()
