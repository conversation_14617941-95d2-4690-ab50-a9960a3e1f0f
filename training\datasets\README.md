# 训练数据集

本目录包含用于模型训练的数据集。

## 目录结构

```
datasets/
├── fault_cases/       # 故障案例数据
│   ├── raw/          # 原始故障案例
│   ├── labeled/      # 标注后的故障案例
│   └── augmented/    # 数据增强后的案例
├── equipment_data/    # 设备数据
│   ├── specifications/ # 设备规格数据
│   ├── manuals/       # 设备手册
│   └── maintenance/   # 维护记录
└── processed/         # 预处理后的训练数据
    ├── embedding_train.jsonl    # 嵌入模型训练数据
    ├── embedding_val.jsonl      # 嵌入模型验证数据
    ├── embedding_test.jsonl     # 嵌入模型测试数据
    ├── fault_train.jsonl        # 故障分类训练数据
    ├── fault_val.jsonl          # 故障分类验证数据
    └── fault_test.jsonl         # 故障分类测试数据
```

## 数据格式

### 嵌入模型训练数据格式
```json
{
  "query": "变压器差动保护动作",
  "positive": "变压器内部故障导致差动保护启动，需要立即停运检查",
  "negative": "断路器机械故障导致无法正常分合闸"
}
```

### 故障分类训练数据格式
```json
{
  "text": "110kV变压器A相绕组对地绝缘电阻下降，差动保护动作跳闸",
  "equipment_type": "transformer",
  "fault_type": "insulation_fault",
  "severity": "critical",
  "labels": ["transformer", "insulation_fault", "critical"]
}
```

## 数据来源

1. **历史故障记录**：从SCADA系统和维护记录中提取
2. **设备手册**：制造商提供的技术文档
3. **专家知识**：领域专家标注的案例
4. **仿真数据**：基于物理模型生成的故障场景

## 数据预处理

### 文本清洗
- 去除无关字符和格式
- 统一术语表达
- 处理缺失值

### 数据标注
- 设备类型标注
- 故障类型分类
- 严重程度评估
- 因果关系标注

### 数据增强
- 同义词替换
- 回译增强
- 上下文扩展
- 负采样生成

## 数据质量控制

### 质量检查
- 标注一致性检查
- 数据完整性验证
- 异常值检测
- 重复数据去除

### 统计信息
- 样本数量分布
- 类别平衡性分析
- 文本长度统计
- 词汇覆盖率

## 使用说明

### 数据准备
```bash
# 从原始数据生成训练集
python scripts/prepare_training_data.py \
  --input data/01_raw \
  --output training/datasets/processed \
  --split_ratio 0.8,0.1,0.1

# 数据质量检查
python scripts/validate_dataset.py \
  --dataset training/datasets/processed \
  --output training/logs/data_quality_report.json
```

### 数据增强
```bash
# 运行数据增强
python scripts/augment_data.py \
  --input training/datasets/processed/fault_train.jsonl \
  --output training/datasets/processed/fault_train_augmented.jsonl \
  --augmentation_ratio 0.5
```

## 注意事项

1. **数据隐私**：所有数据已进行脱敏处理，不包含敏感信息
2. **版权声明**：数据仅用于研究和开发目的
3. **更新频率**：建议定期更新训练数据以保持模型性能
4. **备份策略**：重要数据集应定期备份

## 数据集版本

- v1.0：初始版本，包含基础故障案例
- v1.1：增加设备规格数据和维护记录
- v1.2：添加多模态数据（图像+文本）
- v2.0：大规模数据扩充和质量优化
