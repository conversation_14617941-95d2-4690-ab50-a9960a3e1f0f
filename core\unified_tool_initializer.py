#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一工具初始化器
消除重复的工具初始化代码，提供统一的工具管理接口
"""

import os
from typing import Dict, Any, Optional, Type, List
from loguru import logger
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

from core.dependency_manager import get_dependency_manager


class ToolType(Enum):
    """工具类型"""
    OCR = "ocr"
    IMAGE_PROCESSOR = "image_processor"
    VECTOR_PROCESSOR = "vector_processor"
    KNOWLEDGE_BASE = "knowledge_base"
    RETRIEVER = "retriever"
    CACHE = "cache"
    DATABASE = "database"


@dataclass
class ToolConfig:
    """工具配置"""
    tool_type: ToolType
    config: Dict[str, Any]
    dependencies: List[str]
    fallback_class: Optional[Type] = None
    required: bool = True


class BaseToolInitializer(ABC):
    """工具初始化器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.dependency_manager = get_dependency_manager()
        self._initialized_tools = {}
        self._failed_tools = {}
    
    @abstractmethod
    def initialize(self) -> Any:
        """初始化工具"""
        pass
    
    def check_dependencies(self, dependencies: List[str]) -> bool:
        """检查依赖"""
        for dep in dependencies:
            if not self.dependency_manager.is_available(dep):
                return False
        return True
    
    def get_tool(self, tool_name: str) -> Optional[Any]:
        """获取已初始化的工具"""
        return self._initialized_tools.get(tool_name)
    
    def is_tool_available(self, tool_name: str) -> bool:
        """检查工具是否可用"""
        return tool_name in self._initialized_tools


class OCRInitializer(BaseToolInitializer):
    """OCR工具初始化器"""
    
    def initialize(self) -> Optional[Any]:
        """初始化OCR处理器"""
        try:
            if self.check_dependencies(["paddleocr"]):
                from data_processing.ocr_processor import OCRProcessor
                ocr_config = self.config.get("ocr", {})
                ocr_processor = OCRProcessor(ocr_config)
                self._initialized_tools["ocr_processor"] = ocr_processor
                logger.info("✅ OCR处理器初始化成功")
                return ocr_processor
            else:
                logger.warning("⚠️ OCR依赖不可用，跳过初始化")
                return None
        except Exception as e:
            logger.error(f"❌ OCR处理器初始化失败: {e}")
            self._failed_tools["ocr_processor"] = str(e)
            return None


class ImageProcessorInitializer(BaseToolInitializer):
    """图像处理器初始化器"""
    
    def initialize(self) -> Optional[Any]:
        """初始化图像处理器"""
        try:
            if self.check_dependencies(["opencv"]):
                from data_processing.image_processor import ImageProcessor
                image_config = self.config.get("image_processing", {})
                image_processor = ImageProcessor(image_config)
                self._initialized_tools["image_processor"] = image_processor
                logger.info("✅ 图像处理器初始化成功")
                return image_processor
            else:
                logger.warning("⚠️ 图像处理依赖不可用，跳过初始化")
                return None
        except Exception as e:
            logger.error(f"❌ 图像处理器初始化失败: {e}")
            self._failed_tools["image_processor"] = str(e)
            return None


class VectorProcessorInitializer(BaseToolInitializer):
    """向量处理器初始化器"""
    
    def initialize(self) -> Optional[Any]:
        """初始化向量处理器"""
        try:
            # 向量处理器有多种实现，按优先级尝试
            vector_config = self.config.get("vector_processing", {})
            
            # 尝试使用高级向量处理器
            if self.check_dependencies(["sentence_transformers", "chromadb"]):
                from data_processing.vector_processor import VectorProcessor
                vector_processor = VectorProcessor(vector_config)
                self._initialized_tools["vector_processor"] = vector_processor
                logger.info("✅ 高级向量处理器初始化成功")
                return vector_processor
            
            # 回退到基础向量处理器
            elif self.check_dependencies(["numpy"]):
                from data_processing.vector_processor import VectorProcessor
                basic_config = {**vector_config, "use_basic_mode": True}
                vector_processor = VectorProcessor(basic_config)
                self._initialized_tools["vector_processor"] = vector_processor
                logger.info("✅ 基础向量处理器初始化成功")
                return vector_processor
            
            else:
                logger.warning("⚠️ 向量处理依赖不可用，跳过初始化")
                return None
                
        except Exception as e:
            logger.error(f"❌ 向量处理器初始化失败: {e}")
            self._failed_tools["vector_processor"] = str(e)
            return None


class KnowledgeBaseInitializer(BaseToolInitializer):
    """知识库初始化器"""
    
    def initialize(self) -> Optional[Any]:
        """初始化知识库"""
        try:
            kb_config = self.config.get("knowledge_base", {})
            
            # 尝试使用统一专业检索器
            if self.check_dependencies(["chromadb"]):
                from retriever.unified_professional_retriever import get_unified_retriever
                retriever_config = {
                    "chroma_path": kb_config.get("chroma_path", "./embeddings/chroma_store"),
                    "collection_name": kb_config.get("collection_name", "power_fault_collection"),
                    "cache_enabled": True
                }
                knowledge_base = get_unified_retriever(retriever_config)
                self._initialized_tools["knowledge_base"] = knowledge_base
                logger.info("✅ 统一知识库初始化成功")
                return knowledge_base
            
            else:
                logger.warning("⚠️ 知识库依赖不可用，跳过初始化")
                return None
                
        except Exception as e:
            logger.error(f"❌ 知识库初始化失败: {e}")
            self._failed_tools["knowledge_base"] = str(e)
            return None


class CacheInitializer(BaseToolInitializer):
    """缓存初始化器"""
    
    def initialize(self) -> Optional[Any]:
        """初始化缓存系统"""
        try:
            from utils.optimized_cache import get_cache_manager
            cache_config = self.config.get("cache", {})
            cache_manager = get_cache_manager(cache_config)
            self._initialized_tools["cache_manager"] = cache_manager
            logger.info("✅ 缓存系统初始化成功")
            return cache_manager
        except Exception as e:
            logger.error(f"❌ 缓存系统初始化失败: {e}")
            self._failed_tools["cache_manager"] = str(e)
            return None


class UnifiedToolManager:
    """统一工具管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.initializers = {
            ToolType.OCR: OCRInitializer(config),
            ToolType.IMAGE_PROCESSOR: ImageProcessorInitializer(config),
            ToolType.VECTOR_PROCESSOR: VectorProcessorInitializer(config),
            ToolType.KNOWLEDGE_BASE: KnowledgeBaseInitializer(config),
            ToolType.CACHE: CacheInitializer(config)
        }
        self._tools = {}
        self._initialization_status = {}
    
    def initialize_tool(self, tool_type: ToolType) -> Optional[Any]:
        """初始化指定类型的工具"""
        if tool_type in self._tools:
            return self._tools[tool_type]
        
        initializer = self.initializers.get(tool_type)
        if not initializer:
            logger.error(f"未找到工具类型 {tool_type} 的初始化器")
            return None
        
        tool = initializer.initialize()
        if tool:
            self._tools[tool_type] = tool
            self._initialization_status[tool_type] = True
        else:
            self._initialization_status[tool_type] = False
        
        return tool
    
    def initialize_all_tools(self) -> Dict[ToolType, bool]:
        """初始化所有工具"""
        logger.info("🔧 开始初始化所有工具...")
        
        for tool_type in ToolType:
            self.initialize_tool(tool_type)
        
        # 统计结果
        success_count = sum(1 for status in self._initialization_status.values() if status)
        total_count = len(self._initialization_status)
        
        logger.info(f"🔧 工具初始化完成: {success_count}/{total_count} 成功")
        return self._initialization_status
    
    def get_tool(self, tool_type: ToolType) -> Optional[Any]:
        """获取工具实例"""
        return self._tools.get(tool_type)
    
    def is_tool_available(self, tool_type: ToolType) -> bool:
        """检查工具是否可用"""
        return self._initialization_status.get(tool_type, False)
    
    def get_initialization_report(self) -> Dict[str, Any]:
        """获取初始化报告"""
        return {
            "status": self._initialization_status,
            "available_tools": list(self._tools.keys()),
            "failed_tools": {
                tool_type: initializer._failed_tools 
                for tool_type, initializer in self.initializers.items()
                if initializer._failed_tools
            }
        }


# 全局工具管理器实例
_tool_manager = None


def get_tool_manager(config: Optional[Dict[str, Any]] = None) -> UnifiedToolManager:
    """获取全局工具管理器实例"""
    global _tool_manager
    if _tool_manager is None:
        if config is None:
            from core.unified_config_manager import get_config
            config = get_config().get_all_config()
        _tool_manager = UnifiedToolManager(config)
    return _tool_manager


def initialize_all_tools(config: Optional[Dict[str, Any]] = None) -> Dict[ToolType, bool]:
    """初始化所有工具的便捷函数"""
    return get_tool_manager(config).initialize_all_tools()


def get_tool(tool_type: ToolType) -> Optional[Any]:
    """获取工具的便捷函数"""
    return get_tool_manager().get_tool(tool_type)


def is_tool_available(tool_type: ToolType) -> bool:
    """检查工具是否可用的便捷函数"""
    return get_tool_manager().is_tool_available(tool_type)
