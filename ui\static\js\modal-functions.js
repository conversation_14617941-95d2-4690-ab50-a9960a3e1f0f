/**
 * 模态框和文档/图片管理功能
 * 处理添加文档、添加图片的模态框操作和相关功能
 * 使用统一的状态管理和DOM管理
 */

// 使用统一状态管理替代全局变量
// 通过 window.appState 访问状态

// ==================== 模态框控制 ====================

/**
 * 显示添加文档模态框 - 使用统一DOM管理
 */
function showAddDocumentModal() {
    try {
        const modal = window.domManager.getElement('#addDocumentModal');
        if (modal) {
            // 重置表单
            resetDocumentForm();

            // 使用统一的模态框管理
            const modalId = 'addDocumentModal';
            window.appState.setState(`ui.modals.${modalId}`, { visible: true, timestamp: Date.now() });

            // 显示模态框
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // 默认显示上传标签页
            showDocumentTab('doc-upload');
        } else {
            console.error('找不到添加文档模态框');
            window.errorHandler.handleError({
                type: 'ui',
                message: '找不到添加文档模态框元素',
                context: 'showAddDocumentModal'
            });
        }
    } catch (error) {
        console.error('显示添加文档模态框失败:', error);
        window.errorHandler.handleError(error);
        showNotification('打开添加文档界面失败', 'error');
    }
}

/**
 * 显示添加图片模态框
 */
function showAddImageModal() {
    try {
        const modal = window.domManager.getElement('#addImageModal');
        if (modal) {
            // 重置表单
            resetImageForm();

            // 使用统一的模态框管理
            const modalId = 'addImageModal';
            window.appState.setState(`ui.modals.${modalId}`, { visible: true, timestamp: Date.now() });

            // 显示模态框
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
            // 默认显示上传标签页
            showImageTab('img-upload');
        } else {
            console.error('找不到添加图片模态框');
            window.errorHandler.handleError({
                type: 'ui',
                message: '找不到添加图片模态框元素',
                context: 'showAddImageModal'
            });
        }
    } catch (error) {
        console.error('显示添加图片模态框失败:', error);
        window.errorHandler.handleError(error);
        showNotification('打开添加图片界面失败', 'error');
    }
}

/**
 * 关闭模态框
 */
function closeModal(modalId) {
    try {
        const modal = window.domManager.getElement(`#${modalId}`);
        if (modal) {
            // 更新状态管理
            window.appState.setState(`ui.modals.${modalId}`, { visible: false, timestamp: Date.now() });

            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            if (bootstrapModal) {
                bootstrapModal.hide();
            } else {
                // 如果没有实例，创建一个并隐藏
                const newModal = new bootstrap.Modal(modal);
                newModal.hide();
            }

            // 清理数据
            if (modalId === 'addDocumentModal') {
                resetDocumentForm();
            } else if (modalId === 'addImageModal') {
                resetImageForm();
            }
        } else {
            console.error(`找不到模态框: ${modalId}`);
            window.errorHandler.handleError({
                type: 'ui',
                message: `找不到模态框: ${modalId}`,
                context: 'closeModal'
            });
        }
    } catch (error) {
        console.error('关闭模态框失败:', error);
        window.errorHandler.handleError(error);
    }
}

// ==================== 标签页控制 ====================

/**
 * 显示文档标签页
 */
function showDocumentTab(tabId) {
    try {
        // 隐藏所有标签页内容 - 使用DOMManager
        const tabPanes = window.domManager.getElements('#documentTabContent .tab-pane');
        tabPanes.forEach(pane => {
            window.domManager.removeClass(pane, 'show', 'active');
        });

        // 显示指定标签页
        const targetPane = window.domManager.getElement(`#${tabId}`);
        if (targetPane) {
            window.domManager.addClass(targetPane, 'show', 'active');
        }

        // 更新标签页按钮状态
        const tabButtons = window.domManager.getElements('#documentTabs .nav-link');
        tabButtons.forEach(btn => {
            window.domManager.removeClass(btn, 'active');
        });

        const activeButton = window.domManager.getElement(`#documentTabs .nav-link[onclick*="${tabId}"]`);
        if (activeButton) {
            window.domManager.addClass(activeButton, 'active');
        }

        // 更新状态管理
        window.appState.setState('ui.activeDocumentTab', tabId);
    } catch (error) {
        console.error('切换文档标签页失败:', error);
        window.errorHandler.handleError(error);
    }
}

/**
 * 显示图片标签页
 */
function showImageTab(tabId) {
    try {
        // 隐藏所有标签页内容 - 使用DOMManager
        const tabPanes = window.domManager.getElements('#imageTabContent .tab-pane');
        tabPanes.forEach(pane => {
            window.domManager.removeClass(pane, 'show', 'active');
        });

        // 显示指定标签页
        const targetPane = window.domManager.getElement(`#${tabId}`);
        if (targetPane) {
            window.domManager.addClass(targetPane, 'show', 'active');
        }

        // 更新标签页按钮状态
        const tabButtons = window.domManager.getElements('#imageTabs .nav-link');
        tabButtons.forEach(btn => {
            window.domManager.removeClass(btn, 'active');
        });

        const activeButton = window.domManager.getElement(`#imageTabs .nav-link[onclick*="${tabId}"]`);
        if (activeButton) {
            window.domManager.addClass(activeButton, 'active');
        }

        // 更新状态管理
        window.appState.setState('ui.activeImageTab', tabId);
    } catch (error) {
        console.error('切换图片标签页失败:', error);
        window.errorHandler.handleError(error);
    }
}

// ==================== 兼容性函数 ====================

/**
 * 兼容旧版本的标签页切换函数
 */
function switchDocTab(targetTab) {
    showDocumentTab(targetTab);
}

/**
 * 兼容旧版本的标签页切换函数
 */
function switchImgTab(targetTab) {
    showImageTab(targetTab);
}

// ==================== 流程管理 ====================

/**
 * 检查文档是否可以进行标注
 */
function canProceedToDocumentAnnotation() {
    if (currentDocumentFiles.length === 0) {
        showNotification('请先上传文档文件', 'warning');
        return false;
    }

    const title = window.domManager.getValue('#doc-title')?.trim();
    const category = window.domManager.getValue('#doc-category')?.trim();

    if (!title) {
        showNotification('请先填写文档标题', 'warning');
        const titleInput = window.domManager.getElement('#doc-title');
        if (titleInput) titleInput.focus();
        return false;
    }

    if (!category) {
        showNotification('请先选择文档类别', 'warning');
        const categoryInput = window.domManager.getElement('#doc-category');
        if (categoryInput) categoryInput.focus();
        return false;
    }

    return true;
}

/**
 * 检查图片是否可以进行标注
 */
function canProceedToImageAnnotation() {
    if (currentImageFiles.length === 0) {
        showNotification('请先上传图片文件', 'warning');
        return false;
    }

    const title = window.domManager.getValue('#img-title')?.trim();
    const category = window.domManager.getValue('#img-category')?.trim();

    if (!title) {
        showNotification('请先填写图片标题', 'warning');
        const titleInput = window.domManager.getElement('#img-title');
        if (titleInput) titleInput.focus();
        return false;
    }

    if (!category) {
        showNotification('请先选择图片类别', 'warning');
        const categoryInput = window.domManager.getElement('#img-category');
        if (categoryInput) categoryInput.focus();
        return false;
    }

    return true;
}

/**
 * 进入文档标注流程
 */
function proceedToDocumentAnnotation() {
    if (canProceedToDocumentAnnotation()) {
        showDocumentTab('doc-annotation');
        showNotification('已进入标注阶段，可以开始标注文档', 'info');
    }
}

/**
 * 进入图片标注流程
 */
function proceedToImageAnnotation() {
    if (canProceedToImageAnnotation()) {
        showImageTab('img-annotation');
        showNotification('已进入标注阶段，可以开始标注图片', 'info');
    }
}

/**
 * 进入数据清洗流程
 */
function proceedToDataCleaning(type) {
    if (type === 'document') {
        if (canProceedToDocumentAnnotation()) {
            showDocumentTab('doc-cleaning');
            showNotification('已进入清洗阶段，可以进行数据清洗', 'info');
        }
    } else if (type === 'image') {
        if (canProceedToImageAnnotation()) {
            showImageTab('img-processing');
            showNotification('已进入处理阶段，可以进行图像处理', 'info');
        }
    }
}

// ==================== 文件处理 ====================

/**
 * 处理文档文件选择（modal版本）
 */
function handleDocFileSelect_modal(input) {
    try {
        const files = Array.from(input.files);
        currentDocumentFiles = files;

        if (files.length > 0) {
            displayDocumentPreview(files);
            showNotification(`已选择 ${files.length} 个文件，请填写基本信息后进行标注`, 'success');
            // 不自动切换标签页，让用户先填写基本信息
        }
    } catch (error) {
        console.error('处理文档文件选择失败:', error);
        showNotification('文件选择失败', 'error');
    }
}

/**
 * 处理图片文件选择（modal版本）
 */
function handleImgFileSelect_modal(input) {
    try {
        const files = Array.from(input.files);
        currentImageFiles = files;

        if (files.length > 0) {
            displayImagePreview(files);
            showNotification(`已选择 ${files.length} 张图片，请填写基本信息后进行标注`, 'success');
            // 不自动切换标签页，让用户先填写基本信息
        }
    } catch (error) {
        console.error('处理图片文件选择失败:', error);
        showNotification('文件选择失败', 'error');
    }
}

/**
 * 显示文档预览
 */
function displayDocumentPreview(files) {
    try {
        const previewContainer = document.getElementById('document-preview-container');
        const previewList = document.getElementById('document-preview-list');
        
        if (!previewContainer || !previewList) {
            console.error('找不到文档预览容器');
            return;
        }
        
        previewList.innerHTML = '';
        
        files.forEach((file, index) => {
            const previewItem = document.createElement('div');
            previewItem.className = 'col-12 mb-2';
            previewItem.innerHTML = `
                <div class="card border-success">
                    <div class="card-body p-2">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-file-text text-success me-2"></i>
                            <div class="flex-grow-1">
                                <small class="fw-semibold">${file.name}</small>
                                <br>
                                <small class="text-muted">${formatFileSize(file.size)}</small>
                            </div>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeDocumentFile(${index})">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            previewList.appendChild(previewItem);
        });
        
        previewContainer.classList.remove('d-none');
    } catch (error) {
        console.error('显示文档预览失败:', error);
    }
}

/**
 * 显示图片预览
 */
function displayImagePreview(files) {
    try {
        const previewContainer = document.getElementById('image-preview-container');
        const previewList = document.getElementById('image-preview-list');
        
        if (!previewContainer || !previewList) {
            console.error('找不到图片预览容器');
            return;
        }
        
        previewList.innerHTML = '';
        
        files.forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewItem = document.createElement('div');
                    previewItem.className = 'col-md-6 col-lg-4';
                    previewItem.innerHTML = `
                        <div class="card border-success">
                            <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                            <div class="card-body p-2">
                                <small class="fw-semibold d-block">${file.name}</small>
                                <small class="text-muted">${formatFileSize(file.size)}</small>
                                <button type="button" class="btn btn-outline-danger btn-sm float-end" onclick="removeImageFile(${index})">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    previewList.appendChild(previewItem);
                };
                reader.readAsDataURL(file);
            }
        });
        
        previewContainer.classList.remove('d-none');
    } catch (error) {
        console.error('显示图片预览失败:', error);
    }
}

// ==================== 工具函数 ====================

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 移除文档文件
 */
function removeDocumentFile(index) {
    try {
        currentDocumentFiles.splice(index, 1);
        if (currentDocumentFiles.length > 0) {
            displayDocumentPreview(currentDocumentFiles);
        } else {
            const previewContainer = document.getElementById('document-preview-container');
            if (previewContainer) {
                previewContainer.classList.add('d-none');
            }
            // 重置文件输入
            const fileInput = document.getElementById('doc-file');
            if (fileInput) {
                fileInput.value = '';
            }
        }
    } catch (error) {
        console.error('移除文档文件失败:', error);
    }
}

/**
 * 移除图片文件
 */
function removeImageFile(index) {
    try {
        currentImageFiles.splice(index, 1);
        if (currentImageFiles.length > 0) {
            displayImagePreview(currentImageFiles);
        } else {
            const previewContainer = document.getElementById('image-preview-container');
            if (previewContainer) {
                previewContainer.classList.add('d-none');
            }
            // 重置文件输入
            const fileInput = document.getElementById('img-file');
            if (fileInput) {
                fileInput.value = '';
            }
        }
    } catch (error) {
        console.error('移除图片文件失败:', error);
    }
}

/**
 * 重置文档表单
 */
function resetDocumentForm() {
    try {
        currentDocumentFiles = [];
        currentAnnotations = [];
        
        // 重置文件输入
        const fileInput = document.getElementById('doc-file');
        if (fileInput) {
            fileInput.value = '';
        }
        
        // 隐藏预览
        const previewContainer = document.getElementById('document-preview-container');
        if (previewContainer) {
            previewContainer.classList.add('d-none');
        }
        
        // 重置表单字段
        const form = document.getElementById('add-document-form');
        if (form) {
            form.reset();
        }
    } catch (error) {
        console.error('重置文档表单失败:', error);
    }
}

/**
 * 重置图片表单
 */
function resetImageForm() {
    try {
        currentImageFiles = [];
        currentAnnotations = [];
        
        // 重置文件输入
        const fileInput = document.getElementById('img-file');
        if (fileInput) {
            fileInput.value = '';
        }
        
        // 隐藏预览
        const previewContainer = document.getElementById('image-preview-container');
        if (previewContainer) {
            previewContainer.classList.add('d-none');
        }
        
        // 重置表单字段
        const form = document.getElementById('add-image-form');
        if (form) {
            form.reset();
        }
    } catch (error) {
        console.error('重置图片表单失败:', error);
    }
}

// ==================== 文档和图片保存 ====================

/**
 * 保存文档（带标注）
 */
async function addDocumentWithAnnotation() {
    try {

        if (currentDocumentFiles.length === 0) {
            showNotification('请先选择文档文件', 'warning');
            console.error('❌ 没有选择文档文件');
            return;
        }

        const formData = new FormData();

        // 添加文件
        currentDocumentFiles.forEach((file, index) => {

            formData.append('files', file);
        });

        // 添加表单数据 - 使用DOMManager
        const title = window.domManager.getValue('#doc-title') || '';
        const category = window.domManager.getValue('#doc-category') || '';
        const content = window.domManager.getValue('#doc-content') || '';
        const equipmentType = window.domManager.getValue('#doc-equipment-type') || '';
        const voltageLevel = window.domManager.getValue('#doc-voltage-level') || '';
        const faultType = window.domManager.getValue('#doc-fault-type') || '';
        const tags = window.domManager.getValue('#doc-tags') || '';

        formData.append('title', title);
        formData.append('category', category);
        formData.append('content', content);
        formData.append('equipment_type', equipmentType);
        formData.append('voltage_level', voltageLevel);
        formData.append('fault_type', faultType);
        formData.append('tags', tags);
        formData.append('created_at', new Date().toISOString());

        // 显示加载状态
        const saveButton = document.querySelector('button[onclick="addDocumentWithAnnotation()"]');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> 保存中...';
        saveButton.disabled = true;

        // 发送请求
        const response = await fetch('/api/v1/knowledge/documents/add', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            showNotification('文档保存成功！', 'success');
            closeModal('addDocumentModal');
            // 刷新知识库列表
            if (typeof refreshKnowledgeBase === 'function') {
                refreshKnowledgeBase();
            }
        } else {
            const error = await response.json();
            showNotification(`保存失败: ${error.message || '未知错误'}`, 'error');
        }
    } catch (error) {
        console.error('保存文档失败:', error);
        showNotification('保存文档时发生错误', 'error');
    } finally {
        // 恢复按钮状态
        const saveButton = document.querySelector('button[onclick="addDocumentWithAnnotation()"]');
        if (saveButton) {
            saveButton.innerHTML = '<i class="bi bi-cloud-upload me-1"></i> 保存文档';
            saveButton.disabled = false;
        }
    }
}

/**
 * 保存图片（带标注）
 */
async function addImageWithAnnotation() {
    try {

        if (currentImageFiles.length === 0) {
            showNotification('请先选择图片文件', 'warning');
            console.error('❌ 没有选择图片文件');
            return;
        }

        const formData = new FormData();

        // 添加文件
        currentImageFiles.forEach((file, index) => {

            formData.append('files', file);
        });

        // 添加表单数据 - 使用DOMManager
        const title = window.domManager.getValue('#img-title') || '';
        const category = window.domManager.getValue('#img-category') || '';
        const equipmentType = window.domManager.getValue('#img-equipment-type') || '';
        const equipment = window.domManager.getValue('#img-equipment') || '';
        const location = window.domManager.getValue('#img-location') || '';
        const description = window.domManager.getValue('#img-description') || '';
        const tags = window.domManager.getValue('#img-tags') || '';

        formData.append('title', title);
        formData.append('category', category);
        formData.append('equipment_type', equipmentType);
        formData.append('equipment', equipment);
        formData.append('location', location);
        formData.append('description', description);
        formData.append('tags', tags);
        formData.append('created_at', new Date().toISOString());

        // 添加标注数据
        if (currentAnnotations.length > 0) {
            formData.append('annotations', JSON.stringify(currentAnnotations));
        }

        // 显示加载状态
        const saveButton = document.querySelector('button[onclick="addImageWithAnnotation()"]');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="bi bi-hourglass-split me-1"></i> 保存中...';
        saveButton.disabled = true;

        // 发送请求
        const response = await fetch('/api/v1/knowledge/images/add', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            showNotification('图片保存成功！', 'success');
            closeModal('addImageModal');
            // 刷新知识库列表
            if (typeof refreshKnowledgeBase === 'function') {
                refreshKnowledgeBase();
            }
        } else {
            const error = await response.json();
            showNotification(`保存失败: ${error.message || '未知错误'}`, 'error');
        }
    } catch (error) {
        console.error('保存图片失败:', error);
        showNotification('保存图片时发生错误', 'error');
    } finally {
        // 恢复按钮状态
        const saveButton = document.querySelector('button[onclick="addImageWithAnnotation()"]');
        if (saveButton) {
            saveButton.innerHTML = '<i class="bi bi-cloud-upload me-1"></i> 保存图片';
            saveButton.disabled = false;
        }
    }
}

// ==================== 标注功能 ====================

/**
 * 设置标注工具
 */
function setAnnotationTool(tool) {
    try {
        // 更新工具按钮状态
        const toolButtons = document.querySelectorAll('[id$="-tool"]');
        toolButtons.forEach(btn => {
            btn.classList.remove('active');
        });

        const activeButton = document.getElementById(`${tool}-tool`);
        if (activeButton) {
            activeButton.classList.add('active');
        }

        // 设置当前工具
        window.currentAnnotationTool = tool;
        showNotification(`已选择${tool === 'rect' ? '矩形' : tool === 'circle' ? '圆形' : '多边形'}工具`, 'info');
    } catch (error) {
        console.error('设置标注工具失败:', error);
    }
}

/**
 * 运行OCR识别
 */
async function runOCR() {
    try {
        if (currentImageFiles.length === 0) {
            showNotification('请先上传图片', 'warning');
            return;
        }

        showNotification('正在进行OCR识别...', 'info');

        const formData = new FormData();
        formData.append('image', currentImageFiles[0]);

        const response = await fetch('/api/v1/knowledge/images/ocr', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            displayOCRResults(result.text_blocks || []);
            showNotification('OCR识别完成', 'success');
        } else {
            showNotification('OCR识别失败', 'error');
        }
    } catch (error) {
        console.error('OCR识别失败:', error);
        showNotification('OCR识别时发生错误', 'error');
    }
}

/**
 * 显示OCR结果
 */
function displayOCRResults(textBlocks) {
    try {
        const resultsContainer = document.getElementById('ocr-results');
        if (!resultsContainer) return;

        if (textBlocks.length === 0) {
            resultsContainer.innerHTML = '<div class="text-muted text-center py-3"><i class="bi bi-info-circle me-1"></i>未识别到文字</div>';
            return;
        }

        let html = '';
        textBlocks.forEach((block, index) => {
            html += `
                <div class="border-bottom pb-2 mb-2">
                    <small class="text-primary fw-semibold">文本块 ${index + 1}:</small>
                    <div class="mt-1">${block.text}</div>
                    <small class="text-muted">置信度: ${(block.confidence * 100).toFixed(1)}%</small>
                </div>
            `;
        });

        resultsContainer.innerHTML = html;
    } catch (error) {
        console.error('显示OCR结果失败:', error);
    }
}

/**
 * 检测缺陷
 */
async function detectDefects() {
    try {
        if (currentImageFiles.length === 0) {
            showNotification('请先上传图片', 'warning');
            return;
        }

        showNotification('正在进行缺陷检测...', 'info');

        const formData = new FormData();
        formData.append('image', currentImageFiles[0]);

        const response = await fetch('/api/v1/knowledge/images/detect', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            displayDefectResults(result.defects || []);
            showNotification('缺陷检测完成', 'success');
        } else {
            showNotification('缺陷检测失败', 'error');
        }
    } catch (error) {
        console.error('缺陷检测失败:', error);
        showNotification('缺陷检测时发生错误', 'error');
    }
}

/**
 * 显示缺陷检测结果
 */
function displayDefectResults(defects) {
    try {
        const listContainer = document.getElementById('annotation-list');
        if (!listContainer) return;

        if (defects.length === 0) {
            listContainer.innerHTML = '<div class="text-muted text-center py-3"><i class="bi bi-info-circle me-1"></i>未检测到缺陷</div>';
            return;
        }

        let html = '';
        defects.forEach((defect, index) => {
            html += `
                <div class="border-bottom pb-2 mb-2">
                    <small class="text-danger fw-semibold">缺陷 ${index + 1}:</small>
                    <div class="mt-1">${defect.type}</div>
                    <small class="text-muted">置信度: ${(defect.confidence * 100).toFixed(1)}%</small>
                    <small class="text-muted d-block">位置: (${defect.x}, ${defect.y})</small>
                </div>
            `;
        });

        listContainer.innerHTML = html;
        currentAnnotations = defects;
    } catch (error) {
        console.error('显示缺陷检测结果失败:', error);
    }
}

/**
 * 清除标注
 */
function clearAnnotations() {
    try {
        currentAnnotations = [];

        const listContainer = document.getElementById('annotation-list');
        if (listContainer) {
            listContainer.innerHTML = '<div class="text-muted text-center py-3"><i class="bi bi-info-circle me-1"></i>标注信息将显示在这里</div>';
        }

        const ocrResults = document.getElementById('ocr-results');
        if (ocrResults) {
            ocrResults.innerHTML = '<div class="text-muted text-center py-3"><i class="bi bi-info-circle me-1"></i>点击"OCR识别"提取文字</div>';
        }

        showNotification('已清除所有标注', 'info');
    } catch (error) {
        console.error('清除标注失败:', error);
    }
}

// ==================== 图像处理功能 ====================

/**
 * 调整图像亮度
 */
function adjustImageBrightness(value) {
    try {
        showNotification(`亮度调整为: ${value}`, 'info');
        // TODO: 实现实际的图像亮度调整
    } catch (error) {
        console.error('调整图像亮度失败:', error);
    }
}

/**
 * 调整图像对比度
 */
function adjustImageContrast(value) {
    try {
        showNotification(`对比度调整为: ${value}`, 'info');
        // TODO: 实现实际的图像对比度调整
    } catch (error) {
        console.error('调整图像对比度失败:', error);
    }
}

/**
 * 切换图像滤镜
 */
function toggleImageFilter(filterType, enabled) {
    try {
        const filterName = {
            'sharpen': '锐化',
            'denoise': '降噪',
            'edge': '边缘增强'
        }[filterType] || filterType;

        showNotification(`${enabled ? '启用' : '禁用'}${filterName}滤镜`, 'info');
        // TODO: 实现实际的滤镜切换
    } catch (error) {
        console.error('切换图像滤镜失败:', error);
    }
}

/**
 * 重置图像处理
 */
function resetImageProcessing() {
    try {
        // 重置滑块
        const brightnessSlider = document.getElementById('brightness-slider');
        const contrastSlider = document.getElementById('contrast-slider');

        if (brightnessSlider) brightnessSlider.value = 0;
        if (contrastSlider) contrastSlider.value = 1;

        // 重置复选框
        const checkboxes = ['sharpen-filter', 'noise-reduction', 'edge-enhance'];
        checkboxes.forEach(id => {
            const checkbox = document.getElementById(id);
            if (checkbox) checkbox.checked = false;
        });

        showNotification('图像处理已重置', 'info');
        // TODO: 实现实际的图像重置
    } catch (error) {
        console.error('重置图像处理失败:', error);
    }
}

/**
 * 分析图像质量
 */
async function analyzeImageQuality() {
    try {
        if (currentImageFiles.length === 0) {
            showNotification('请先上传图片', 'warning');
            return;
        }

        showNotification('正在分析图像质量...', 'info');

        // 模拟分析结果
        setTimeout(() => {
            const clarity = Math.random() * 100;
            const saturation = Math.random() * 100;
            const noise = Math.random() * 50;
            const quality = (clarity + saturation + (100 - noise)) / 3;

            updateQualityDisplay(clarity, saturation, noise, quality);
            showNotification('图像质量分析完成', 'success');
        }, 2000);

        // TODO: 实现实际的图像质量分析
    } catch (error) {
        console.error('分析图像质量失败:', error);
        showNotification('图像质量分析失败', 'error');
    }
}

/**
 * 更新质量显示
 */
function updateQualityDisplay(clarity, saturation, noise, quality) {
    try {
        const clarityBar = document.getElementById('clarity-bar');
        const saturationBar = document.getElementById('saturation-bar');
        const noiseBar = document.getElementById('noise-bar');
        const qualityScore = document.getElementById('image-quality-score');

        if (clarityBar) clarityBar.style.width = `${clarity}%`;
        if (saturationBar) saturationBar.style.width = `${saturation}%`;
        if (noiseBar) noiseBar.style.width = `${noise}%`;
        if (qualityScore) qualityScore.textContent = Math.round(quality);
    } catch (error) {
        console.error('更新质量显示失败:', error);
    }
}

/**
 * 导出处理后的图像
 */
function exportProcessedImage(format) {
    try {
        if (currentImageFiles.length === 0) {
            showNotification('请先上传图片', 'warning');
            return;
        }

        showNotification(`正在导出${format.toUpperCase()}格式图像...`, 'info');

        // TODO: 实现实际的图像导出
        setTimeout(() => {
            showNotification(`${format.toUpperCase()}格式图像导出完成`, 'success');
        }, 1000);
    } catch (error) {
        console.error('导出图像失败:', error);
        showNotification('导出图像失败', 'error');
    }
}

/**
 * 导出标注数据
 */
function exportAnnotations() {
    try {
        if (currentAnnotations.length === 0) {
            showNotification('没有标注数据可导出', 'warning');
            return;
        }

        const dataStr = JSON.stringify(currentAnnotations, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `annotations_${new Date().getTime()}.json`;
        link.click();

        showNotification('标注数据导出完成', 'success');
    } catch (error) {
        console.error('导出标注数据失败:', error);
        showNotification('导出标注数据失败', 'error');
    }
}

// ==================== 其他功能 ====================

/**
 * 编辑设备
 */
function editEquipment() {
    try {
        showNotification('设备编辑功能开发中...', 'info');
        // TODO: 实现设备编辑功能
    } catch (error) {
        console.error('编辑设备失败:', error);
    }
}

// ==================== 页面初始化 ====================

/**
 * 初始化标签页系统
 */
function initializeTabs() {
    try {

        // 添加文档标签页事件监听器
        document.querySelectorAll('#documentTabs .nav-link').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                const targetTab = this.getAttribute('onclick').match(/showDocumentTab\('([^']+)'\)/);
                if (targetTab) {
                    showDocumentTab(targetTab[1]);
                }
            });
        });

        // 添加图片标签页事件监听器
        document.querySelectorAll('#imageTabs .nav-link').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                const targetTab = this.getAttribute('onclick').match(/showImageTab\('([^']+)'\)/);
                if (targetTab) {
                    showImageTab(targetTab[1]);
                }
            });
        });

    } catch (error) {
        console.error('初始化标签页系统失败:', error);
    }
}

/**
 * 验证标签页功能
 */
function validateTabFunctionality() {
    try {

        // 检查文档标签页
        const docTabs = document.querySelectorAll('#documentTabs .nav-link');

        // 检查图片标签页
        const imgTabs = document.querySelectorAll('#imageTabs .nav-link');

    } catch (error) {
        console.error('验证标签页功能失败:', error);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {

    initializeTabs();

    // 延迟验证
    setTimeout(() => {
        validateTabFunctionality();
    }, 500);
});

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    try {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    } catch (error) {
        console.error('显示通知失败:', error);
    }
}
