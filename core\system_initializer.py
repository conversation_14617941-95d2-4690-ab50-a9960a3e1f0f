#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统初始化器
统一管理系统启动流程，整合健康检查、依赖检查、服务初始化等
提供生产环境友好的启动和错误处理机制
"""

import os
import sys
import socket
from typing import Dict, Any, List, Optional
from pathlib import Path
from loguru import logger

from .dependency_manager import get_dependency_manager
from .service_manager import get_service_manager
from .config_manager import get_config


class SystemInitializer:
    """系统初始化器"""
    
    def __init__(self):
        self.config = get_config()
        self.dependency_manager = get_dependency_manager()
        self.service_manager = get_service_manager()
        self.initialization_steps = []
        
    def check_system_health(self) -> Dict[str, Any]:
        """系统健康检查"""
        logger.info("🔍 执行系统健康检查...")
        
        health_status = {
            "python_version": sys.version,
            "platform": sys.platform,
            "working_directory": os.getcwd(),
            "project_root": str(Path(__file__).parent.parent),
            "memory_available": True,
            "disk_space": True,
            "network": True,
            "permissions": True
        }
        
        # 检查网络连接
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            health_status['network'] = True
            logger.debug("✅ 网络连接正常")
        except Exception:
            health_status['network'] = False
            logger.warning("⚠️ 网络连接异常")
        
        # 检查文件权限
        try:
            test_file = Path("temp_permission_test.tmp")
            test_file.write_text("test")
            test_file.unlink()
            health_status['permissions'] = True
            logger.debug("✅ 文件权限正常")
        except Exception:
            health_status['permissions'] = False
            logger.warning("⚠️ 文件权限异常")
        
        # 检查磁盘空间（简化检查）
        try:
            import shutil
            free_space = shutil.disk_usage('.').free
            if free_space < 100 * 1024 * 1024:  # 100MB
                health_status['disk_space'] = False
                logger.warning("⚠️ 磁盘空间不足")
            else:
                logger.debug("✅ 磁盘空间充足")
        except Exception:
            logger.debug("⚠️ 无法检查磁盘空间")
        
        return health_status
    
    def create_directories(self) -> None:
        """创建必要的目录结构"""
        logger.info("📁 创建目录结构...")
        
        directories = [
            "logs", "uploads", "cache", "embeddings", 
            "knowledge_base", "data", "static", "temp"
        ]
        
        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
                logger.debug(f"✅ 目录创建: {directory}")
            except Exception as e:
                logger.error(f"❌ 目录创建失败 {directory}: {e}")
                raise
        
        logger.info("✅ 目录结构创建完成")
    
    def setup_logging(self) -> None:
        """设置日志系统"""
        logger.info("📝 配置日志系统...")
        
        try:
            # 确保日志目录存在
            os.makedirs("logs", exist_ok=True)
            
            # 配置日志
            log_level = self.config.get("system.log_level", "INFO")
            
            # 添加文件日志
            logger.add(
                "logs/system.log",
                rotation="1 day",
                retention="30 days",
                level=log_level,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
            )
            
            # 添加错误日志
            logger.add(
                "logs/error.log",
                rotation="1 day",
                retention="30 days",
                level="ERROR",
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
            )
            
            logger.info("✅ 日志系统配置完成")
            
        except Exception as e:
            logger.error(f"❌ 日志系统配置失败: {e}")
            raise
    
    def initialize_core_services(self) -> None:
        """初始化核心服务"""
        logger.info("🔧 初始化核心服务...")
        
        try:
            # 注册核心服务工厂
            self._register_core_service_factories()
            
            # 预加载关键服务
            self._preload_critical_services()
            
            logger.info("✅ 核心服务初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 核心服务初始化失败: {e}")
            raise
    
    def _register_core_service_factories(self) -> None:
        """注册核心服务工厂"""
        
        # 故障分析器
        def create_fault_analyzer():
            from core.fault_analyzer import FaultAnalyzer
            return FaultAnalyzer()
        
        # 设备管理器
        def create_equipment_manager():
            from core.equipment_manager import EquipmentManager
            return EquipmentManager()
        
        # 知识库 - 使用统一检索器
        def create_knowledge_base():
            try:
                from retriever.unified_professional_retriever import get_unified_retriever
                config = {
                    "chroma_path": "./embeddings/chroma_store_new",
                    "collection_name": "baiyin_power_fault_collection",
                    "cache_enabled": True
                }
                return get_unified_retriever(config)
            except ImportError:
                logger.warning("统一检索器不可用，创建模拟实例")
                return None
        
        # 注册服务工厂
        self.service_manager.register_factory("fault_analyzer", create_fault_analyzer)
        self.service_manager.register_factory("equipment_manager", create_equipment_manager)
        self.service_manager.register_factory("knowledge_base", create_knowledge_base)
    
    def _preload_critical_services(self) -> None:
        """预加载关键服务"""
        critical_services = ["fault_analyzer", "equipment_manager"]
        
        for service_name in critical_services:
            try:
                service = self.service_manager.get_service(service_name)
                if service:
                    logger.debug(f"✅ 预加载服务: {service_name}")
                else:
                    logger.warning(f"⚠️ 服务预加载失败: {service_name}")
            except Exception as e:
                logger.error(f"❌ 服务预加载错误 {service_name}: {e}")
    
    def print_startup_banner(self) -> None:
        """打印启动横幅"""
        config = self.config

        logger.info("\n" + "="*70)
        logger.info("🚀 故障分析智能助手系统")
        logger.info("="*70)
        logger.info(f"📊 版本: {config.get('system.version', '2.0.0')}")
        logger.info(f"🌐 服务地址: http://{config.get('server.host', '0.0.0.0')}:{config.get('server.port', 5002)}")
        logger.info(f"🔧 运行模式: {'生产模式' if not config.is_debug() else '调试模式'}")
        logger.info(f"📁 工作目录: {os.getcwd()}")
        
        # 显示功能状态
        features = self.dependency_manager.get_feature_availability()
        logger.info(f"📡 WebSocket支持: {'✅' if features.get('websocket') else '❌'}")
        logger.info(f"🤖 AI分析: {'✅' if features.get('ai_analysis') else '❌'}")
        logger.debug(f"🔍 向量检索: {'✅' if features.get('vector_search') else '❌'}")
        logger.info(f"📊 数据处理: {'✅' if features.get('data_processing') else '❌'}")
        logger.info(f"🖼️ 图像处理: {'✅' if features.get('image_processing') else '❌'}")
        logger.info(f"📝 OCR识别: {'✅' if features.get('ocr_recognition') else '❌'}")
        
        logger.info("="*70)
    
    def full_initialization(self) -> bool:
        """完整的系统初始化流程"""
        try:
            logger.info("🚀 开始系统初始化...")
            
            # 1. 设置日志
            self.setup_logging()
            
            # 2. 系统健康检查
            health_status = self.check_system_health()
            
            # 3. 依赖检查
            success, dep_results = self.dependency_manager.check_all_dependencies()
            if not success:
                logger.error("❌ 关键依赖缺失，系统无法启动")
                return False
            
            # 4. 创建目录
            self.create_directories()
            
            # 5. 初始化核心服务
            self.initialize_core_services()
            
            # 6. 打印启动信息
            self.print_startup_banner()
            
            logger.info("✅ 系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def cleanup(self) -> None:
        """系统清理"""
        logger.info("🧹 执行系统清理...")
        
        try:
            # 清理服务
            self.service_manager.clear_all()
            
            # 清理临时文件
            temp_dir = Path("temp")
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
            
            logger.info("✅ 系统清理完成")
            
        except Exception as e:
            logger.error(f"❌ 系统清理失败: {e}")


# 全局系统初始化器实例
_system_initializer = None


def get_system_initializer() -> SystemInitializer:
    """获取全局系统初始化器实例"""
    global _system_initializer
    if _system_initializer is None:
        _system_initializer = SystemInitializer()
    return _system_initializer


def initialize_system() -> bool:
    """系统初始化的便捷函数"""
    return get_system_initializer().full_initialization()


def cleanup_system() -> None:
    """系统清理的便捷函数"""
    get_system_initializer().cleanup()
