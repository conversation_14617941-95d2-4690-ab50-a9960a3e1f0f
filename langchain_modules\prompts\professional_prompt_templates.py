#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业级提示词模板系统 - 白银市电力故障诊断系统
基于最佳实践重新设计的提示词工程框架，确保输出质量达到专业级水准

核心特性：
1. 结构化输出格式（JSON Schema验证）
2. Few-shot学习示例
3. Chain-of-Thought推理链
4. 专业术语标准化
5. 多模态支持
6. 质量控制机制
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
try:
    from langchain.prompts import PromptTemplate
    from langchain.schema import BaseOutputParser
    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 创建简单的替代类
    class PromptTemplate:
        def __init__(self, template: str, input_variables: list):
            self.template = template
            self.input_variables = input_variables

        def format(self, **kwargs):
            return self.template.format(**kwargs)

    class BaseOutputParser:
        def parse(self, text: str):
            return {"raw_output": text}

        @property
        def _type(self) -> str:
            return "base_output_parser"

    LANGCHAIN_AVAILABLE = False

try:
    from pydantic import BaseModel, Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    # 创建简单的替代类
    class BaseModel:
        pass

    def Field(**kwargs):
        return None

    PYDANTIC_AVAILABLE = False

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """模型类型枚举"""
    DEEPSEEK_R1 = "deepseek-r1"
    DEEPSEEK_V3 = "deepseek-v3"
    GENERAL = "general"


class OutputFormat(Enum):
    """输出格式枚举"""
    JSON = "json"
    STRUCTURED_TEXT = "structured_text"
    MARKDOWN = "markdown"
    TECHNICAL_REPORT = "technical_report"


@dataclass
class PromptConfig:
    """提示词配置"""
    model_type: ModelType
    output_format: OutputFormat
    use_few_shot: bool = True
    use_cot: bool = True
    max_tokens: int = 4000
    temperature: float = 0.3
    quality_threshold: float = 0.85


class FaultAnalysisOutput(BaseModel):
    """故障分析输出模型"""
    fault_classification: Dict[str, Any] = Field(description="故障分类信息")
    equipment_analysis: Dict[str, Any] = Field(description="设备分析")
    technical_analysis: Dict[str, Any] = Field(description="技术分析")
    root_cause_analysis: Dict[str, Any] = Field(description="根因分析")
    recommendations: Dict[str, Any] = Field(description="处理建议")
    risk_assessment: Dict[str, Any] = Field(description="风险评估")
    confidence_score: float = Field(description="置信度分数", ge=0.0, le=1.0)
    analysis_metadata: Dict[str, Any] = Field(description="分析元数据")


class ProfessionalPromptTemplates:
    """专业级提示词模板系统"""
    
    def __init__(self):
        self.templates = {}
        self.few_shot_examples = {}
        self.output_parsers = {}
        
        # 电力系统专业知识库
        self.power_system_knowledge = {
            "equipment_types": {
                "变压器": {
                    "subtypes": ["电力变压器", "配电变压器", "特种变压器"],
                    "key_parameters": ["额定容量", "额定电压", "阻抗电压", "损耗"],
                    "common_faults": ["绝缘击穿", "过热", "局部放电", "油质劣化"]
                },
                "断路器": {
                    "subtypes": ["SF6断路器", "真空断路器", "油断路器"],
                    "key_parameters": ["额定电流", "开断电流", "操作电压", "机械寿命"],
                    "common_faults": ["拒动", "误动", "接触不良", "绝缘下降"]
                }
            },
            "fault_categories": {
                "电气故障": ["短路", "接地", "断线", "绝缘击穿"],
                "机械故障": ["卡涩", "变形", "磨损", "松动"],
                "热故障": ["过热", "温升异常", "散热不良"],
                "化学故障": ["油质劣化", "气体超标", "腐蚀"]
            },
            "technical_standards": {
                "国标": ["GB/T 1094", "GB/T 11022", "GB/T 16927"],
                "行标": ["DL/T 596", "DL/T 1071", "DL/T 1984"],
                "国际标准": ["IEC 60076", "IEC 62271", "IEEE C37"]
            }
        }
        
        self._initialize_templates()
        self._initialize_few_shot_examples()
        self._initialize_output_parsers()
    
    def _initialize_templates(self):
        """初始化提示词模板"""
        
        # DeepSeek-R1 专业故障分析模板
        self.templates["deepseek_r1_fault_analysis"] = PromptTemplate(
            template="""# 系统角色定义
你是白银市电力系统首席故障诊断专家，拥有25年变电站运维经验，精通IEC 61850、IEEE C37、GB/T等国际国内标准。

# 专业能力要求
- **技术深度**：掌握电力系统理论、设备结构原理、故障机理分析
- **实践经验**：具备丰富的现场故障处理和设备检修经验
- **标准规范**：熟悉相关技术标准和安全规程
- **创新思维**：结合最新技术发展和行业最佳实践

# DeepSeek-R1推理要求
请使用完整的Chain-of-Thought推理过程，展示专业的分析思维。

# 严格输出格式
请严格按照以下格式输出，不得省略任何标签：

<think>
**第一阶段：现象观察与数据收集**
[详细分析故障现象、设备状态、监测数据等基础信息]

**第二阶段：初步假设形成**
[基于现象和经验，形成可能的故障原因假设]

**第三阶段：技术机理分析**
[从电气、机械、热力学、化学等角度深入分析故障机理]

**第四阶段：标准规范对照**
[对照相关技术标准，评估参数超标情况和风险等级]

**第五阶段：历史案例对比**
[结合类似故障案例，分析共性和特殊性]

**第六阶段：综合推理判断**
[综合所有信息，形成最终的故障诊断结论]

**第七阶段：自我验证**
[检查推理逻辑的合理性和结论的可靠性]
</think>

<answer>
请严格按照以下JSON格式输出分析结果：

```json
{{
  "fault_analysis": {{
    "equipment_info": {{
      "type": "设备类型",
      "model": "设备型号",
      "voltage_level": "电压等级",
      "location": "设备位置",
      "commissioning_date": "投运日期"
    }},
    "fault_classification": {{
      "primary_type": "主要故障类型",
      "secondary_type": "次要故障类型",
      "severity_level": "严重程度(1-5级)",
      "urgency_level": "紧急程度(低/中/高/紧急)",
      "fault_category": "故障类别(电气/机械/热/化学)"
    }},
    "technical_analysis": {{
      "symptoms": ["症状1", "症状2", "症状3"],
      "root_causes": ["根本原因1", "根本原因2"],
      "contributing_factors": ["影响因素1", "影响因素2"],
      "failure_mechanism": "故障机理详细描述",
      "technical_parameters": [
        {{"parameter": "参数名", "value": "数值", "unit": "单位", "status": "正常/异常", "threshold": "阈值"}}
      ]
    }},
    "standards_compliance": {{
      "applicable_standards": ["适用标准1", "适用标准2"],
      "compliance_status": "符合性状态",
      "deviations": ["偏差1", "偏差2"]
    }},
    "recommendations": {{
      "immediate_actions": [
        {{"action": "立即措施", "priority": "优先级", "timeline": "时间要求"}}
      ],
      "maintenance_plan": [
        {{"task": "检修任务", "method": "方法", "resources": "所需资源"}}
      ],
      "preventive_measures": [
        {{"measure": "预防措施", "implementation": "实施方案"}}
      ],
      "monitoring_points": ["监测要点1", "监测要点2"]
    }},
    "risk_assessment": {{
      "safety_risk": {{"level": "安全风险等级", "description": "风险描述"}},
      "equipment_risk": {{"level": "设备风险等级", "description": "风险描述"}},
      "system_impact": {{"level": "系统影响等级", "description": "影响描述"}},
      "economic_impact": {{"estimated_cost": "预估损失", "description": "经济影响描述"}}
    }},
    "confidence_assessment": {{
      "overall_confidence": 0.95,
      "data_quality": 0.90,
      "analysis_completeness": 0.95,
      "recommendation_reliability": 0.90
    }},
    "metadata": {{
      "analysis_timestamp": "分析时间戳",
      "analyst_id": "分析师ID",
      "analysis_method": "分析方法",
      "data_sources": ["数据源1", "数据源2"]
    }}
  }}
}}
```
</answer>

# 技术背景信息
## 故障描述
{fault_description}

## 设备信息
{equipment_info}

## 监测数据
{monitoring_data}

## 历史记录
{historical_data}

## 图像分析
{image_analysis}

# 分析任务
{question}

请基于上述信息，运用您的专业知识和丰富经验，按照要求的格式进行深度技术分析。""",
            input_variables=["fault_description", "equipment_info", "monitoring_data", "historical_data", "image_analysis", "question"]
        )
        
        # DeepSeek-V3 专业分析模板
        self.templates["deepseek_v3_fault_analysis"] = PromptTemplate(
            template="""# 系统角色
你是白银市电力系统资深故障诊断专家，具备深厚的理论基础和丰富的实践经验。

# 专业标准
- 遵循国家电网技术标准和安全规程
- 基于科学的分析方法和工程实践
- 提供精确的技术判断和可操作的建议
- 确保分析结果的专业性和可靠性

# 输出要求
请按照以下结构化格式输出专业分析报告：

## 🔍 故障概况
**设备信息**：[设备类型、型号、电压等级、位置]
**故障性质**：[故障类型、严重程度、影响范围]
**发生时间**：[故障发生时间和持续时间]

## ⚡ 技术分析
**故障现象**：[详细描述观察到的异常现象]
**参数分析**：[关键技术参数的异常情况]
**机理分析**：[从技术原理角度分析故障发生机制]

## 🎯 根因诊断
**主要原因**：[导致故障的主要技术原因]
**次要因素**：[影响故障发生的次要因素]
**发展过程**：[故障发生和发展的时序过程]

## 📋 处理方案
**应急措施**：[立即需要采取的安全措施]
**检修方案**：[具体的检修步骤和技术要求]
**恢复计划**：[设备恢复运行的时间安排]

## 🛡️ 风险评估
**安全风险**：[对人员和设备安全的影响评估]
**系统影响**：[对电网运行的影响分析]
**经济损失**：[预估的经济损失和修复成本]

## 🔧 预防建议
**技术改进**：[设备和技术方面的改进建议]
**管理优化**：[运维管理方面的优化措施]
**监测加强**：[需要加强的监测项目和频次]

# 技术资料
## 故障信息
{fault_description}

## 设备状态
{equipment_info}

## 监测数据
{monitoring_data}

## 历史记录
{historical_data}

# 分析任务
{question}

请基于提供的技术资料，按照上述结构进行专业分析。分析应当技术准确、逻辑清晰、建议可行。""",
            input_variables=["fault_description", "equipment_info", "monitoring_data", "historical_data", "question"]
        )
        
        logger.info("专业级提示词模板初始化完成")
    
    def _initialize_few_shot_examples(self):
        """初始化Few-shot学习示例"""
        
        # DeepSeek-R1 故障分析示例
        self.few_shot_examples["deepseek_r1_fault_analysis"] = [
            {
                "input": {
                    "fault_description": "110kV变压器A相套管发生闪络，保护动作跳闸",
                    "equipment_info": "110kV/10kV变压器，容量31.5MVA，投运15年",
                    "monitoring_data": "跳闸前A相电流突增至2.3kA，温度正常",
                    "historical_data": "近期雷雨天气较多，该变压器曾有轻微放电现象",
                    "image_analysis": "套管表面可见烧损痕迹，绝缘子有裂纹",
                    "question": "请分析故障原因并提供处理建议"
                },
                "output": {
                    "fault_classification": {
                        "primary_type": "绝缘击穿",
                        "secondary_type": "套管故障",
                        "severity_level": 4,
                        "urgency_level": "高"
                    },
                    "technical_analysis": {
                        "root_causes": ["套管绝缘老化", "雷击过电压"],
                        "failure_mechanism": "长期运行导致套管绝缘性能下降，雷击过电压成为触发因素"
                    }
                }
            }
        ]
        
        logger.info("Few-shot学习示例初始化完成")
    
    def _initialize_output_parsers(self):
        """初始化输出解析器"""
        
        class FaultAnalysisOutputParser(BaseOutputParser):
            """故障分析输出解析器"""
            
            def parse(self, text: str) -> Dict[str, Any]:
                try:
                    # 提取JSON部分
                    start_idx = text.find('```json')
                    end_idx = text.find('```', start_idx + 7)
                    
                    if start_idx != -1 and end_idx != -1:
                        json_str = text[start_idx + 7:end_idx].strip()
                        return json.loads(json_str)
                    else:
                        # 回退到简单解析
                        return {"raw_output": text, "parsed": False}
                        
                except Exception as e:
                    logger.error(f"输出解析失败: {e}")
                    return {"raw_output": text, "parsed": False, "error": str(e)}
            
            @property
            def _type(self) -> str:
                return "fault_analysis_output_parser"
        
        self.output_parsers["fault_analysis"] = FaultAnalysisOutputParser()
        
        logger.info("输出解析器初始化完成")
    
    def get_template(self, template_name: str, config: Optional[PromptConfig] = None) -> PromptTemplate:
        """获取提示词模板"""
        if template_name not in self.templates:
            # 如果模板不存在，返回默认模板
            logger.warning(f"模板 {template_name} 不存在，使用默认模板")
            return self._get_default_template(template_name)

        template = self.templates[template_name]

        # 如果需要添加Few-shot示例
        if config and config.use_few_shot and template_name in self.few_shot_examples:
            template = self._add_few_shot_examples(template, template_name)

        return template

    def _get_default_template(self, template_name: str) -> PromptTemplate:
        """获取默认模板"""
        if "deepseek_r1" in template_name:
            default_template = """
# 系统角色
你是白银市电力系统故障诊断专家，具备丰富的理论知识和实践经验。

# 分析要求
请使用DeepSeek-R1的推理能力，按照以下格式进行分析：

<think>
**步骤1：问题理解**
[理解故障描述和相关信息]

**步骤2：技术分析**
[从技术角度分析可能的故障原因]

**步骤3：综合判断**
[结合经验和理论进行综合判断]

**步骤4：解决方案**
[提出具体的处理建议]
</think>

<answer>
基于分析，提供结构化的故障诊断结论和处理建议。
</answer>

# 故障信息
{fault_description}

# 设备信息
{equipment_info}

# 监测数据
{monitoring_data}

# 历史记录
{historical_data}

# 分析任务
{question}
"""
        else:
            default_template = """
# 系统角色
你是白银市电力系统故障诊断专家。

# 分析任务
请对以下电力故障进行专业分析：

## 故障描述
{fault_description}

## 设备信息
{equipment_info}

## 监测数据
{monitoring_data}

## 历史记录
{historical_data}

## 具体问题
{question}

# 分析要求
请提供专业的技术分析和处理建议，包括：
1. 故障原因分析
2. 技术参数评估
3. 处理方案建议
4. 预防措施
"""

        return PromptTemplate(
            template=default_template,
            input_variables=["fault_description", "equipment_info", "monitoring_data", "historical_data", "question"]
        )
    
    def _add_few_shot_examples(self, template: PromptTemplate, template_name: str) -> PromptTemplate:
        """添加Few-shot示例到模板"""
        examples = self.few_shot_examples[template_name]
        
        # 构建示例文本
        examples_text = "\n\n# 分析示例\n"
        for i, example in enumerate(examples[:2], 1):  # 限制示例数量
            examples_text += f"\n## 示例 {i}\n"
            examples_text += f"**输入**：{example['input']['question']}\n"
            examples_text += f"**分析要点**：{example['output']['technical_analysis']['root_causes']}\n"
        
        # 在模板中插入示例
        new_template = template.template.replace(
            "# 分析任务",
            f"{examples_text}\n\n# 分析任务"
        )
        
        return PromptTemplate(
            template=new_template,
            input_variables=template.input_variables
        )
    
    def get_output_parser(self, parser_name: str) -> BaseOutputParser:
        """获取输出解析器"""
        if parser_name not in self.output_parsers:
            raise ValueError(f"解析器 {parser_name} 不存在")
        
        return self.output_parsers[parser_name]


    def validate_output_quality(self, output: Dict[str, Any], config: PromptConfig) -> Dict[str, Any]:
        """验证输出质量"""
        quality_metrics = {
            "completeness": 0.0,
            "technical_accuracy": 0.0,
            "structure_compliance": 0.0,
            "professional_depth": 0.0,
            "overall_score": 0.0
        }

        try:
            # 检查完整性
            if "fault_analysis" in output:
                required_fields = ["equipment_info", "fault_classification", "technical_analysis", "recommendations"]
                present_fields = sum(1 for field in required_fields if field in output["fault_analysis"])
                quality_metrics["completeness"] = present_fields / len(required_fields)

            # 检查结构合规性
            if output.get("parsed", True):
                quality_metrics["structure_compliance"] = 1.0

            # 检查专业深度（基于内容长度和关键词）
            content_length = len(str(output))
            if content_length > 1000:
                quality_metrics["professional_depth"] = min(content_length / 3000, 1.0)

            # 计算总分
            quality_metrics["overall_score"] = sum(quality_metrics.values()) / len(quality_metrics)

        except Exception as e:
            logger.error(f"质量验证失败: {e}")

        return quality_metrics

    def get_enhanced_prompt(self, template_name: str, context: Dict[str, Any],
                          config: Optional[PromptConfig] = None) -> str:
        """获取增强的提示词"""
        try:
            template = self.get_template(template_name, config)

            # 确保所有必需的变量都存在
            required_vars = template.input_variables
            filled_context = {}

            for var in required_vars:
                if var in context:
                    filled_context[var] = context[var]
                else:
                    # 提供默认值
                    default_values = {
                        "fault_description": "故障描述待补充",
                        "equipment_info": "设备信息待补充",
                        "monitoring_data": "监测数据待补充",
                        "historical_data": "历史数据待补充",
                        "image_analysis": "图像分析待补充",
                        "question": context.get("question", "请进行故障分析")
                    }
                    filled_context[var] = default_values.get(var, f"{var}信息待补充")

            # 填充模板
            prompt = template.format(**filled_context)

            # 添加质量控制指令
            if config and config.quality_threshold > 0.8:
                quality_instruction = f"""

# 质量控制要求
- 确保分析的技术准确性达到{config.quality_threshold*100:.0f}%以上
- 提供具体的数值和参数分析
- 包含明确的处理建议和时间安排
- 使用标准的电力系统术语
- 确保输出格式严格符合要求"""

                prompt += quality_instruction

            return prompt

        except Exception as e:
            logger.error(f"生成增强提示词失败: {e}")
            return self._get_fallback_prompt(context.get("question", ""))

    def _get_fallback_prompt(self, question: str) -> str:
        """获取回退提示词"""
        return f"""你是电力系统故障诊断专家。请分析以下问题：

{question}

请提供专业的技术分析和处理建议。"""


class PromptQualityController:
    """提示词质量控制器"""

    def __init__(self):
        self.quality_standards = {
            "technical_accuracy": 0.90,
            "completeness": 0.85,
            "structure_compliance": 0.95,
            "professional_depth": 0.80
        }

    def evaluate_prompt_quality(self, prompt: str) -> Dict[str, float]:
        """评估提示词质量"""
        metrics = {}

        # 长度检查
        metrics["length_score"] = min(len(prompt) / 2000, 1.0)

        # 结构检查
        structure_keywords = ["#", "##", "**", "```", "<think>", "<answer>"]
        structure_count = sum(1 for keyword in structure_keywords if keyword in prompt)
        metrics["structure_score"] = min(structure_count / len(structure_keywords), 1.0)

        # 专业术语检查
        professional_terms = ["故障", "设备", "分析", "诊断", "技术", "参数", "监测"]
        term_count = sum(1 for term in professional_terms if term in prompt)
        metrics["professional_score"] = min(term_count / len(professional_terms), 1.0)

        # 总分
        metrics["overall_score"] = sum(metrics.values()) / len(metrics)

        return metrics

    def optimize_prompt(self, prompt: str, target_score: float = 0.9) -> str:
        """优化提示词"""
        current_metrics = self.evaluate_prompt_quality(prompt)

        if current_metrics["overall_score"] >= target_score:
            return prompt

        # 添加结构化元素
        if current_metrics["structure_score"] < 0.8:
            prompt = self._add_structure_elements(prompt)

        # 增强专业性
        if current_metrics["professional_score"] < 0.8:
            prompt = self._enhance_professionalism(prompt)

        return prompt

    def _add_structure_elements(self, prompt: str) -> str:
        """添加结构化元素"""
        if "# 分析要求" not in prompt:
            prompt += "\n\n# 分析要求\n请提供结构化的专业分析。"

        return prompt

    def _enhance_professionalism(self, prompt: str) -> str:
        """增强专业性"""
        professional_addition = """

# 专业标准
- 使用标准电力系统术语
- 提供具体的技术参数分析
- 包含相关标准规范引用
- 确保建议的可操作性"""

        return prompt + professional_addition


# 全局实例
professional_prompt_templates = ProfessionalPromptTemplates()
prompt_quality_controller = PromptQualityController()
