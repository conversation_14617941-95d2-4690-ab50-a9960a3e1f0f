"""
现代化Chroma数据库管理器
兼容最新版本的ChromaDB (1.0.15+)
"""

import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError as e:
    logger.warning(f"ChromaDB不可用: {e}")
    CHROMADB_AVAILABLE = False


class ModernChromaManager:
    """现代化的Chroma数据库管理器"""
    
    def __init__(self, persist_directory: str = "embeddings/modern_chroma_store"):
        self.persist_directory = persist_directory
        self.collection_name = "power_system_knowledge"
        self.client = None
        self.collection = None
        
        if CHROMADB_AVAILABLE:
            self._init_client()
            self._init_collection()
        else:
            logger.error("ChromaDB不可用，无法初始化")
    
    def _init_client(self):
        """初始化Chroma客户端"""
        try:
            # 确保存储目录存在
            os.makedirs(self.persist_directory, exist_ok=True)
            
            # 使用最新的API创建客户端
            settings = Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
            
            self.client = chromadb.PersistentClient(
                path=self.persist_directory,
                settings=settings
            )
            
            logger.info(f"✅ 现代Chroma客户端初始化成功: {self.persist_directory}")
            
        except Exception as e:
            logger.error(f"❌ Chroma客户端初始化失败: {e}")
            # 尝试使用内存客户端作为回退
            try:
                self.client = chromadb.Client()
                logger.warning("⚠️ 使用内存客户端作为回退")
            except Exception as fallback_error:
                logger.error(f"❌ 内存客户端初始化也失败: {fallback_error}")
                raise
    
    def _init_collection(self):
        """初始化集合"""
        if not self.client:
            logger.error("客户端未初始化")
            return
            
        try:
            # 尝试获取现有集合
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                logger.info(f"✅ 获取现有集合: {self.collection_name}")
                return
                
            except Exception:
                logger.info(f"集合 {self.collection_name} 不存在，创建新集合")
            
            # 创建新集合
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={
                    "description": "白银电力系统故障诊断知识库",
                    "created_at": datetime.now().isoformat(),
                    "version": "2.0"
                }
            )
            
            logger.info(f"✅ 创建新集合: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"❌ 集合初始化失败: {e}")
            self.collection = None
    
    def is_available(self) -> bool:
        """检查Chroma是否可用"""
        return CHROMADB_AVAILABLE and self.client is not None and self.collection is not None
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到集合"""
        if not self.is_available():
            logger.warning("Chroma不可用，跳过文档添加")
            return False
            
        if not documents:
            logger.warning("文档列表为空")
            return True
            
        try:
            # 准备数据
            ids = []
            texts = []
            metadatas = []
            
            for doc in documents:
                doc_id = doc.get('id', str(uuid.uuid4()))
                text = doc.get('content', doc.get('text', ''))
                metadata = doc.get('metadata', {})
                
                # 确保metadata是字典且不包含None值
                if not isinstance(metadata, dict):
                    metadata = {}
                
                # 清理metadata中的None值
                clean_metadata = {k: v for k, v in metadata.items() if v is not None}
                
                ids.append(str(doc_id))
                texts.append(str(text))
                metadatas.append(clean_metadata)
            
            # 批量添加文档
            self.collection.add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"✅ 成功添加 {len(documents)} 个文档")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加文档失败: {e}")
            return False
    
    def search(self, query: str, n_results: int = 5, where: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """搜索文档"""
        if not self.is_available():
            logger.warning("Chroma不可用，返回空结果")
            return []
            
        if not query.strip():
            logger.warning("查询为空")
            return []
            
        try:
            # 执行搜索
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where
            )
            
            # 格式化结果
            formatted_results = []
            
            if results and 'documents' in results:
                documents = results['documents'][0] if results['documents'] else []
                metadatas = results.get('metadatas', [[]])[0]
                distances = results.get('distances', [[]])[0]
                ids = results.get('ids', [[]])[0]
                
                for i, doc in enumerate(documents):
                    result = {
                        'id': ids[i] if i < len(ids) else f"doc_{i}",
                        'content': doc,
                        'metadata': metadatas[i] if i < len(metadatas) else {},
                        'distance': distances[i] if i < len(distances) else 0.0,
                        'score': 1.0 - (distances[i] if i < len(distances) else 0.0)
                    }
                    formatted_results.append(result)
            
            logger.info(f"✅ 搜索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ 搜索失败: {e}")
            return []
    
    def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        if not self.is_available():
            return {"available": False, "error": "Chroma不可用"}
            
        try:
            count = self.collection.count()
            return {
                "available": True,
                "name": self.collection_name,
                "count": count,
                "persist_directory": self.persist_directory
            }
        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return {"available": False, "error": str(e)}
    
    def delete_collection(self) -> bool:
        """删除集合"""
        if not self.client:
            return False
            
        try:
            self.client.delete_collection(name=self.collection_name)
            self.collection = None
            logger.info(f"✅ 删除集合: {self.collection_name}")
            return True
        except Exception as e:
            logger.error(f"❌ 删除集合失败: {e}")
            return False
    
    def clear_collection(self) -> bool:
        """清空集合（删除所有文档但保留集合）"""
        if not self.is_available():
            logger.warning("Chroma不可用，无法清空集合")
            return False

        try:
            # 获取所有文档ID并删除
            result = self.collection.get()
            if result and 'ids' in result and result['ids']:
                self.collection.delete(ids=result['ids'])
                logger.info(f"✅ 清空集合: {self.collection_name}, 删除了 {len(result['ids'])} 个文档")
            else:
                logger.info(f"✅ 集合 {self.collection_name} 已经是空的")
            return True

        except Exception as e:
            logger.error(f"❌ 清空集合失败: {e}")
            return False

    def reset_collection(self) -> bool:
        """重置集合"""
        try:
            # 删除现有集合
            if self.collection:
                self.delete_collection()

            # 重新初始化
            self._init_collection()

            logger.info(f"✅ 重置集合: {self.collection_name}")
            return True

        except Exception as e:
            logger.error(f"❌ 重置集合失败: {e}")
            return False


# 创建全局实例
modern_chroma_manager = ModernChromaManager()


def get_modern_chroma_manager() -> ModernChromaManager:
    """获取现代化Chroma管理器实例"""
    return modern_chroma_manager


# 测试函数
def test_modern_chroma():
    """测试现代化Chroma管理器"""
    manager = get_modern_chroma_manager()

    print(f"🧪 测试现代化Chroma管理器...")
    print(f"📊 可用性: {manager.is_available()}")

    if manager.is_available():
        # 测试添加文档
        test_docs = [
            {
                "id": "test_1",
                "content": "这是一个测试文档",
                "metadata": {"source": "test", "type": "example"}
            }
        ]

        success = manager.add_documents(test_docs)
        print(f"📝 添加文档: {'✅ 成功' if success else '❌ 失败'}")

        # 测试搜索
        results = manager.search("测试", n_results=1)
        print(f"🔍 搜索结果: {len(results)} 个")

        # 获取集合信息
        info = manager.get_collection_info()
        print(f"📊 集合信息: {info}")

    print("🧪 测试完成")


if __name__ == "__main__":
    test_modern_chroma()
