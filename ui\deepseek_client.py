#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek客户端模块
提取ui/app.py中的DeepSeek相关代码，保持功能完整
"""

import json
import requests
import time
import sys
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from langchain_modules.prompts.prompt_manager import PromptManager
    PROMPT_MANAGER_AVAILABLE = True
except ImportError as e:
    PROMPT_MANAGER_AVAILABLE = False
    logging.error(f"提示词管理器不可用: {e}")

try:
    from langchain_modules.reasoning.deepseek_r1_optimizer import deepseek_r1_optimizer
    REASONING_OPTIMIZER_AVAILABLE = True
except ImportError as e:
    REASONING_OPTIMIZER_AVAILABLE = False
    logging.error(f"DeepSeek-R1推理优化器不可用: {e}")

logger = logging.getLogger(__name__)

class DeepSeekClient:
    """DeepSeek API客户端"""

    def __init__(self, api_key: str, base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "User-Agent": "PowerSystem-FaultAnalysis/1.0"
        }

        # 阿里云DashScope特殊配置
        if "dashscope.aliyuncs.com" in base_url:
            self.headers["X-DashScope-SSE"] = "enable"

        # 初始化专业组件
        self.prompt_manager = None
        if PROMPT_MANAGER_AVAILABLE:
            try:
                self.prompt_manager = PromptManager({})
                logger.info("✅ 专业提示词管理器初始化成功")
            except Exception as e:
                logger.error(f"❌ 提示词管理器初始化失败: {e}")

        # 推理优化器
        self.reasoning_optimizer = deepseek_r1_optimizer if REASONING_OPTIMIZER_AVAILABLE else None
        if self.reasoning_optimizer:
            logger.info("✅ DeepSeek-R1推理优化器已加载")

    def chat_completion(self, messages: list, model: str = "deepseek-v3",
                       temperature: float = 0.7, max_tokens: int = 2000, max_retries: int = 2,
                       stream: bool = False):
        """阿里云DashScope API调用，支持流式和非流式响应"""
        url = f"{self.base_url}/chat/completions"

        # 阿里云DashScope API payload格式
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }

        # DeepSeek-R1特殊配置
        if model == "deepseek-r1":
            payload["reasoning"] = True

        for attempt in range(max_retries + 1):
            try:
                if stream:
                    response = requests.post(url, headers=self.headers, json=payload, stream=True, timeout=120)
                else:
                    response = requests.post(url, headers=self.headers, json=payload, timeout=60)

                if response.status_code == 200:
                    if stream:
                        return response
                    else:
                        return response.json()
                else:
                    error_msg = f"API调用失败: {response.status_code}"
                    try:
                        error_detail = response.json()
                        error_msg += f" - {error_detail}"
                    except:
                        error_msg += f" - {response.text}"
                    
                    logger.error(error_msg)
                    
                    if attempt < max_retries:
                        wait_time = 2 ** attempt
                        logger.info(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        return {"error": error_msg}

            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (尝试 {attempt + 1}/{max_retries + 1})"
                logger.error(error_msg)
                
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    return {"error": "请求超时，请稍后重试"}

            except Exception as e:
                error_msg = f"请求异常: {str(e)} (尝试 {attempt + 1}/{max_retries + 1})"
                logger.error(error_msg)
                
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    return {"error": f"请求异常: {str(e)}"}

        return {"error": "所有重试都失败了"}

    def _make_stream_request(self, messages: List[Dict], model: str, temperature: float = 0.7, max_tokens: int = 2000, **kwargs):
        """创建流式请求"""
        try:
            return self.chat_completion(
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True,
                **kwargs
            )
        except Exception as e:
            logger.error(f"创建流式请求失败: {e}")
            return None

    def process_stream_response(self, stream_response, model: str = "deepseek-v3"):
        """处理流式响应 - 符合官方API格式的优化版本"""
        if not stream_response:
            yield {"type": "error", "message": "无法建立流式连接"}
            return

        # 初始化状态变量
        full_content = ""
        full_reasoning = ""

        # 性能优化变量
        chunk_buffer = ""
        reasoning_buffer = ""
        buffer_size = 0
        max_buffer_size = 512
        last_yield_time = time.time()
        min_yield_interval = 0.03

        try:
            for line in stream_response.iter_lines():
                if not line:
                    continue

                line = line.decode('utf-8')
                if not line.startswith('data: '):
                    continue

                data_str = line[6:]
                if data_str.strip() == '[DONE]':
                    # 发送最终处理结果
                    if model == "deepseek-r1":
                        yield self._finalize_r1_stream_official(full_reasoning, full_content)
                    else:
                        yield self._finalize_v3_stream(full_content)
                    break

                try:
                    data = json.loads(data_str)
                    if 'choices' not in data or not data['choices']:
                        continue

                    delta = data['choices'][0].get('delta', {})
                    content_chunk = delta.get('content', '')
                    reasoning_chunk = delta.get('reasoning_content', '')

                    # 处理推理内容（DeepSeek-R1的官方格式）
                    if reasoning_chunk and model == "deepseek-r1":
                        full_reasoning += reasoning_chunk
                        reasoning_buffer += reasoning_chunk

                        # 实时输出推理过程
                        if len(reasoning_buffer) >= max_buffer_size or time.time() - last_yield_time >= min_yield_interval:
                            formatted_reasoning = self._format_reasoning_content_official(reasoning_buffer)
                            yield {
                                "type": "reasoning_chunk",
                                "content": formatted_reasoning,
                                "raw_content": reasoning_buffer
                            }
                            reasoning_buffer = ""
                            last_yield_time = time.time()

                    # 处理主要内容
                    if content_chunk:
                        full_content += content_chunk
                        chunk_buffer += content_chunk
                        buffer_size += len(content_chunk)

                        current_time = time.time()
                        if (buffer_size >= max_buffer_size or
                            current_time - last_yield_time >= min_yield_interval):

                            if model == "deepseek-r1":
                                formatted_chunk = self._format_r1_content_official(chunk_buffer)
                                yield {
                                    "type": "answer_chunk",
                                    "content": formatted_chunk,
                                    "raw_content": chunk_buffer
                                }
                            else:
                                formatted_chunk = self._format_v3_content_official(chunk_buffer)
                                yield {
                                    "type": "content_chunk",
                                    "content": formatted_chunk,
                                    "raw_content": chunk_buffer
                                }

                            chunk_buffer = ""
                            buffer_size = 0
                            last_yield_time = current_time

                except json.JSONDecodeError:
                    continue

            # 发送完成信号
            yield {"type": "complete", "model": model}

        except Exception as e:
            logger.error(f"流式响应处理异常: {e}")
            yield {"type": "error", "message": f"处理异常: {str(e)}"}

    def _process_r1_stream_chunk(self, chunk: str, thinking_buffer: str, answer_buffer: str, current_section: str) -> Dict:
        """处理R1流式数据块 - 优化版本"""
        thinking_buffer += chunk
        yield_data = None

        # 检测<think>标签开始
        if '<think>' in chunk and current_section == "none":
            current_section = "thinking"
            # 清理开始标签并发送开始信号
            clean_content = chunk.replace('<think>', '').strip()
            yield_data = {
                "type": "reasoning_start",
                "content": "🤔 **开始深度推理分析**\n\n" + (self._format_reasoning_chunk(clean_content) if clean_content else "")
            }

        # 在思考阶段处理
        elif current_section == "thinking":
            if '</think>' in chunk:
                # 思考结束，提取完整思考内容
                think_match = re.search(r'<think>(.*?)</think>', thinking_buffer, re.DOTALL)
                if think_match:
                    thinking_content = think_match.group(1).strip()
                    formatted_thinking = self._format_thinking_content(thinking_content)
                    yield_data = {
                        "type": "reasoning_complete",
                        "content": f"\n\n✅ **推理分析完成**\n\n{formatted_thinking}"
                    }
                else:
                    # 处理当前块的结束部分
                    clean_chunk = chunk.replace('</think>', '').strip()
                    if clean_chunk:
                        yield_data = {
                            "type": "reasoning_chunk",
                            "content": self._format_reasoning_chunk(clean_chunk)
                        }
                current_section = "answer"
            else:
                # 实时思考内容处理
                clean_chunk = chunk.replace('<think>', '').replace('</think>', '').strip()
                if clean_chunk:
                    formatted_chunk = self._format_reasoning_chunk(clean_chunk)
                    yield_data = {
                        "type": "reasoning_chunk",
                        "content": formatted_chunk
                    }

        # 检测<answer>标签开始
        elif '<answer>' in chunk:
            current_section = "answer"
            clean_content = chunk.replace('<answer>', '').strip()
            yield_data = {
                "type": "answer_start",
                "content": "📋 **生成专业诊断结论**\n\n" + (self._format_answer_chunk(clean_content) if clean_content else "")
            }

        # 在答案阶段处理
        elif current_section == "answer":
            if '</answer>' in chunk:
                # 答案结束，提取完整答案内容
                answer_match = re.search(r'<answer>(.*?)</answer>', thinking_buffer, re.DOTALL)
                if answer_match:
                    answer_content = answer_match.group(1).strip()
                    formatted_answer = self._format_answer_content(answer_content)
                    yield_data = {
                        "type": "answer_complete",
                        "content": formatted_answer
                    }
                else:
                    # 处理当前块的结束部分
                    clean_chunk = chunk.replace('</answer>', '').strip()
                    if clean_chunk:
                        yield_data = {
                            "type": "answer_chunk",
                            "content": self._format_answer_chunk(clean_chunk)
                        }
            else:
                # 实时答案内容处理
                clean_chunk = chunk.replace('<answer>', '').replace('</answer>', '').strip()
                if clean_chunk:
                    formatted_chunk = self._format_answer_chunk(clean_chunk)
                    yield_data = {
                        "type": "answer_chunk",
                        "content": formatted_chunk
                    }

        # 无标签情况，智能判断内容类型
        elif current_section == "none":
            # 如果没有标签，尝试智能分类
            if self._is_thinking_content(chunk):
                current_section = "thinking"
                yield_data = {
                    "type": "reasoning_start",
                    "content": "🤔 **推理分析过程**\n\n" + self._format_reasoning_chunk(chunk)
                }
            else:
                # 直接作为答案内容
                yield_data = {
                    "type": "answer_chunk",
                    "content": self._format_answer_chunk(chunk)
                }

        return {
            "thinking_buffer": thinking_buffer,
            "answer_buffer": answer_buffer,
            "current_section": current_section,
            "yield_data": yield_data
        }

    def _is_thinking_content(self, content: str) -> bool:
        """判断是否为思考内容"""
        thinking_indicators = [
            "分析", "考虑", "判断", "推理", "思考", "评估", "检查",
            "首先", "其次", "然后", "接下来", "最后",
            "可能", "应该", "需要", "建议", "认为"
        ]
        return any(indicator in content for indicator in thinking_indicators)

    def _format_reasoning_content_official(self, content: str) -> str:
        """格式化推理内容 - 增强层次结构显示"""
        if not content.strip():
            return ""

        content = content.strip()

        # 分段处理，增强层次结构
        paragraphs = content.split('\n\n')
        formatted_paragraphs = []

        for paragraph in paragraphs:
            if not paragraph.strip():
                continue

            lines = paragraph.split('\n')
            formatted_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 识别主要推理步骤（数字开头）
                if re.match(r'^\d+[\.、]\s*', line):
                    formatted_lines.append(f"🔍 **{line}**")

                # 识别分析要点（冒号结尾）
                elif line.endswith('：') or line.endswith(':'):
                    formatted_lines.append(f"📌 **{line}**")

                # 识别逻辑连接词
                elif any(line.startswith(conn) for conn in ['因此', '所以', '由于', '因为', '然而', '但是', '另外', '同时']):
                    formatted_lines.append(f"💡 *{line}*")

                # 识别关键结论（包含"可能"、"应该"、"需要"等）
                elif any(keyword in line for keyword in ['可能', '应该', '需要', '建议', '认为', '判断']):
                    formatted_lines.append(f"⚡ {line}")

                # 识别问题或疑问
                elif '？' in line or '?' in line:
                    formatted_lines.append(f"❓ {line}")

                # 普通推理内容
                else:
                    formatted_lines.append(f"　　{line}")

            if formatted_lines:
                formatted_paragraphs.append('\n'.join(formatted_lines))

        return '\n\n'.join(formatted_paragraphs)

    def _format_r1_content_official(self, content: str) -> str:
        """格式化DeepSeek-R1内容 - 增强结构化显示"""
        if not content.strip():
            return ""

        content = content.strip()

        # 处理Markdown标题，增加图标
        content = re.sub(r'^#{1}\s+(.+)$', r'# 📋 \1', content, flags=re.MULTILINE)
        content = re.sub(r'^#{2}\s+(.+)$', r'## 🔧 \1', content, flags=re.MULTILINE)
        content = re.sub(r'^#{3}\s+(.+)$', r'### ⚙️ \1', content, flags=re.MULTILINE)
        content = re.sub(r'^#{4,6}\s+(.+)$', r'#### 📌 \1', content, flags=re.MULTILINE)

        # 处理列表项，增加层次感
        content = re.sub(r'^[\s]*[-*+]\s+(.+)$', r'• \1', content, flags=re.MULTILINE)
        content = re.sub(r'^[\s]*(\d+)\.\s+(.+)$', r'\1️⃣ \2', content, flags=re.MULTILINE)

        # 处理关键信息标记
        content = re.sub(r'\*\*(.+?)\*\*', r'**🔥 \1**', content)

        return content

    def _format_v3_content_official(self, content: str) -> str:
        """格式化DeepSeek-V3内容 - 增强结构化显示"""
        if not content.strip():
            return ""

        content = content.strip()

        # 智能检测和格式化标题
        lines = content.split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                formatted_lines.append('')
                continue

            # 保持已有的Markdown标题，增加图标
            if line.startswith('# '):
                formatted_lines.append(f"# 📊 {line[2:]}")
            elif line.startswith('## '):
                formatted_lines.append(f"## 🔧 {line[3:]}")
            elif line.startswith('### '):
                formatted_lines.append(f"### ⚙️ {line[4:]}")
            elif line.startswith('#### '):
                formatted_lines.append(f"#### 📌 {line[5:]}")

            # 智能识别可能的标题
            elif ('：' in line or ':' in line) and len(line) < 80 and not line.startswith(('-', '*', '+')):
                # 检查是否包含标题关键词
                title_keywords = ['分析', '报告', '结论', '建议', '措施', '方案', '原因', '故障', '设备', '检查', '维护', '现象', '处理', '解决']
                if any(keyword in line for keyword in title_keywords):
                    formatted_lines.append(f"### 🔍 {line}")
                else:
                    formatted_lines.append(line)

            # 处理列表项
            elif line.startswith(('- ', '* ', '+ ')):
                formatted_lines.append(f"• {line[2:]}")
            elif re.match(r'^\d+\.\s+', line):
                formatted_lines.append(f"{line[:line.find('.')+1]}️⃣ {line[line.find('.')+2:]}")

            else:
                formatted_lines.append(line)

        return '\n'.join(formatted_lines)

    def _is_likely_header(self, text: str) -> bool:
        """判断是否可能是标题"""
        if len(text) > 100:  # 太长不太可能是标题
            return False

        header_indicators = [
            '分析', '报告', '结论', '建议', '措施', '方案', '原因', '故障',
            '设备', '检查', '维护', '处理', '解决', '预防'
        ]

        return (
            ('：' in text or ':' in text) and
            any(indicator in text for indicator in header_indicators) and
            not text.startswith(('- ', '* ', '+ ')) and
            not re.match(r'^\d+\.', text)
        )

    def _determine_header_level(self, text: str) -> int:
        """确定标题级别"""
        if any(word in text for word in ['报告', '分析']):
            return 2  # ##
        elif any(word in text for word in ['设备', '故障', '原因']):
            return 3  # ###
        else:
            return 4  # ####

    def _finalize_r1_stream_official(self, reasoning_content: str, answer_content: str) -> Dict:
        """完成R1流式处理 - 符合官方API格式"""
        return {
            "type": "stream_complete",
            "model": "deepseek-r1",
            "summary": {
                "has_reasoning": bool(reasoning_content.strip()),
                "has_answer": bool(answer_content.strip()),
                "reasoning_length": len(reasoning_content),
                "answer_length": len(answer_content),
                "total_length": len(reasoning_content) + len(answer_content)
            }
        }

    def _finalize_v3_stream(self, full_content: str) -> Dict:
        """完成V3流式处理"""
        structure_analysis = self._analyze_content_structure(full_content)
        return {
            "type": "stream_complete",
            "model": "deepseek-v3",
            "summary": {
                "total_length": len(full_content),
                "structure_score": structure_analysis["score"],
                "sections_count": structure_analysis["sections"]
            }
        }

    def professional_chat_completion(self, query: str, context: Dict[str, Any],
                                   model: str = "deepseek-v3", stream: bool = False,
                                   use_reasoning_optimization: bool = True) -> Dict[str, Any]:
        """专业级聊天完成 - 集成提示词优化和推理增强"""
        try:
            # 使用优化的提示词系统
            optimized_prompt = self._build_optimized_prompt(query, context, model)

            # 构建消息 - 根据官方建议，不使用system prompt
            messages = [
                {"role": "user", "content": optimized_prompt}
            ]

            # 获取优化的模型参数
            model_config = self._get_optimized_model_config(model)

            # 调用API
            response = self.chat_completion(
                messages=messages,
                model=model,
                temperature=model_config["temperature"],
                max_tokens=model_config["max_tokens"],
                stream=stream
            )

            if not response:
                return {"success": False, "error": "API调用失败"}

            # 处理响应
            if stream:
                return {"success": True, "response": response, "stream": True}
            else:
                content = response.get("choices", [{}])[0].get("message", {}).get("content", "")

                # 应用输出质量优化
                processed_content = self._process_model_output(content, model)

                return {
                    "success": True,
                    "content": processed_content["content"],
                    "model": model,
                    "quality_metrics": processed_content.get("quality_metrics", {}),
                    "structure_analysis": processed_content.get("structure_analysis", {})
                }

        except Exception as e:
            logger.error(f"专业聊天完成失败: {e}")
            return {"success": False, "error": str(e)}

    def _build_optimized_prompt(self, query: str, context: Dict[str, Any], model: str) -> str:
        """构建优化的提示词 - 符合官方API格式"""
        if model == "deepseek-r1":
            # DeepSeek-R1 不需要特殊标签，API会自动处理推理过程
            return f"""你是白银市电力系统故障诊断专家，具备丰富的电力设备故障分析经验。

问题：{query}

上下文信息：
{json.dumps(context, ensure_ascii=False, indent=2)}

请进行深度推理分析，包含以下步骤：

1. 详细分析故障现象和症状
2. 考虑所有可能的故障原因
3. 评估各种可能性的概率和逻辑关系
4. 结合专业知识进行系统性推理
5. 得出最可能的故障原因和解决方案

最终请提供结构化的故障分析报告：

## 故障分析报告

### 设备信息
- 设备类型：
- 设备编号：
- 故障类型：

### 故障原因分析
- 主要原因：
- 次要因素：

### 解决方案
- 紧急措施：
- 根本解决：

### 预防措施
- 日常维护：
- 监控要点："""
        else:  # deepseek-v3
            return f"""你是白银市电力系统故障诊断专家，具备丰富的电力设备故障分析经验。

问题：{query}

上下文信息：
{json.dumps(context, ensure_ascii=False, indent=2)}

请提供结构化的专业技术分析，使用Markdown格式，包含以下部分：

## 故障分析报告

### 设备信息
### 故障现象
### 原因分析
### 解决方案
### 预防措施

要求：
1. 分析逻辑清晰，层次分明
2. 技术术语准确，表述专业
3. 结论明确，建议可操作
4. 格式规范，便于阅读"""

    def _get_optimized_model_config(self, model: str) -> Dict[str, Any]:
        """获取优化的模型配置"""
        if model == "deepseek-r1":
            return {
                "temperature": 0.6,  # 官方推荐范围0.5-0.7
                "max_tokens": 8192,
                "top_p": 0.95
            }
        else:  # deepseek-v3
            return {
                "temperature": 0.7,
                "max_tokens": 4000,
                "top_p": 0.9
            }

    def _process_model_output(self, content: str, model: str) -> Dict[str, Any]:
        """处理模型输出，提升质量和结构"""
        if model == "deepseek-r1":
            return self._process_r1_output(content)
        else:
            return self._process_v3_output(content)

    def _process_r1_output(self, content: str) -> Dict[str, Any]:
        """处理DeepSeek-R1输出 - 符合官方API格式"""
        try:
            # 官方API已经分离了reasoning_content和content，这里直接处理content部分
            formatted_content = self._format_r1_content_official(content)

            return {
                "content": formatted_content,
                "quality_metrics": {
                    "has_content": bool(content.strip()),
                    "content_length": len(content),
                    "structure_score": self._calculate_structure_score(formatted_content)
                }
            }

        except Exception as e:
            logger.error(f"处理R1输出失败: {e}")
            return {"content": content, "quality_metrics": {"error": str(e)}}

    def _calculate_structure_score(self, content: str) -> float:
        """计算内容结构分数"""
        score = 0.5  # 基础分

        # 检查是否有标题
        if re.search(r'^#{1,6}\s+', content, re.MULTILINE):
            score += 0.2

        # 检查是否有列表
        if re.search(r'^[-*+]\s+', content, re.MULTILINE) or re.search(r'^\d+\.\s+', content, re.MULTILINE):
            score += 0.2

        # 检查段落结构
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        if len(paragraphs) >= 3:
            score += 0.1

        return min(score, 1.0)

    def _process_v3_output(self, content: str) -> Dict[str, Any]:
        """处理DeepSeek-V3输出"""
        try:
            # 确保Markdown格式正确
            formatted_content = self._format_v3_content(content)

            # 分析内容结构
            structure_analysis = self._analyze_content_structure(formatted_content)

            return {
                "content": formatted_content,
                "quality_metrics": {
                    "structure_score": structure_analysis["score"],
                    "sections_count": structure_analysis["sections"],
                    "has_headers": structure_analysis["has_headers"]
                },
                "structure_analysis": structure_analysis
            }

        except Exception as e:
            logger.error(f"处理V3输出失败: {e}")
            return {"content": content, "quality_metrics": {"error": str(e)}}

    def _format_thinking_content(self, content: str) -> str:
        """格式化思考内容 - 增强版本"""
        if not content:
            return ""

        lines = content.split('\n')
        formatted_lines = []
        in_list = False

        for line in lines:
            line = line.strip()
            if not line:
                if in_list:
                    formatted_lines.append('')  # 保持列表间距
                continue

            # 处理编号步骤
            if re.match(r'^\d+[\.、]\s*', line):
                if in_list:
                    formatted_lines.append('')  # 添加间距
                formatted_lines.append(f"**{line}**")
                in_list = False
            # 处理要点列表
            elif line.startswith(('- ', '• ', '* ')):
                formatted_lines.append(f"  {line}")
                in_list = True
            # 处理分析标题
            elif line.endswith('：') or line.endswith(':'):
                if in_list:
                    formatted_lines.append('')
                formatted_lines.append(f"\n**{line}**")
                in_list = False
            # 处理逻辑连接
            elif any(line.startswith(conn) for conn in ['因此', '所以', '由于', '因为', '然而', '但是']):
                if in_list:
                    formatted_lines.append('')
                formatted_lines.append(f"**{line}**")
                in_list = False
            else:
                formatted_lines.append(line)
                in_list = False

        return '\n'.join(formatted_lines)

    def _format_answer_content(self, content: str) -> str:
        """格式化答案内容 - 增强版本"""
        if not content:
            return ""

        # 标准化Markdown格式
        content = self._normalize_markdown_structure(content)

        # 智能添加结构
        if not content.startswith('#'):
            content = self._add_smart_structure(content)

        return content.strip()

    def _normalize_markdown_structure(self, content: str) -> str:
        """标准化Markdown结构"""
        # 修复标题格式
        content = re.sub(r'^(#{1,6})\s*([^#\n]+)', r'\1 \2', content, flags=re.MULTILINE)

        # 修复列表格式
        content = re.sub(r'^[\s]*[-*+]\s+', '- ', content, flags=re.MULTILINE)
        content = re.sub(r'^[\s]*(\d+)\.\s+', r'\1. ', content, flags=re.MULTILINE)

        # 确保段落间距
        content = re.sub(r'\n{3,}', '\n\n', content)

        return content

    def _add_smart_structure(self, content: str) -> str:
        """智能添加结构"""
        lines = content.split('\n')
        structured_lines = []
        current_section = None

        for line in lines:
            line = line.strip()
            if not line:
                structured_lines.append('')
                continue

            # 检测可能的章节标题
            section = self._detect_section_type(line)
            if section and section != current_section:
                if structured_lines and structured_lines[-1]:  # 添加间距
                    structured_lines.append('')
                structured_lines.append(f"### {line}")
                current_section = section
            else:
                structured_lines.append(line)

        return '\n'.join(structured_lines)

    def _detect_section_type(self, line: str) -> str:
        """检测章节类型"""
        section_keywords = {
            'equipment': ['设备', '装置', '机器'],
            'fault': ['故障', '问题', '异常', '错误'],
            'analysis': ['分析', '原因', '诊断'],
            'solution': ['解决', '处理', '修复', '措施'],
            'prevention': ['预防', '维护', '保养', '监控']
        }

        for section_type, keywords in section_keywords.items():
            if any(keyword in line for keyword in keywords) and ('：' in line or ':' in line):
                return section_type

        return None

    def _format_v3_content(self, content: str) -> str:
        """格式化V3内容"""
        if not content:
            return ""

        # 标准化Markdown格式
        lines = content.split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                formatted_lines.append('')
                continue

            # 确保标题格式
            if line.startswith('#'):
                formatted_lines.append(line)
            elif '：' in line and not line.startswith('-'):
                # 可能是标题
                formatted_lines.append(f"### {line}")
            else:
                formatted_lines.append(line)

        return '\n'.join(formatted_lines)

    def _analyze_content_structure(self, content: str) -> Dict[str, Any]:
        """分析内容结构"""
        headers = re.findall(r'^#{1,6}\s+(.+)', content, re.MULTILINE)
        sections = len(headers)
        has_headers = sections > 0

        # 计算结构分数
        score = 0.5  # 基础分
        if has_headers:
            score += 0.3
        if sections >= 3:
            score += 0.2

        return {
            "score": min(score, 1.0),
            "sections": sections,
            "has_headers": has_headers,
            "headers": headers
        }

    def enhanced_fault_analysis(self, fault_description: str, equipment_info: str = "",
                              monitoring_data: str = "", historical_data: str = "",
                              image_analysis: str = "", model: str = "deepseek-v3") -> Dict[str, Any]:
        """增强故障分析 - 专业级故障诊断接口"""
        context = {
            "question": fault_description,
            "fault_description": fault_description,
            "equipment_info": equipment_info,
            "monitoring_data": monitoring_data,
            "historical_data": historical_data,
            "image_analysis": image_analysis
        }

        return self.professional_chat_completion(
            query=fault_description,
            context=context,
            model=model,
            stream=False,
            use_reasoning_optimization=True
        )

    def test_connection(self) -> Dict[str, Any]:
        """测试API连接"""
        try:
            test_messages = [{"role": "user", "content": "你好"}]
            result = self.chat_completion(
                messages=test_messages,
                model="deepseek-v3",
                max_tokens=50,
                stream=False
            )
            
            if "error" in result:
                return {"success": False, "error": result["error"]}
            else:
                return {"success": True, "message": "连接正常"}
                
        except Exception as e:
            return {"success": False, "error": f"连接测试失败: {str(e)}"}

class DeepSeekR1Config:
    """DeepSeek-R1配置管理"""
    
    # R1模型的不同配置类型
    TYPE_CONFIGS = {
        "balanced": {
            "temperature": 0.3,
            "max_tokens": 8192,
            "description": "平衡模式 - 兼顾准确性和创造性"
        },
        "precise": {
            "temperature": 0.1,
            "max_tokens": 12288,
            "description": "精确模式 - 最高准确性，适合技术分析"
        },
        "creative": {
            "temperature": 0.5,
            "max_tokens": 6144,
            "description": "创新模式 - 更多创造性思维"
        },
        "detailed": {
            "temperature": 0.2,
            "max_tokens": 16384,
            "description": "详细模式 - 最详细的分析过程"
        }
    }
    
    @classmethod
    def get_config(cls, config_type: str = "balanced") -> Dict[str, Any]:
        """获取指定类型的配置"""
        return cls.TYPE_CONFIGS.get(config_type, cls.TYPE_CONFIGS["balanced"])
    
    @classmethod
    def get_all_configs(cls) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        return cls.TYPE_CONFIGS

def create_deepseek_client(api_key: str, base_url: str = None) -> DeepSeekClient:
    """创建DeepSeek客户端的便捷函数"""
    if base_url is None:
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    return DeepSeekClient(api_key, base_url)
