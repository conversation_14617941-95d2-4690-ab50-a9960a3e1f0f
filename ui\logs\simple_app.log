2025-07-23 15:57:23,846 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://**************:5003
2025-07-23 15:57:23,847 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 15:57:23,867 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 15:57:24,248 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 15:57:24,255 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 15:59:24,653 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 15:59:24,653 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 15:59:24,654 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 15:59:25,177 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 15:59:25,547 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 15:59:25,553 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:00:13,161 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:00:13,162 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:00:13,163 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:00:13,959 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:00:14,628 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:00:14,634 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:00:42,397 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:00:42,398 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:00:42,398 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:00:42,601 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:00:42,981 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:00:42,988 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:01:36,870 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://**************:5003
2025-07-23 16:01:36,870 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 16:01:36,878 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:01:37,232 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:01:37,238 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:03:13,581 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\test_deepseek_optimization.py', reloading
2025-07-23 16:03:13,581 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\test_deepseek_optimization.py', reloading
2025-07-23 16:03:13,581 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\test_deepseek_optimization.py', reloading
2025-07-23 16:03:13,582 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\test_deepseek_optimization.py', reloading
2025-07-23 16:03:13,583 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\test_deepseek_optimization.py', reloading
2025-07-23 16:03:13,583 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\test_deepseek_optimization.py', reloading
2025-07-23 16:03:13,857 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:03:13,895 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:03:14,313 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:03:14,324 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:03:14,339 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:03:14,349 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:06:08,472 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sentence_transformers\\cross_encoder\\__init__.py', reloading
2025-07-23 16:06:08,472 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sentence_transformers\\cross_encoder\\__init__.py', reloading
2025-07-23 16:06:08,489 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sentence_transformers\\cross_encoder\\CrossEncoder.py', reloading
2025-07-23 16:06:08,489 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sentence_transformers\\cross_encoder\\CrossEncoder.py', reloading
2025-07-23 16:06:08,558 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\transformers\\utils\\import_utils.py', reloading
2025-07-23 16:06:08,560 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\transformers\\utils\\import_utils.py', reloading
2025-07-23 16:06:08,690 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\transformers\\modeling_utils.py', reloading
2025-07-23 16:06:08,690 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\transformers\\modeling_utils.py', reloading
2025-07-23 16:06:08,697 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\utils\\checkpoint.py', reloading
2025-07-23 16:06:08,697 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\utils\\checkpoint.py', reloading
2025-07-23 16:06:08,752 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\_functorch\\_aot_autograd\\functional_utils.py', reloading
2025-07-23 16:06:08,752 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\_functorch\\_aot_autograd\\functional_utils.py', reloading
2025-07-23 16:06:08,758 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\fx\\experimental\\symbolic_shapes.py', reloading
2025-07-23 16:06:08,759 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\fx\\experimental\\symbolic_shapes.py', reloading
2025-07-23 16:06:08,782 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\utils\\_sympy\\functions.py', reloading
2025-07-23 16:06:08,782 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\utils\\_sympy\\functions.py', reloading
2025-07-23 16:06:08,804 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sympy\\__init__.py', reloading
2025-07-23 16:06:08,804 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sympy\\__init__.py', reloading
2025-07-23 16:06:08,823 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sympy\\core\\__init__.py', reloading
2025-07-23 16:06:08,823 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\sympy\\core\\__init__.py', reloading
2025-07-23 16:06:09,472 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:06:09,472 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:06:10,221 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:06:10,232 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:06:10,239 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:06:10,252 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:06:23,284 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://**************:5003
2025-07-23 16:06:23,284 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 16:06:23,293 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:06:23,634 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:06:23,641 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:09:48,085 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,085 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,086 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,086 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,086 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,086 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,086 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,086 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,087 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:09:48,157 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:09:48,557 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:09:48,564 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:09:48,884 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:09:48,897 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:09:49,262 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:09:49,270 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:09:49,271 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:09:49,277 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:10:12,463 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:10:12,463 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:10:12,464 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:10:12,464 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:10:12,464 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:10:12,465 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:10:12,465 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_client.py', reloading
2025-07-23 16:10:12,538 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:10:12,547 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:10:12,810 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:10:12,952 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:10:12,959 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:10:12,963 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:10:12,969 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:10:13,176 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:10:13,183 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:15:44,294 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://**************:5003
2025-07-23 16:15:44,294 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 16:15:44,303 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:15:44,658 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:15:44,664 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:16:20,295 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 16:16:20] "GET / HTTP/1.1" 200 -
2025-07-23 16:16:21,792 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 16:16:21] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-23 16:16:22,507 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 16:16:22] "GET /test_optimizer HTTP/1.1" 200 -
2025-07-23 16:16:22,554 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 16:16:22] "GET /static/css/deepseek_output_styles.css HTTP/1.1" 200 -
2025-07-23 16:16:22,858 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 16:16:22] "GET /static/js/deepseek_output_enhancer.js HTTP/1.1" 200 -
2025-07-23 16:16:28,127 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://**************:5003
2025-07-23 16:16:28,127 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 16:16:28,136 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:16:28,483 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:16:28,489 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:16:45,012 - werkzeug - INFO - 127.0.0.1 - - [23/Jul/2025 16:16:45] "GET /api/health HTTP/1.1" 200 -
2025-07-23 16:18:23,797 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\__init__.py', reloading
2025-07-23 16:18:23,797 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\__init__.py', reloading
2025-07-23 16:18:23,799 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\aliases.py', reloading
2025-07-23 16:18:23,799 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\aliases.py', reloading
2025-07-23 16:18:23,800 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\utf_8.py', reloading
2025-07-23 16:18:23,800 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\utf_8.py', reloading
2025-07-23 16:18:23,800 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\utf_8.py', reloading
2025-07-23 16:18:23,801 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\gbk.py', reloading
2025-07-23 16:18:23,801 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\gbk.py', reloading
2025-07-23 16:18:23,801 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\encodings\\gbk.py', reloading
2025-07-23 16:18:23,912 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:18:23,914 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\re\\__init__.py', reloading
2025-07-23 16:18:23,914 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\re\\__init__.py', reloading
2025-07-23 16:18:23,916 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\enum.py', reloading
2025-07-23 16:18:23,917 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\types.py', reloading
2025-07-23 16:18:23,918 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\types.py', reloading
2025-07-23 16:18:23,919 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\operator.py', reloading
2025-07-23 16:18:23,921 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\functools.py', reloading
2025-07-23 16:18:23,923 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\collections\\__init__.py', reloading
2025-07-23 16:18:23,926 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\keyword.py', reloading
2025-07-23 16:18:23,928 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\reprlib.py', reloading
2025-07-23 16:18:23,934 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\re\\_compiler.py', reloading
2025-07-23 16:18:23,936 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\re\\_parser.py', reloading
2025-07-23 16:18:23,938 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\re\\_constants.py', reloading
2025-07-23 16:18:23,940 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\re\\_casefix.py', reloading
2025-07-23 16:18:23,942 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copyreg.py', reloading
2025-07-23 16:18:23,942 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copyreg.py', reloading
2025-07-23 16:18:23,956 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\collections\\abc.py', reloading
2025-07-23 16:18:23,958 - werkzeug - INFO -  * Detected change in 'C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\datetime.py', reloading
2025-07-23 16:18:24,041 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:18:24,620 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:18:24,635 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:18:24,677 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:18:24,685 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:18:24,693 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:18:24,704 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:20:19,757 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5003
 * Running on http://**************:5003
2025-07-23 16:20:19,757 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 16:20:19,766 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:20:20,106 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:20:20,112 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:29:05,705 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\deepseek_output_optimizer.py', reloading
2025-07-23 16:29:06,239 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-07-23 16:29:06,716 - werkzeug - WARNING -  * Debugger is active!
2025-07-23 16:29:06,727 - werkzeug - INFO -  * Debugger PIN: 211-************-07-23 16:30:09,705 - werkzeug - INFO -  * Detected change in 'G:\\my-dl-dmx\\ui\\simple_app.py', reloading
2025-07-23 16:30:10,309 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
