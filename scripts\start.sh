#!/bin/bash

# 故障分析智能助手启动脚本

set -e

echo "=== 故障分析智能助手启动脚本 ==="

# 检查环境变量
if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "警告: DEEPSEEK_API_KEY 环境变量未设置"
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p logs data/raw data/processed data/structured
mkdir -p embeddings/faiss_store embeddings/index
mkdir -p knowledge_base/text knowledge_base/images knowledge_base/mappings

# 初始化向量数据库
echo "初始化向量数据库..."
python -c "
import os
import numpy as np
try:
    import faiss
    # 创建FAISS索引
    dimension = 384  # sentence-transformers paraphrase-multilingual-MiniLM-L12-v2 维度
    index = faiss.IndexFlatIP(dimension)

    # 保存索引
    os.makedirs('embeddings/faiss_store', exist_ok=True)
    faiss.write_index(index, 'embeddings/faiss_store/index.faiss')
    print('FAISS索引初始化完成')
except ImportError:
    print('FAISS未安装，跳过向量数据库初始化')
except Exception as e:
    print(f'向量数据库初始化失败: {e}')
"

# 初始化知识库
echo "初始化知识库..."
python -c "
import json
import os

# 创建示例知识库数据
sample_data = {
    'documents': [
        {
            'id': 'doc_001',
            'title': '变压器故障诊断手册',
            'content': '变压器是电力系统中的重要设备，常见故障包括绕组故障、铁心故障、套管故障等。',
            'type': 'manual',
            'source': 'internal'
        },
        {
            'id': 'doc_002', 
            'title': '断路器维护规程',
            'content': '断路器应定期检查操作机构、触头系统、绝缘系统等关键部件。',
            'type': 'procedure',
            'source': 'standard'
        }
    ]
}

os.makedirs('knowledge_base/mappings', exist_ok=True)
with open('knowledge_base/mappings/documents.json', 'w', encoding='utf-8') as f:
    json.dump(sample_data, f, ensure_ascii=False, indent=2)

print('知识库初始化完成')
"

# 使用统一启动脚本
echo "启动故障分析智能助手系统..."
python start_project.py
