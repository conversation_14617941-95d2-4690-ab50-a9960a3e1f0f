2025-07-22 10:01:43.116 | INFO     | __main__:main:293 - 系统启动开始
2025-07-22 10:01:43.117 | INFO     | __main__:main:297 - 步骤 1/5: 系统健康检查
2025-07-22 10:01:43.205 | INFO     | __main__:main:301 - 步骤 2/5: 检查依赖
2025-07-22 10:01:43.842 | INFO     | __main__:main:307 - 步骤 3/5: 创建目录
2025-07-22 10:01:43.843 | INFO     | __main__:main:311 - 步骤 4/5: 显示启动信息
2025-07-22 10:01:43.845 | INFO     | __main__:main:315 - 步骤 5/5: 启动主服务器
2025-07-22 10:01:43.845 | INFO     | __main__:start_optimized_server:184 - 🚀 启动主服务器...
2025-07-22 10:01:48.932 | INFO     | data_processing.ocr_processor:<module>:27 - PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-22 10:01:48.934 | INFO     | data_processing.vector_processor:<module>:27 - 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-22 10:01:49.654 | INFO     | data_processing.modern_chroma_manager:_init_client:53 - ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-22 10:01:49.655 | INFO     | data_processing.modern_chroma_manager:_init_collection:75 - ✅ 获取现有集合: power_system_knowledge
2025-07-22 10:01:49.911 | INFO     | data_processing.chroma_manager:__init__:34 - ✅ 使用现代化Chroma管理器
2025-07-22 10:01:50.394 | INFO     | data_processing.advanced_retrieval_optimizer:_init_jieba:132 - PaddlePaddle未安装，使用jieba默认模式
2025-07-22 10:01:52.052 | INFO     | __main__:start_optimized_server:194 - 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-22 10:01:52.052 | INFO     | __main__:start_optimized_server:195 - 📊 使用完整功能版本 (ui/app.py)
2025-07-22 10:01:52.053 | INFO     | __main__:start_optimized_server:198 - 📡 WebSocket支持: ✅ 可用
2025-07-22 10:01:52.053 | INFO     | __main__:start_optimized_server:199 - 🔧 实时监控: 已启用
2025-07-22 10:01:52.053 | INFO     | __main__:start_optimized_server:200 - 🤖 DeepSeek AI: 已集成
2025-07-22 10:01:52.053 | INFO     | __main__:start_optimized_server:201 - 🔍 RAG检索: 已启用
2025-07-22 10:01:52.053 | INFO     | __main__:start_optimized_server:202 - 📊 数据处理: 已启用
2025-07-22 10:01:52.054 | INFO     | __main__:start_optimized_server:208 - 📊 实时监控系统已启动
2025-07-22 10:01:52.054 | INFO     | __main__:start_optimized_server:214 - 🔗 使用SocketIO启动（支持WebSocket）
2025-07-22 10:01:53.396 | INFO     | __main__:main:293 - 系统启动开始
2025-07-22 10:01:53.396 | INFO     | __main__:main:297 - 步骤 1/5: 系统健康检查
2025-07-22 10:01:53.498 | INFO     | __main__:main:301 - 步骤 2/5: 检查依赖
2025-07-22 10:01:53.891 | INFO     | __main__:main:307 - 步骤 3/5: 创建目录
2025-07-22 10:01:53.892 | INFO     | __main__:main:311 - 步骤 4/5: 显示启动信息
2025-07-22 10:01:53.893 | INFO     | __main__:main:315 - 步骤 5/5: 启动主服务器
2025-07-22 10:01:53.893 | INFO     | __main__:start_optimized_server:184 - 🚀 启动主服务器...
2025-07-22 10:01:57.673 | INFO     | data_processing.ocr_processor:<module>:27 - PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-22 10:01:57.674 | INFO     | data_processing.vector_processor:<module>:27 - 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-22 10:01:58.177 | INFO     | data_processing.modern_chroma_manager:_init_client:53 - ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-22 10:01:58.178 | INFO     | data_processing.modern_chroma_manager:_init_collection:75 - ✅ 获取现有集合: power_system_knowledge
2025-07-22 10:01:58.428 | INFO     | data_processing.chroma_manager:__init__:34 - ✅ 使用现代化Chroma管理器
2025-07-22 10:01:58.936 | INFO     | data_processing.advanced_retrieval_optimizer:_init_jieba:132 - PaddlePaddle未安装，使用jieba默认模式
2025-07-22 10:02:00.219 | INFO     | __main__:start_optimized_server:194 - 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-22 10:02:00.220 | INFO     | __main__:start_optimized_server:195 - 📊 使用完整功能版本 (ui/app.py)
2025-07-22 10:02:00.220 | INFO     | __main__:start_optimized_server:198 - 📡 WebSocket支持: ✅ 可用
2025-07-22 10:02:00.220 | INFO     | __main__:start_optimized_server:199 - 🔧 实时监控: 已启用
2025-07-22 10:02:00.220 | INFO     | __main__:start_optimized_server:200 - 🤖 DeepSeek AI: 已集成
2025-07-22 10:02:00.220 | INFO     | __main__:start_optimized_server:201 - 🔍 RAG检索: 已启用
2025-07-22 10:02:00.221 | INFO     | __main__:start_optimized_server:202 - 📊 数据处理: 已启用
2025-07-22 10:02:00.221 | INFO     | __main__:start_optimized_server:208 - 📊 实时监控系统已启动
2025-07-22 10:02:00.222 | INFO     | __main__:start_optimized_server:214 - 🔗 使用SocketIO启动（支持WebSocket）
2025-07-22 10:11:26.445 | INFO     | __main__:main:352 - 系统启动流程结束
2025-07-22 10:11:34.498 | INFO     | __main__:main:293 - 系统启动开始
2025-07-22 10:11:34.499 | INFO     | __main__:main:297 - 步骤 1/5: 系统健康检查
2025-07-22 10:11:34.600 | INFO     | __main__:main:301 - 步骤 2/5: 检查依赖
2025-07-22 10:11:35.241 | INFO     | __main__:main:307 - 步骤 3/5: 创建目录
2025-07-22 10:11:35.242 | INFO     | __main__:main:311 - 步骤 4/5: 显示启动信息
2025-07-22 10:11:35.244 | INFO     | __main__:main:315 - 步骤 5/5: 启动主服务器
2025-07-22 10:11:35.244 | INFO     | __main__:start_optimized_server:184 - 🚀 启动主服务器...
2025-07-22 10:11:41.228 | INFO     | data_processing.ocr_processor:<module>:27 - PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-22 10:11:41.230 | INFO     | data_processing.vector_processor:<module>:27 - 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-22 10:11:42.066 | INFO     | data_processing.modern_chroma_manager:_init_client:53 - ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-22 10:11:42.067 | INFO     | data_processing.modern_chroma_manager:_init_collection:75 - ✅ 获取现有集合: power_system_knowledge
2025-07-22 10:11:42.399 | INFO     | data_processing.chroma_manager:__init__:34 - ✅ 使用现代化Chroma管理器
2025-07-22 10:11:43.013 | INFO     | data_processing.advanced_retrieval_optimizer:_init_jieba:132 - PaddlePaddle未安装，使用jieba默认模式
2025-07-22 10:11:44.778 | INFO     | __main__:start_optimized_server:194 - 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-22 10:11:44.778 | INFO     | __main__:start_optimized_server:195 - 📊 使用完整功能版本 (ui/app.py)
2025-07-22 10:11:44.779 | INFO     | __main__:start_optimized_server:198 - 📡 WebSocket支持: ✅ 可用
2025-07-22 10:11:44.779 | INFO     | __main__:start_optimized_server:199 - 🔧 实时监控: 已启用
2025-07-22 10:11:44.779 | INFO     | __main__:start_optimized_server:200 - 🤖 DeepSeek AI: 已集成
2025-07-22 10:11:44.779 | INFO     | __main__:start_optimized_server:201 - 🔍 RAG检索: 已启用
2025-07-22 10:11:44.779 | INFO     | __main__:start_optimized_server:202 - 📊 数据处理: 已启用
2025-07-22 10:11:44.780 | INFO     | __main__:start_optimized_server:208 - 📊 实时监控系统已启动
2025-07-22 10:11:44.781 | INFO     | __main__:start_optimized_server:214 - 🔗 使用SocketIO启动（支持WebSocket）
