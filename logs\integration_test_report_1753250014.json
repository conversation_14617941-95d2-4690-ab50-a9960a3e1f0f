{"test_summary": {"total_tests": 8, "overall_score": 0.8525349882737516, "total_time": 25.897950410842896, "test_date": "2025-07-23T13:53:34.221349"}, "test_results": {"unified_retrieval": {"success_rate": 1.0, "avg_response_time": 5.8710410594940186, "avg_quality_score": 0.6779555555555555, "strategy_results": {"hybrid": {"success": true, "results_count": 3, "response_time": 17.611539840698242, "quality_score": 0.5794666666666667}, "professional": {"success": true, "results_count": 2, "response_time": 0.0005271434783935547, "quality_score": 0.7272000000000001}, "sparse": {"success": true, "results_count": 2, "response_time": 0.0010561943054199219, "quality_score": 0.7272000000000001}}, "score": 0.7393132395233468}, "professional_prompts": {"success_rate": 1.0, "avg_quality": 0.98325, "template_results": {"deepseek_r1_fault_analysis": {"success": true, "prompt_length": 2811, "quality_metrics": {"length": 1.0, "structure": 1.0, "professional": 1.0, "completeness": 1.0}, "overall_quality": 1.0}, "deepseek_v3_fault_analysis": {"success": true, "prompt_length": 866, "quality_metrics": {"length": 0.866, "structure": 1.0, "professional": 1.0, "completeness": 1.0}, "overall_quality": 0.9665}}, "score": 0.9933}, "deepseek_r1_reasoning": {"optimization_time": 0.0, "quality_score": 0.8860666666666666, "improvements_applied": true, "grpo_results": {"total_reward": 0.8860666666666666, "improvement_trend": true}, "verification_results": {"passed": true, "overall_score": 0.8860666666666666, "suggestions": ["推理质量良好"]}, "score": 0.8860666666666666}, "advanced_rag": {"response_time": 0.0010020732879638672, "results_count": 6, "quality_score": 0.8098333333333334, "techniques_used": ["hyde", "self_rag", "raptor"], "reasoning_chain": ["应用HyDE技术生成假设性文档", "应用Self-RAG技术进行自我反思", "应用RAPTOR技术构建抽象树"], "score": 0.8098333333333334}, "data_processing": {"document_type": "fault_report", "entities_count": 8, "relationships_count": 5, "technical_terms_count": 2, "quality_metrics": {"overall_score": 0.6417666666666666, "completeness": 0.7781666666666667, "accuracy": 0.4333333333333333, "professional_depth": 0.74}, "score": 0.6417666666666666}, "output_formatting": {"success_rate": 1.0, "format_results": {"structured_text": {"success": true, "report_length": 900, "contains_structure": true}, "html": {"success": true, "report_length": 2151, "contains_structure": true}, "json": {"success": true, "report_length": 58, "contains_structure": false}}, "score": 1.0}, "system_configuration": {"config_completeness": 1.0, "component_completeness": 1.0, "config_summary": {"system_info": {"name": "白银市电力故障诊断系统", "version": "2.0.0", "environment": "development"}, "components_enabled": {"deepseek_api": true, "unified_retriever": true, "advanced_rag": true, "data_processing": true, "monitoring": true}, "config_files": {"main": true, "database": true, "api": true, "models": true, "retrieval": true, "processing": true, "ui": true, "monitoring": true}, "validation_status": "通过", "last_updated": "2025-07-23T13:53:34.216347"}, "score": 1.0}, "end_to_end_integration": {"retrieval_success": true, "prompt_success": true, "processing_success": false, "formatting_success": true, "integration_score": 0.75, "integration_details": {"retrieval_details": {"success": true, "results_count": 2}, "prompt_details": {"success": true, "prompt_length": 866}, "processing_details": {"success": false, "error": "cannot access local variable 'doc_id' where it is not associated with a value"}, "formatting_details": {"success": true, "report_length": 623}}, "score": 0.75}}, "recommendations": ["建议改进数据质量评估标准和处理流程", "系统基本功能正常，建议针对低分项进行优化"], "system_status": "良好 - 系统基本满足要求，需少量优化"}