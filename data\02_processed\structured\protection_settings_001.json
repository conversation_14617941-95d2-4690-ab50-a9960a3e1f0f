{"protection_settings": {"station_id": "ST_110_001", "station_name": "110kV某某变电站", "voltage_level": "110kV", "configuration_date": "2024-01-15T10:00:00Z", "engineer": "张工程师", "approved_by": "李总工", "version": "v2.3"}, "transformer_protection": {"equipment_id": "TR001", "equipment_name": "1号主变压器", "rated_capacity": "50MVA", "voltage_ratio": "110/10.5kV", "protection_devices": [{"device_id": "PROT_TR001_DIFF", "device_name": "变压器差动保护", "manufacturer": "南瑞继保", "model": "RCS-978", "settings": {"differential_current_setting": {"pickup_current": "0.3A", "slope_1": "0.3", "slope_2": "0.8", "inflection_point_1": "1.0A", "inflection_point_2": "4.0A", "restraint_coefficient": "0.15"}, "second_harmonic_restraint": {"enable": true, "restraint_ratio": "0.15", "minimum_fundamental": "0.1A"}, "fifth_harmonic_restraint": {"enable": true, "restraint_ratio": "0.35"}}, "ct_ratios": {"high_voltage_side": "300/5A", "low_voltage_side": "3000/5A"}, "status": "in_service"}, {"device_id": "PROT_TR001_GAS", "device_name": "瓦斯保护", "manufacturer": "ABB", "model": "BUCHHOLZ-QJ1", "settings": {"light_gas_alarm": {"enable": true, "gas_volume": "200ml", "action": "alarm_only"}, "heavy_gas_trip": {"enable": true, "gas_flow_rate": "1.0m/s", "action": "trip_all_sides"}, "oil_flow_trip": {"enable": true, "oil_flow_rate": "0.5m/s", "action": "trip_all_sides"}}, "status": "in_service"}, {"device_id": "PROT_TR001_TEMP", "device_name": "温度保护", "settings": {"oil_temperature": {"alarm_setting": "55°C", "trip_setting": "75°C", "action_delay": "10s"}, "winding_temperature": {"alarm_setting": "65°C", "trip_setting": "85°C", "action_delay": "5s"}}, "status": "in_service"}]}, "line_protection": {"equipment_id": "LINE001", "equipment_name": "110kV出线1", "line_length": "25km", "conductor_type": "LGJ-240/30", "protection_devices": [{"device_id": "PROT_LINE001_DIST", "device_name": "距离保护", "manufacturer": "南瑞继保", "model": "RCS-902", "settings": {"zone_1": {"reach_impedance": "8.5Ω", "reach_percentage": "85%", "operating_time": "0ms", "enable": true}, "zone_2": {"reach_impedance": "15.0Ω", "reach_percentage": "120%", "operating_time": "300ms", "enable": true}, "zone_3": {"reach_impedance": "25.0Ω", "reach_percentage": "200%", "operating_time": "800ms", "enable": true}, "impedance_angle": "75°", "load_impedance_check": {"enable": true, "load_impedance": "45Ω"}}, "ct_ratio": "600/5A", "vt_ratio": "110000/100V", "status": "in_service"}, {"device_id": "PROT_LINE001_OC", "device_name": "过流保护", "settings": {"instantaneous_overcurrent": {"pickup_current": "12.0A", "operating_time": "0ms", "enable": true}, "time_overcurrent_1": {"pickup_current": "4.0A", "time_multiplier": "0.1", "curve_type": "IEC_NI", "enable": true}, "time_overcurrent_2": {"pickup_current": "2.0A", "time_multiplier": "0.2", "curve_type": "IEC_NI", "enable": true}}, "status": "in_service"}]}, "bus_protection": {"equipment_id": "BUS001", "equipment_name": "110kV母线", "bus_configuration": "single_bus_sectioned", "protection_devices": [{"device_id": "PROT_BUS001_DIFF", "device_name": "母线差动保护", "manufacturer": "南瑞继保", "model": "RCS-915", "settings": {"differential_current": {"pickup_current": "0.2A", "slope": "0.3", "high_set_current": "8.0A"}, "ct_saturation_detection": {"enable": true, "detection_level": "0.1A"}, "zone_selection": {"bus_section_1": true, "bus_section_2": true, "tie_breaker": true}}, "connected_feeders": ["TR001_HV", "LINE001", "LINE002", "LINE003"], "status": "in_service"}]}, "backup_protection": {"失灵保护": {"enable": true, "detection_time": "150ms", "trip_time": "200ms", "current_criterion": "0.2A"}, "低频减载": {"enable": true, "frequency_stages": [{"frequency": "49.5Hz", "load_shedding": "10%", "delay": "0.5s"}, {"frequency": "49.0Hz", "load_shedding": "15%", "delay": "0.5s"}, {"frequency": "48.5Hz", "load_shedding": "20%", "delay": "0.5s"}]}, "低压减载": {"enable": true, "voltage_stages": [{"voltage": "95kV", "load_shedding": "10%", "delay": "5s"}, {"voltage": "90kV", "load_shedding": "15%", "delay": "3s"}]}}, "communication_settings": {"protocol": "IEC61850", "network_topology": "star", "communication_devices": [{"device_id": "COMM_001", "device_type": "ethernet_switch", "ip_address": "************", "subnet_mask": "*************", "vlan_id": "100"}], "goose_settings": {"publish_datasets": ["TR001_TRIP", "LINE001_TRIP", "BUS001_TRIP"], "subscribe_datasets": ["REMOTE_TRIP_CMD", "SYSTEM_STATUS"]}}, "testing_records": {"last_test_date": "2024-01-10", "next_test_date": "2024-07-10", "test_results": {"primary_injection": "passed", "secondary_injection": "passed", "communication_test": "passed", "coordination_check": "passed"}, "test_engineer": "王工程师", "approved_by": "李总工"}}