"""
故障分析推理链

基于LangChain的故障分析推理链，整合多种工具进行综合故障分析
"""

from typing import Dict, Any, List, Optional
from langchain.chains.base import Chain
from langchain.llms.base import BaseLLM
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from langchain.prompts import PromptTemplate
from pydantic import Field
from loguru import logger

from ..tools.ocr_tool import OCRTool, TableExtractionTool
from ..tools.defect_tool import DefectDetectionTool, ImageAnalysisTool
from ..tools.equipment_tool import EquipmentLocatorTool, EquipmentStatusTool
from ..tools.waveform_tool import WaveformAnalysisTool
from retriever.unified_professional_retriever import get_unified_retriever


class FaultAnalysisChain(Chain):
    """故障分析推理链"""
    
    llm: BaseLLM = Field()
    knowledge_base: Any = Field()
    config: Dict[str, Any] = Field(default_factory=dict)
    
    # 工具实例
    ocr_tool: Optional[OCRTool] = None
    table_tool: Optional[TableExtractionTool] = None
    defect_tool: Optional[DefectDetectionTool] = None
    image_tool: Optional[ImageAnalysisTool] = None
    equipment_tool: Optional[EquipmentLocatorTool] = None
    status_tool: Optional[EquipmentStatusTool] = None
    waveform_tool: Optional[WaveformAnalysisTool] = None
    
    # Agent执行器
    agent_executor: Optional[AgentExecutor] = None
    
    input_key: str = "query"
    output_key: str = "result"
    
    def __init__(self, llm: BaseLLM, knowledge_base: Any, config: Dict[str, Any], **kwargs):
        super().__init__(llm=llm, knowledge_base=knowledge_base, config=config, **kwargs)
        self._initialize_tools()
        self._create_agent()
    
    def _initialize_tools(self):
        """初始化工具"""
        try:
            # 初始化各种工具
            self.ocr_tool = OCRTool(self.config)
            self.table_tool = TableExtractionTool(self.config)
            self.defect_tool = DefectDetectionTool(self.config)
            self.image_tool = ImageAnalysisTool(self.config)
            self.equipment_tool = EquipmentLocatorTool(self.config)
            self.status_tool = EquipmentStatusTool(self.config)
            self.waveform_tool = WaveformAnalysisTool()
            
            logger.info("故障分析工具初始化完成")
            
        except Exception as e:
            logger.error(f"工具初始化失败: {str(e)}")
    
    def _create_agent(self):
        """创建Agent"""
        try:
            # 创建工具列表
            tools = []
            
            if self.ocr_tool:
                tools.append(Tool.from_function(
                    func=self.ocr_tool._run,
                    name=self.ocr_tool.name,
                    description=self.ocr_tool.description
                ))
            
            if self.table_tool:
                tools.append(Tool.from_function(
                    func=self.table_tool._run,
                    name=self.table_tool.name,
                    description=self.table_tool.description
                ))
            
            if self.defect_tool:
                tools.append(Tool.from_function(
                    func=self.defect_tool._run,
                    name=self.defect_tool.name,
                    description=self.defect_tool.description
                ))
            
            if self.image_tool:
                tools.append(Tool.from_function(
                    func=self.image_tool._run,
                    name=self.image_tool.name,
                    description=self.image_tool.description
                ))
            
            if self.equipment_tool:
                tools.append(Tool.from_function(
                    func=self.equipment_tool._run,
                    name=self.equipment_tool.name,
                    description=self.equipment_tool.description
                ))
            
            if self.status_tool:
                tools.append(Tool.from_function(
                    func=self.status_tool._run,
                    name=self.status_tool.name,
                    description=self.status_tool.description
                ))
            
            if self.waveform_tool:
                tools.append(Tool.from_function(
                    func=self.waveform_tool._run,
                    name=self.waveform_tool.name,
                    description=self.waveform_tool.description
                ))
            
            # 添加知识库搜索工具
            tools.append(Tool.from_function(
                func=self._search_knowledge_base,
                name="knowledge_search",
                description="搜索知识库中的相关文档和图像信息。输入查询文本，返回相关的技术文档、历史案例等。"
            ))
            
            # 创建故障分析提示词模板
            prompt_template = self._create_fault_analysis_prompt()
            
            # 创建ReAct Agent
            agent = create_react_agent(
                llm=self.llm,
                tools=tools,
                prompt=prompt_template
            )
            
            # 创建Agent执行器
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=10,
                handle_parsing_errors=True
            )
            
            logger.info("故障分析Agent创建完成")
            
        except Exception as e:
            logger.error(f"Agent创建失败: {str(e)}")
    
    def _create_fault_analysis_prompt(self) -> PromptTemplate:
        """创建故障分析提示词模板"""
        template = """你是一个电力系统故障分析专家，具备丰富的变电站设备故障诊断经验。

你的任务是根据用户提供的故障信息，进行全面的故障分析，包括：
1. 故障现象分析
2. 可能原因分析  
3. 设备状态检查
4. 历史案例查询
5. 处理建议

你可以使用以下工具：
{tools}

请按照以下步骤进行分析：

1. 首先理解用户的故障描述
2. 如果有图像信息，使用相应工具进行图像分析
3. 查询相关设备信息和状态
4. 搜索知识库中的相关案例和技术文档
5. 如果有波形数据，进行波形分析
6. 综合所有信息，给出故障分析结论和处理建议

工具使用格式：
Thought: 我需要分析这个故障...
Action: [工具名称]
Action Input: [工具输入]
Observation: [工具输出]
... (重复直到有足够信息)
Thought: 我现在有足够的信息来回答问题了
Final Answer: [最终的故障分析结果]

用户问题: {input}

{agent_scratchpad}"""

        return PromptTemplate(
            template=template,
            input_variables=["input", "tools", "agent_scratchpad"]
        )
    
    def _search_knowledge_base(self, query: str) -> str:
        """搜索知识库"""
        try:
            results = self.knowledge_base.search(query, search_type="multimodal", top_k=5)
            
            if not results or not results.get("results"):
                return "未找到相关的知识库信息"
            
            # 格式化搜索结果
            output_lines = ["知识库搜索结果:"]
            
            multimodal_results = results.get("results", {})
            
            # 文本结果
            text_results = multimodal_results.get("text", [])
            if text_results:
                output_lines.append(f"\n相关文档 ({len(text_results)} 个):")
                for i, result in enumerate(text_results[:3]):  # 最多显示3个
                    output_lines.append(f"{i+1}. {result.get('metadata', {}).get('source', 'unknown')}")
                    output_lines.append(f"   相关度: {result.get('score', 0):.2f}")
                    content = result.get('content', '')[:200]  # 截取前200字符
                    output_lines.append(f"   内容摘要: {content}...")
            
            # 图像结果
            image_results = multimodal_results.get("images", [])
            if image_results:
                output_lines.append(f"\n相关图像 ({len(image_results)} 个):")
                for i, result in enumerate(image_results[:3]):
                    output_lines.append(f"{i+1}. {result.get('filename', 'unknown')}")
                    output_lines.append(f"   相关度: {result.get('score', 0):.2f}")
                    ocr_text = result.get('ocr_text', '')[:100]
                    if ocr_text:
                        output_lines.append(f"   图像文字: {ocr_text}...")
            
            return "\n".join(output_lines)
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {str(e)}")
            return f"知识库搜索失败: {str(e)}"
    
    @property
    def input_keys(self) -> List[str]:
        """输入键"""
        return [self.input_key]
    
    @property
    def output_keys(self) -> List[str]:
        """输出键"""
        return [self.output_key]
    
    def _call(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行故障分析链"""
        try:
            query = inputs[self.input_key]
            
            if not self.agent_executor:
                return {self.output_key: "故障分析Agent未正确初始化"}
            
            # 执行Agent
            result = self.agent_executor.invoke({"input": query})
            
            return {self.output_key: result.get("output", "分析完成，但未获得结果")}
            
        except Exception as e:
            logger.error(f"故障分析链执行失败: {str(e)}")
            return {self.output_key: f"故障分析失败: {str(e)}"}
    
    def analyze_fault(self, fault_description: str, images: Optional[List[str]] = None, 
                     waveform_data: Optional[str] = None) -> Dict[str, Any]:
        """
        执行故障分析
        
        Args:
            fault_description: 故障描述
            images: 相关图像路径列表
            waveform_data: 波形数据
            
        Returns:
            故障分析结果
        """
        try:
            # 构建完整的查询
            query_parts = [f"故障描述: {fault_description}"]
            
            if images:
                query_parts.append(f"相关图像: {', '.join(images)}")
            
            if waveform_data:
                query_parts.append(f"波形数据: {waveform_data}")
            
            full_query = "\n".join(query_parts)
            
            # 执行分析
            result = self._call({self.input_key: full_query})
            
            return {
                "success": True,
                "analysis": result[self.output_key],
                "query": full_query
            }
            
        except Exception as e:
            logger.error(f"故障分析执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "query": fault_description
            }
