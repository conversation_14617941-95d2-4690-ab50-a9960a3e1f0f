"""
配置管理模块 - 已重构为使用统一配置管理器

此文件保留为向后兼容性，所有功能已迁移到 unified_config_manager.py
建议直接使用 get_unified_config() 或 get_config() 函数
"""

# 直接导入统一配置管理器的所有功能
from .unified_config_manager import (
    UnifiedConfigManager,
    get_unified_config,
    get_config,
    get_server_port,
    get_server_host,
    is_production
)

# 向后兼容的别名
ConfigManager = UnifiedConfigManager
config = get_unified_config()

def load_config_from_file(config_path: str) -> None:
    """从指定文件加载配置"""
    config.load_config(config_path)

def get_upload_dir() -> str:
    """获取上传目录"""
    return get_config("upload.upload_path", "./uploads")
