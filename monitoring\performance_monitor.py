#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控系统
"""

import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    timestamp: float
    metric_name: str
    value: float
    unit: str
    tags: Dict[str, str] = None
    
    def to_dict(self) -> Dict:
        return asdict(self)

@dataclass
class SystemHealthStatus:
    """系统健康状态"""
    timestamp: float
    overall_health: str  # healthy, warning, critical
    api_status: str
    rag_status: str
    knowledge_base_status: str
    response_time_avg: float
    error_rate: float
    active_users: int

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, storage_path: str = "monitoring/data"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 性能指标存储
        self.metrics: List[PerformanceMetric] = []
        self.max_metrics = 10000  # 最大存储指标数量
        
        # 实时统计
        self.request_times: List[float] = []
        self.error_count = 0
        self.total_requests = 0
        self.active_sessions = set()
        
        # 监控配置
        self.monitoring_enabled = True
        self.auto_save_interval = 300  # 5分钟自动保存
        self.last_save_time = time.time()
        
        # 启动后台监控线程
        self._start_background_monitoring()

    def record_request_start(self, request_id: str, endpoint: str, user_id: str = None) -> Dict:
        """记录请求开始"""
        if not self.monitoring_enabled:
            return {}
        
        request_info = {
            'request_id': request_id,
            'endpoint': endpoint,
            'user_id': user_id,
            'start_time': time.time(),
            'status': 'started'
        }
        
        # 记录活跃用户
        if user_id:
            self.active_sessions.add(user_id)
        
        self.total_requests += 1
        
        return request_info

    def record_request_end(self, request_info: Dict, success: bool = True, error_type: str = None):
        """记录请求结束"""
        if not self.monitoring_enabled or not request_info:
            return
        
        end_time = time.time()
        duration = end_time - request_info['start_time']
        
        # 记录响应时间
        self.request_times.append(duration)
        if len(self.request_times) > 1000:  # 保持最近1000个请求的时间
            self.request_times = self.request_times[-1000:]
        
        # 记录错误
        if not success:
            self.error_count += 1
        
        # 记录性能指标
        self.add_metric('response_time', duration, 'seconds', {
            'endpoint': request_info['endpoint'],
            'success': str(success),
            'error_type': error_type or 'none'
        })

    def add_metric(self, name: str, value: float, unit: str, tags: Dict[str, str] = None):
        """添加性能指标"""
        if not self.monitoring_enabled:
            return
        
        metric = PerformanceMetric(
            timestamp=time.time(),
            metric_name=name,
            value=value,
            unit=unit,
            tags=tags or {}
        )
        
        self.metrics.append(metric)
        
        # 限制指标数量
        if len(self.metrics) > self.max_metrics:
            self.metrics = self.metrics[-self.max_metrics//2:]  # 保留一半

    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前统计信息"""
        current_time = time.time()
        
        # 计算平均响应时间
        avg_response_time = sum(self.request_times) / len(self.request_times) if self.request_times else 0
        
        # 计算错误率
        error_rate = (self.error_count / self.total_requests * 100) if self.total_requests > 0 else 0
        
        # 获取最近的指标
        recent_metrics = [m for m in self.metrics if current_time - m.timestamp < 3600]  # 最近1小时
        
        return {
            'timestamp': current_time,
            'total_requests': self.total_requests,
            'error_count': self.error_count,
            'error_rate': error_rate,
            'avg_response_time': avg_response_time,
            'active_sessions': len(self.active_sessions),
            'recent_metrics_count': len(recent_metrics),
            'monitoring_enabled': self.monitoring_enabled
        }

    def get_health_status(self) -> SystemHealthStatus:
        """获取系统健康状态"""
        current_time = time.time()
        stats = self.get_current_stats()
        
        # 评估整体健康状态
        overall_health = "healthy"
        if stats['error_rate'] > 10:
            overall_health = "critical"
        elif stats['error_rate'] > 5 or stats['avg_response_time'] > 10:
            overall_health = "warning"
        
        # 评估各组件状态
        api_status = "healthy"
        if stats['error_rate'] > 15:
            api_status = "critical"
        elif stats['error_rate'] > 8:
            api_status = "warning"
        
        rag_status = "healthy"
        if stats['avg_response_time'] > 15:
            rag_status = "warning"
        elif stats['avg_response_time'] > 30:
            rag_status = "critical"
        
        return SystemHealthStatus(
            timestamp=current_time,
            overall_health=overall_health,
            api_status=api_status,
            rag_status=rag_status,
            knowledge_base_status="healthy",  # 简化处理
            response_time_avg=stats['avg_response_time'],
            error_rate=stats['error_rate'],
            active_users=stats['active_sessions']
        )

    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成性能报告"""
        current_time = time.time()
        start_time = current_time - (hours * 3600)
        
        # 筛选时间范围内的指标
        period_metrics = [m for m in self.metrics if m.timestamp >= start_time]
        
        # 按指标名称分组
        metrics_by_name = {}
        for metric in period_metrics:
            name = metric.metric_name
            if name not in metrics_by_name:
                metrics_by_name[name] = []
            metrics_by_name[name].append(metric.value)
        
        # 计算统计信息
        report = {
            'period': f"{hours} hours",
            'start_time': start_time,
            'end_time': current_time,
            'total_metrics': len(period_metrics),
            'metrics_summary': {}
        }
        
        for name, values in metrics_by_name.items():
            if values:
                report['metrics_summary'][name] = {
                    'count': len(values),
                    'avg': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'latest': values[-1] if values else 0
                }
        
        return report

    def save_metrics(self, filename: str = None):
        """保存指标到文件"""
        if not filename:
            filename = f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = self.storage_path / filename
        
        try:
            data = {
                'timestamp': time.time(),
                'stats': self.get_current_stats(),
                'health_status': asdict(self.get_health_status()),
                'metrics': [m.to_dict() for m in self.metrics[-1000:]]  # 保存最近1000个指标
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"性能指标已保存到: {filepath}")
            
        except Exception as e:
            logger.error(f"保存性能指标失败: {e}")

    def load_metrics(self, filename: str):
        """从文件加载指标"""
        filepath = self.storage_path / filename
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 恢复指标
            if 'metrics' in data:
                loaded_metrics = []
                for m_data in data['metrics']:
                    metric = PerformanceMetric(**m_data)
                    loaded_metrics.append(metric)
                
                self.metrics.extend(loaded_metrics)
                logger.info(f"已加载 {len(loaded_metrics)} 个性能指标")
            
        except Exception as e:
            logger.error(f"加载性能指标失败: {e}")

    def _start_background_monitoring(self):
        """启动后台监控线程"""
        def monitor_loop():
            while self.monitoring_enabled:
                try:
                    # 定期保存指标
                    current_time = time.time()
                    if current_time - self.last_save_time > self.auto_save_interval:
                        self.save_metrics()
                        self.last_save_time = current_time
                    
                    # 清理过期的活跃会话
                    # 这里简化处理，实际应该基于最后活动时间
                    
                    time.sleep(60)  # 每分钟检查一次
                    
                except Exception as e:
                    logger.error(f"后台监控异常: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        logger.info("性能监控后台线程已启动")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_enabled = False
        self.save_metrics("final_metrics.json")
        logger.info("性能监控已停止")

# 全局性能监控实例
performance_monitor = PerformanceMonitor()
