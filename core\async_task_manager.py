"""
异步任务管理器

提供文件处理的异步任务管理功能
生产环境优化版本，支持任务优先级、资源限制、性能监控
"""

import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

# 尝试导入缓存管理器
try:
    from utils.optimized_cache import analysis_cache
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: str
    task_type: str
    status: TaskStatus
    created_at: datetime
    updated_at: datetime
    priority: TaskPriority = TaskPriority.NORMAL
    progress: float = 0.0
    message: str = ""
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    execution_time: Optional[float] = None
    memory_usage: Optional[int] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        data['priority'] = self.priority.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data


class AsyncTaskManager:
    """异步任务管理器 - 生产环境优化版"""

    def __init__(self, max_concurrent_tasks: int = 5, cleanup_interval: int = 3600):
        """
        初始化任务管理器

        Args:
            max_concurrent_tasks: 最大并发任务数
            cleanup_interval: 清理间隔（秒）
        """
        self.tasks: Dict[str, TaskInfo] = {}
        self.max_concurrent_tasks = max_concurrent_tasks
        self.cleanup_interval = cleanup_interval
        self._semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self._cleanup_task = None
        self._running_tasks: Dict[str, asyncio.Task] = {}

        # 性能统计
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0,
            'average_execution_time': 0.0
        }

        # 线程池用于CPU密集型任务
        self._thread_pool = ThreadPoolExecutor(max_workers=max_concurrent_tasks)
        
    async def start(self):
        """启动任务管理器"""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("异步任务管理器已启动")
    
    async def stop(self):
        """停止任务管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
        
        # 取消所有运行中的任务
        for task in self._running_tasks.values():
            task.cancel()
        
        if self._running_tasks:
            await asyncio.gather(*self._running_tasks.values(), return_exceptions=True)
        
        logger.info("异步任务管理器已停止")
    
    def create_task(self, task_type: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        创建新任务
        
        Args:
            task_type: 任务类型
            metadata: 任务元数据
            
        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        now = datetime.now()
        
        task_info = TaskInfo(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.PENDING,
            created_at=now,
            updated_at=now,
            metadata=metadata or {}
        )
        
        self.tasks[task_id] = task_info
        logger.info(f"创建任务: {task_id} ({task_type})")
        return task_id
    
    async def execute_task(self, task_id: str, task_func: Callable, *args, **kwargs) -> bool:
        """
        执行异步任务
        
        Args:
            task_id: 任务ID
            task_func: 任务函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            是否成功启动任务
        """
        if task_id not in self.tasks:
            logger.error(f"任务不存在: {task_id}")
            return False
        
        task_info = self.tasks[task_id]
        if task_info.status != TaskStatus.PENDING:
            logger.warning(f"任务状态不正确: {task_id} ({task_info.status})")
            return False
        
        # 创建异步任务
        async_task = asyncio.create_task(self._run_task(task_id, task_func, *args, **kwargs))
        self._running_tasks[task_id] = async_task
        
        return True
    
    async def _run_task(self, task_id: str, task_func: Callable, *args, **kwargs):
        """运行单个任务"""
        async with self._semaphore:
            task_info = self.tasks[task_id]
            
            try:
                # 更新任务状态为处理中
                self.update_task_status(task_id, TaskStatus.PROCESSING, "任务开始处理")
                
                # 执行任务
                if asyncio.iscoroutinefunction(task_func):
                    result = await task_func(task_id, self.update_task_progress, *args, **kwargs)
                else:
                    # 对于同步函数，在线程池中执行
                    loop = asyncio.get_event_loop()
                    result = await loop.run_in_executor(
                        None, 
                        lambda: task_func(task_id, self.update_task_progress, *args, **kwargs)
                    )
                
                # 任务完成
                self.update_task_status(task_id, TaskStatus.COMPLETED, "任务完成", result)
                
            except Exception as e:
                logger.error(f"任务执行失败 {task_id}: {str(e)}")
                self.update_task_status(task_id, TaskStatus.FAILED, f"任务失败: {str(e)}")
                
            finally:
                # 清理运行中的任务记录
                if task_id in self._running_tasks:
                    del self._running_tasks[task_id]
    
    def update_task_status(self, task_id: str, status: TaskStatus, message: str = "", 
                          result: Optional[Dict[str, Any]] = None):
        """更新任务状态"""
        if task_id not in self.tasks:
            return
        
        task_info = self.tasks[task_id]
        task_info.status = status
        task_info.message = message
        task_info.updated_at = datetime.now()
        
        if result is not None:
            task_info.result = result
        
        logger.info(f"任务状态更新: {task_id} -> {status.value} ({message})")
    
    def update_task_progress(self, task_id: str, progress: float, message: str = ""):
        """更新任务进度"""
        if task_id not in self.tasks:
            return
        
        task_info = self.tasks[task_id]
        task_info.progress = max(0.0, min(1.0, progress))
        task_info.updated_at = datetime.now()
        
        if message:
            task_info.message = message
        
        logger.debug(f"任务进度更新: {task_id} -> {progress:.1%} ({message})")
    
    def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        task_info = self.tasks.get(task_id)
        return task_info.status if task_info else None
    
    def list_tasks(self, status: Optional[TaskStatus] = None, 
                   task_type: Optional[str] = None) -> List[TaskInfo]:
        """列出任务"""
        tasks = list(self.tasks.values())
        
        if status:
            tasks = [t for t in tasks if t.status == status]
        
        if task_type:
            tasks = [t for t in tasks if t.task_type == task_type]
        
        return sorted(tasks, key=lambda t: t.created_at, reverse=True)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False
        
        task_info = self.tasks[task_id]
        
        # 如果任务正在运行，取消异步任务
        if task_id in self._running_tasks:
            self._running_tasks[task_id].cancel()
            del self._running_tasks[task_id]
        
        # 更新任务状态
        self.update_task_status(task_id, TaskStatus.CANCELLED, "任务已取消")
        return True
    
    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_old_tasks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理任务时出错: {str(e)}")
    
    async def _cleanup_old_tasks(self):
        """清理旧任务"""
        cutoff_time = datetime.now() - timedelta(hours=24)  # 清理24小时前的任务
        
        tasks_to_remove = []
        for task_id, task_info in self.tasks.items():
            if (task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] 
                and task_info.updated_at < cutoff_time):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
        
        if tasks_to_remove:
            logger.info(f"清理了 {len(tasks_to_remove)} 个旧任务")


# 全局任务管理器实例
task_manager = AsyncTaskManager()


async def get_task_manager() -> AsyncTaskManager:
    """获取任务管理器实例"""
    if task_manager._cleanup_task is None:
        await task_manager.start()
    return task_manager
