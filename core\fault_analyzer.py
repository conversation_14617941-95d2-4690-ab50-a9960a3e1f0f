"""
故障分析器

核心故障分析功能，整合各种分析工具和推理链
生产环境优化版本，支持缓存、性能监控、错误处理增强
"""

import os
import time
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from langchain_modules.chains.fault_analysis_chain import FaultAnalysisChain
from langchain_modules.chains.document_qa_chain import DocumentQ<PERSON>hain
from retriever.unified_professional_retriever import get_unified_retriever, UnifiedProfessionalRetriever
from data_processing.image_processor import ImageProcessor
from data_processing.ocr_processor import OCRProcessor

# 尝试导入缓存
try:
    from utils.optimized_cache import analysis_cache
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False
    logger.warning("缓存模块不可用，将不使用缓存功能")


class FaultAnalyzer:
    """故障分析器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, llm=None, knowledge_base=None):
        self.config = config or {}
        self.llm = llm

        # 初始化知识库
        if knowledge_base:
            self.knowledge_base = knowledge_base
        else:
            try:
                kb_config = {
                    "chroma_path": "./embeddings/chroma_store_new",
                    "collection_name": "baiyin_power_fault_collection",
                    "cache_enabled": True
                }
                self.knowledge_base = get_unified_retriever(kb_config)
            except Exception as e:
                logger.error(f"知识库初始化失败: {e}")
                self.knowledge_base = None
        
        # 初始化处理器
        try:
            image_config = self.config.get("data_processing", {}).get("image", {}) if self.config else {}
            self.image_processor = ImageProcessor(image_config)
        except Exception as e:
            logger.warning(f"图像处理器初始化失败: {e}")
            self.image_processor = None

        try:
            ocr_config = self.config.get("data_processing", {}).get("ocr", {}) if self.config else {}
            self.ocr_processor = OCRProcessor(ocr_config)
        except Exception as e:
            logger.warning(f"OCR处理器初始化失败: {e}")
            self.ocr_processor = None
        
        # 初始化推理链
        self.fault_chain = None
        self.qa_chain = None
        
        self._initialize_chains()
    
    def _initialize_chains(self):
        """初始化推理链"""
        try:
            from langchain_modules.prompts.prompt_manager import PromptManager
            
            prompt_manager = PromptManager(self.config)
            
            # 初始化故障分析链
            self.fault_chain = FaultAnalysisChain(
                llm=self.llm,
                knowledge_base=self.knowledge_base,
                config=self.config
            )
            
            # 初始化文档问答链
            self.qa_chain = DocumentQAChain(
                llm=self.llm,
                knowledge_base=self.knowledge_base,
                prompt_manager=prompt_manager,
                config=self.config
            )
            
            logger.info("故障分析推理链初始化完成")
            
        except Exception as e:
            logger.error(f"推理链初始化失败: {str(e)}")
    
    def analyze_fault(self, fault_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        综合故障分析
        
        Args:
            fault_info: 故障信息，包含描述、图像、数据等
            
        Returns:
            故障分析结果
        """
        try:
            analysis_id = self._generate_analysis_id()
            
            logger.info(f"开始故障分析 {analysis_id}")
            
            # 提取故障信息
            fault_description = fault_info.get("description", "")
            images = fault_info.get("images", [])
            waveform_data = fault_info.get("waveform_data")
            equipment_info = fault_info.get("equipment_info", {})
            
            # 预处理图像
            processed_images = self._preprocess_images(images)
            
            # 执行故障分析
            if self.fault_chain:
                analysis_result = self.fault_chain.analyze_fault(
                    fault_description=fault_description,
                    images=images,
                    waveform_data=waveform_data
                )
            else:
                analysis_result = self._basic_fault_analysis(fault_info)
            
            # 查询相关案例
            similar_cases = self._find_similar_cases(fault_description)
            
            # 生成分析报告
            report = self._generate_analysis_report(
                analysis_id=analysis_id,
                fault_info=fault_info,
                analysis_result=analysis_result,
                processed_images=processed_images,
                similar_cases=similar_cases
            )
            
            logger.info(f"故障分析完成 {analysis_id}")
            
            return {
                "success": True,
                "analysis_id": analysis_id,
                "report": report,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"故障分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _generate_analysis_id(self) -> str:
        """生成分析ID"""
        return f"FA_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def _preprocess_images(self, images: List[str]) -> List[Dict[str, Any]]:
        """预处理图像"""
        processed_images = []
        
        for image_path in images:
            try:
                if not os.path.exists(image_path):
                    logger.warning(f"图像文件不存在: {image_path}")
                    continue
                
                # 图像处理
                image_info = self.image_processor.process_image(image_path)
                
                # OCR识别
                ocr_result = self.ocr_processor.extract_text(image_path)
                
                # 缺陷检测
                defects = self.image_processor.detect_defects(image_path)
                
                processed_info = {
                    "path": image_path,
                    "filename": os.path.basename(image_path),
                    "image_info": image_info,
                    "ocr_result": ocr_result,
                    "defects": defects
                }
                
                processed_images.append(processed_info)
                
            except Exception as e:
                logger.error(f"图像预处理失败 {image_path}: {str(e)}")
        
        return processed_images
    
    def _basic_fault_analysis(self, fault_info: Dict[str, Any]) -> Dict[str, Any]:
        """基础故障分析（当推理链不可用时）"""
        try:
            description = fault_info.get("description", "")
            
            # 简单的关键词分析
            analysis = {
                "fault_type": "unknown",
                "severity": "medium",
                "possible_causes": [],
                "recommendations": []
            }
            
            description_lower = description.lower()
            
            # 故障类型识别
            if any(word in description_lower for word in ["短路", "接地", "相间"]):
                analysis["fault_type"] = "electrical_fault"
                analysis["severity"] = "high"
            elif any(word in description_lower for word in ["过热", "温度", "发热"]):
                analysis["fault_type"] = "thermal_fault"
                analysis["severity"] = "medium"
            elif any(word in description_lower for word in ["振动", "噪音", "异响"]):
                analysis["fault_type"] = "mechanical_fault"
                analysis["severity"] = "medium"
            elif any(word in description_lower for word in ["绝缘", "泄漏", "放电"]):
                analysis["fault_type"] = "insulation_fault"
                analysis["severity"] = "high"
            
            # 基本建议
            analysis["recommendations"] = [
                "立即停止设备运行",
                "进行详细检查",
                "联系专业技术人员",
                "记录故障现象"
            ]
            
            return {
                "success": True,
                "analysis": analysis,
                "method": "basic_analysis"
            }
            
        except Exception as e:
            logger.error(f"基础故障分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _find_similar_cases(self, fault_description: str) -> List[Dict[str, Any]]:
        """查找相似案例"""
        try:
            # 使用知识库搜索相似案例
            search_results = self.knowledge_base.search(
                query=fault_description,
                search_type="multimodal",
                top_k=5
            )
            
            similar_cases = []
            
            if search_results.get("results"):
                results = search_results["results"]
                
                # 处理文本案例
                text_results = results.get("text", [])
                for result in text_results:
                    case = {
                        "type": "text",
                        "source": result.get("metadata", {}).get("source", "unknown"),
                        "similarity": result.get("score", 0),
                        "content": result.get("content", "")[:300] + "...",
                        "metadata": result.get("metadata", {})
                    }
                    similar_cases.append(case)
                
                # 处理图像案例
                image_results = results.get("images", [])
                for result in image_results:
                    case = {
                        "type": "image",
                        "source": result.get("filename", "unknown"),
                        "similarity": result.get("score", 0),
                        "ocr_text": result.get("ocr_text", ""),
                        "path": result.get("path", "")
                    }
                    similar_cases.append(case)
            
            return similar_cases
            
        except Exception as e:
            logger.error(f"查找相似案例失败: {str(e)}")
            return []
    
    def _generate_analysis_report(self, analysis_id: str, fault_info: Dict[str, Any],
                                analysis_result: Dict[str, Any], processed_images: List[Dict[str, Any]],
                                similar_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成分析报告"""
        try:
            report = {
                "analysis_id": analysis_id,
                "timestamp": datetime.now().isoformat(),
                "fault_info": {
                    "description": fault_info.get("description", ""),
                    "equipment": fault_info.get("equipment_info", {}),
                    "images_count": len(fault_info.get("images", [])),
                    "has_waveform": bool(fault_info.get("waveform_data"))
                },
                "analysis_result": analysis_result,
                "image_analysis": [],
                "similar_cases": similar_cases[:3],  # 最多3个相似案例
                "recommendations": [],
                "summary": ""
            }
            
            # 图像分析摘要
            for img_info in processed_images:
                img_summary = {
                    "filename": img_info.get("filename", ""),
                    "has_defects": bool(img_info.get("defects", {}).get("total_defects", 0)),
                    "ocr_confidence": img_info.get("ocr_result", {}).get("confidence", 0),
                    "ocr_text_preview": img_info.get("ocr_result", {}).get("full_text", "")[:100]
                }
                report["image_analysis"].append(img_summary)
            
            # 生成建议
            if analysis_result.get("success"):
                analysis_data = analysis_result.get("analysis", {})
                if isinstance(analysis_data, dict):
                    report["recommendations"] = analysis_data.get("recommendations", [])
                else:
                    # 如果是字符串形式的分析结果，提取建议
                    report["recommendations"] = self._extract_recommendations(str(analysis_data))
            
            # 生成摘要
            report["summary"] = self._generate_summary(report)
            
            return report
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {str(e)}")
            return {
                "analysis_id": analysis_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _extract_recommendations(self, analysis_text: str) -> List[str]:
        """从分析文本中提取建议"""
        try:
            recommendations = []
            
            # 简单的建议提取
            lines = analysis_text.split('\n')
            in_recommendations = False
            
            for line in lines:
                line = line.strip()
                if any(keyword in line.lower() for keyword in ["建议", "处理", "措施", "recommendation"]):
                    in_recommendations = True
                    continue
                
                if in_recommendations and line:
                    if line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '•')):
                        recommendations.append(line)
                    elif len(recommendations) > 0 and not line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '•')):
                        break
            
            return recommendations[:5]  # 最多5个建议
            
        except Exception as e:
            logger.error(f"提取建议失败: {str(e)}")
            return []
    
    def _generate_summary(self, report: Dict[str, Any]) -> str:
        """生成分析摘要"""
        try:
            summary_parts = []
            
            # 故障基本信息
            fault_desc = report.get("fault_info", {}).get("description", "")
            if fault_desc:
                summary_parts.append(f"故障描述: {fault_desc[:100]}...")
            
            # 分析结果
            analysis_result = report.get("analysis_result", {})
            if analysis_result.get("success"):
                summary_parts.append("分析状态: 完成")
            else:
                summary_parts.append("分析状态: 失败")
            
            # 图像分析
            image_count = len(report.get("image_analysis", []))
            if image_count > 0:
                defect_count = sum(1 for img in report.get("image_analysis", []) if img.get("has_defects"))
                summary_parts.append(f"图像分析: {image_count}张图像，{defect_count}张检测到缺陷")
            
            # 相似案例
            case_count = len(report.get("similar_cases", []))
            if case_count > 0:
                summary_parts.append(f"相似案例: 找到{case_count}个相关案例")
            
            return "; ".join(summary_parts)
            
        except Exception as e:
            logger.error(f"生成摘要失败: {str(e)}")
            return "摘要生成失败"
    
    def query_fault_knowledge(self, question: str) -> Dict[str, Any]:
        """
        故障知识查询
        
        Args:
            question: 查询问题
            
        Returns:
            查询结果
        """
        try:
            if self.qa_chain:
                result = self.qa_chain.ask_question(question)
                return result
            else:
                # 直接使用知识库搜索
                search_results = self.knowledge_base.search(question, search_type="multimodal", top_k=3)
                
                return {
                    "success": True,
                    "question": question,
                    "answer": "基于知识库搜索的结果",
                    "search_results": search_results
                }
                
        except Exception as e:
            logger.error(f"故障知识查询失败: {str(e)}")
            return {
                "success": False,
                "question": question,
                "error": str(e)
            }
