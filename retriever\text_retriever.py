"""
文本检索模块

基于向量数据库的文本检索功能
"""

import os
import json
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from loguru import logger

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn不可用，将使用基础文本匹配")

try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logger.warning("jieba不可用，将使用基础分词")


class TextRetriever:
    """文本检索器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.index_path = config.get("index_path", "./embeddings/text_index")
        self.metadata_path = config.get("metadata_path", "./embeddings/text_metadata.json")
        
        # 初始化向量化器
        if SKLEARN_AVAILABLE:
            self.vectorizer = TfidfVectorizer(
                max_features=5000,
                stop_words=None,
                ngram_range=(1, 2)
            )
        else:
            self.vectorizer = None
        
        # 文档存储
        self.documents = []
        self.document_vectors = None
        self.metadata = {}
        
        # 加载现有索引
        self._load_index()
    
    def _load_index(self):
        """加载现有索引"""
        try:
            if os.path.exists(self.metadata_path):
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
                
                self.documents = self.metadata.get('documents', [])
                logger.info(f"加载文本索引: {len(self.documents)} 个文档")
                
                # 重建向量索引
                if self.documents and SKLEARN_AVAILABLE:
                    self._rebuild_vectors()
            else:
                logger.info("未找到现有文本索引，将创建新索引")
        except Exception as e:
            logger.error(f"加载文本索引失败: {e}")
            self.documents = []
            self.metadata = {}
    
    def _rebuild_vectors(self):
        """重建向量索引"""
        try:
            if not self.documents or not SKLEARN_AVAILABLE:
                return
            
            texts = [doc.get('content', '') for doc in self.documents]
            self.document_vectors = self.vectorizer.fit_transform(texts)
            logger.info("文本向量索引重建完成")
        except Exception as e:
            logger.error(f"重建向量索引失败: {e}")
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到索引"""
        try:
            for doc in documents:
                if 'id' not in doc or 'content' not in doc:
                    logger.warning("文档缺少必要字段，跳过")
                    continue
                
                # 检查是否已存在
                existing_ids = [d['id'] for d in self.documents]
                if doc['id'] in existing_ids:
                    logger.warning(f"文档 {doc['id']} 已存在，跳过")
                    continue
                
                # 添加时间戳
                doc['indexed_at'] = datetime.now().isoformat()
                self.documents.append(doc)
            
            # 重建向量索引
            if SKLEARN_AVAILABLE:
                self._rebuild_vectors()
            
            # 保存索引
            self._save_index()
            
            logger.info(f"成功添加 {len(documents)} 个文档到文本索引")
            return True
            
        except Exception as e:
            logger.error(f"添加文档到索引失败: {e}")
            return False
    
    def _save_index(self):
        """保存索引到文件"""
        try:
            os.makedirs(os.path.dirname(self.metadata_path), exist_ok=True)
            
            self.metadata = {
                'documents': self.documents,
                'total_documents': len(self.documents),
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            
            logger.info("文本索引保存成功")
        except Exception as e:
            logger.error(f"保存文本索引失败: {e}")
    
    def search(self, query: str, limit: int = 10, threshold: float = 0.1) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            if not self.documents:
                logger.warning("文本索引为空")
                return []
            
            if SKLEARN_AVAILABLE and self.document_vectors is not None:
                return self._vector_search(query, limit, threshold)
            else:
                return self._keyword_search(query, limit)
                
        except Exception as e:
            logger.error(f"文本搜索失败: {e}")
            return []
    
    def _vector_search(self, query: str, limit: int, threshold: float) -> List[Dict[str, Any]]:
        """基于向量的搜索"""
        try:
            # 向量化查询
            query_vector = self.vectorizer.transform([query])
            
            # 计算相似度
            similarities = cosine_similarity(query_vector, self.document_vectors).flatten()
            
            # 获取最相似的文档
            top_indices = np.argsort(similarities)[::-1]
            
            results = []
            for idx in top_indices:
                if similarities[idx] < threshold:
                    break
                
                doc = self.documents[idx].copy()
                doc['similarity'] = float(similarities[idx])
                results.append(doc)
                
                if len(results) >= limit:
                    break
            
            return results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []
    
    def _keyword_search(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """基于关键词的搜索"""
        try:
            query_lower = query.lower()
            results = []
            
            for doc in self.documents:
                content = doc.get('content', '').lower()
                title = doc.get('title', '').lower()
                
                # 计算匹配度
                content_matches = content.count(query_lower)
                title_matches = title.count(query_lower) * 2  # 标题匹配权重更高
                
                if content_matches > 0 or title_matches > 0:
                    doc_copy = doc.copy()
                    doc_copy['match_score'] = content_matches + title_matches
                    results.append(doc_copy)
            
            # 按匹配度排序
            results.sort(key=lambda x: x['match_score'], reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"关键词搜索失败: {e}")
            return []
    
    def get_document_by_id(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取文档"""
        try:
            for doc in self.documents:
                if doc['id'] == doc_id:
                    return doc
            return None
        except Exception as e:
            logger.error(f"获取文档失败: {e}")
            return None
    
    def delete_document(self, doc_id: str) -> bool:
        """删除文档"""
        try:
            original_count = len(self.documents)
            self.documents = [doc for doc in self.documents if doc['id'] != doc_id]
            
            if len(self.documents) < original_count:
                # 重建向量索引
                if SKLEARN_AVAILABLE:
                    self._rebuild_vectors()
                
                # 保存索引
                self._save_index()
                
                logger.info(f"成功删除文档: {doc_id}")
                return True
            else:
                logger.warning(f"未找到要删除的文档: {doc_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_documents': len(self.documents),
            'vectorizer_available': SKLEARN_AVAILABLE,
            'index_path': self.index_path,
            'last_updated': self.metadata.get('last_updated', 'Unknown')
        }
