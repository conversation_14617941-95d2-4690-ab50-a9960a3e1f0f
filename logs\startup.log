2025-07-24 15:26:25.745 | INFO     | __main__:main:378 - 系统启动开始
2025-07-24 15:26:25.750 | INFO     | __main__:main:382 - 步骤 1/5: 系统健康检查
2025-07-24 15:26:25.846 | INFO     | __main__:main:386 - 步骤 2/5: 检查依赖
2025-07-24 15:26:27.198 | INFO     | __main__:main:392 - 步骤 3/5: 创建目录
2025-07-24 15:26:27.201 | INFO     | __main__:main:396 - 步骤 4/5: 显示启动信息
2025-07-24 15:26:27.204 | INFO     | __main__:main:400 - 步骤 5/5: 启动主服务器
2025-07-24 15:26:27.205 | INFO     | __main__:start_optimized_server:286 - 🚀 启动主服务器...
2025-07-24 15:26:27.205 | INFO     | __main__:start_optimized_server:292 - 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-24 15:26:27.205 | INFO     | __main__:start_optimized_server:293 - 📊 使用统一服务器架构
2025-07-24 15:26:27.206 | INFO     | __main__:start_optimized_server:294 - 🔗 API服务: http://0.0.0.0:5003
2025-07-24 15:26:27.206 | INFO     | __main__:start_optimized_server:295 - 📖 API文档: http://0.0.0.0:5003/docs
2025-07-24 15:26:27.206 | INFO     | __main__:start_optimized_server:298 - 📡 WebSocket支持: 已集成
2025-07-24 15:26:27.206 | INFO     | __main__:start_optimized_server:299 - 🔧 实时监控: 已启用
2025-07-24 15:26:27.207 | INFO     | __main__:start_optimized_server:300 - 🤖 DeepSeek AI: 已集成
2025-07-24 15:26:27.207 | INFO     | __main__:start_optimized_server:301 - 🔍 RAG检索: 已启用
2025-07-24 15:26:27.207 | INFO     | __main__:start_optimized_server:302 - 📊 数据处理: 已启用
2025-07-24 15:26:44.712 | WARNING  | data_processing.modern_chroma_manager:<module>:17 - ChromaDB不可用: No module named 'chromadb'
2025-07-24 15:26:44.712 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:26:45.033 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:26:45.700 | INFO     | data_processing.advanced_retrieval_optimizer:_init_jieba:142 - PaddlePaddle未安装，使用jieba默认模式
2025-07-24 15:26:46.313 | INFO     | data_processing.vector_processor:<module>:27 - 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-24 15:26:46.314 | WARNING  | data_processing.vector_processor:<module>:46 - Chroma未安装，将使用FAISS作为向量数据库
2025-07-24 15:26:46.353 | INFO     | __main__:start_optimized_server:308 - 📊 实时监控系统已启动
2025-07-24 15:26:46.353 | INFO     | __main__:start_optimized_server:313 - 🔗 启动Flask Web界面服务
2025-07-24 15:26:46.353 | INFO     | __main__:start_flask_server:66 - 正在启动Flask Web界面服务...
2025-07-24 15:26:46.368 | WARNING  | __main__:start_flask_server:95 - SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-24 15:26:48.710 | INFO     | __main__:main:378 - 系统启动开始
2025-07-24 15:26:48.712 | INFO     | __main__:main:382 - 步骤 1/5: 系统健康检查
2025-07-24 15:26:48.812 | INFO     | __main__:main:386 - 步骤 2/5: 检查依赖
2025-07-24 15:26:49.666 | INFO     | __main__:main:392 - 步骤 3/5: 创建目录
2025-07-24 15:26:49.667 | INFO     | __main__:main:396 - 步骤 4/5: 显示启动信息
2025-07-24 15:26:49.669 | INFO     | __main__:main:400 - 步骤 5/5: 启动主服务器
2025-07-24 15:26:49.669 | INFO     | __main__:start_optimized_server:286 - 🚀 启动主服务器...
2025-07-24 15:26:49.670 | INFO     | __main__:start_optimized_server:292 - 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-24 15:26:49.670 | INFO     | __main__:start_optimized_server:293 - 📊 使用统一服务器架构
2025-07-24 15:26:49.670 | INFO     | __main__:start_optimized_server:294 - 🔗 API服务: http://0.0.0.0:5003
2025-07-24 15:26:49.670 | INFO     | __main__:start_optimized_server:295 - 📖 API文档: http://0.0.0.0:5003/docs
2025-07-24 15:26:49.670 | INFO     | __main__:start_optimized_server:298 - 📡 WebSocket支持: 已集成
2025-07-24 15:26:49.670 | INFO     | __main__:start_optimized_server:299 - 🔧 实时监控: 已启用
2025-07-24 15:26:49.670 | INFO     | __main__:start_optimized_server:300 - 🤖 DeepSeek AI: 已集成
2025-07-24 15:26:49.671 | INFO     | __main__:start_optimized_server:301 - 🔍 RAG检索: 已启用
2025-07-24 15:26:49.671 | INFO     | __main__:start_optimized_server:302 - 📊 数据处理: 已启用
2025-07-24 15:26:59.807 | WARNING  | data_processing.modern_chroma_manager:<module>:17 - ChromaDB不可用: No module named 'chromadb'
2025-07-24 15:26:59.807 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:27:00.252 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:27:00.998 | INFO     | data_processing.advanced_retrieval_optimizer:_init_jieba:142 - PaddlePaddle未安装，使用jieba默认模式
2025-07-24 15:27:01.109 | INFO     | data_processing.vector_processor:<module>:27 - 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-24 15:27:01.110 | WARNING  | data_processing.vector_processor:<module>:46 - Chroma未安装，将使用FAISS作为向量数据库
2025-07-24 15:27:01.273 | INFO     | __main__:start_optimized_server:308 - 📊 实时监控系统已启动
2025-07-24 15:27:01.274 | INFO     | __main__:start_optimized_server:313 - 🔗 启动Flask Web界面服务
2025-07-24 15:27:01.274 | INFO     | __main__:start_flask_server:66 - 正在启动Flask Web界面服务...
2025-07-24 15:27:01.282 | WARNING  | __main__:start_flask_server:95 - SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-24 15:28:43.621 | INFO     | __main__:main:437 - 系统启动流程结束
2025-07-24 15:28:46.345 | INFO     | __main__:main:378 - 系统启动开始
2025-07-24 15:28:46.346 | INFO     | __main__:main:382 - 步骤 1/5: 系统健康检查
2025-07-24 15:28:46.442 | INFO     | __main__:main:386 - 步骤 2/5: 检查依赖
2025-07-24 15:28:47.213 | INFO     | __main__:main:392 - 步骤 3/5: 创建目录
2025-07-24 15:28:47.214 | INFO     | __main__:main:396 - 步骤 4/5: 显示启动信息
2025-07-24 15:28:47.216 | INFO     | __main__:main:400 - 步骤 5/5: 启动主服务器
2025-07-24 15:28:47.216 | INFO     | __main__:start_optimized_server:286 - 🚀 启动主服务器...
2025-07-24 15:28:47.216 | INFO     | __main__:start_optimized_server:292 - 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-24 15:28:47.216 | INFO     | __main__:start_optimized_server:293 - 📊 使用统一服务器架构
2025-07-24 15:28:47.217 | INFO     | __main__:start_optimized_server:294 - 🔗 API服务: http://0.0.0.0:5003
2025-07-24 15:28:47.217 | INFO     | __main__:start_optimized_server:295 - 📖 API文档: http://0.0.0.0:5003/docs
2025-07-24 15:28:47.217 | INFO     | __main__:start_optimized_server:298 - 📡 WebSocket支持: 已集成
2025-07-24 15:28:47.217 | INFO     | __main__:start_optimized_server:299 - 🔧 实时监控: 已启用
2025-07-24 15:28:47.217 | INFO     | __main__:start_optimized_server:300 - 🤖 DeepSeek AI: 已集成
2025-07-24 15:28:47.217 | INFO     | __main__:start_optimized_server:301 - 🔍 RAG检索: 已启用
2025-07-24 15:28:47.218 | INFO     | __main__:start_optimized_server:302 - 📊 数据处理: 已启用
2025-07-24 15:28:54.422 | WARNING  | data_processing.modern_chroma_manager:<module>:17 - ChromaDB不可用: No module named 'chromadb'
2025-07-24 15:28:54.422 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:28:54.676 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:28:55.158 | INFO     | data_processing.advanced_retrieval_optimizer:_init_jieba:142 - PaddlePaddle未安装，使用jieba默认模式
2025-07-24 15:28:55.214 | INFO     | data_processing.vector_processor:<module>:27 - 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-24 15:28:55.215 | WARNING  | data_processing.vector_processor:<module>:46 - Chroma未安装，将使用FAISS作为向量数据库
2025-07-24 15:28:55.238 | INFO     | __main__:start_optimized_server:308 - 📊 实时监控系统已启动
2025-07-24 15:28:55.238 | INFO     | __main__:start_optimized_server:313 - 🔗 启动Flask Web界面服务
2025-07-24 15:28:55.238 | INFO     | __main__:start_flask_server:66 - 正在启动Flask Web界面服务...
2025-07-24 15:28:55.244 | WARNING  | __main__:start_flask_server:95 - SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-24 15:31:05.478 | INFO     | __main__:main:437 - 系统启动流程结束
2025-07-24 15:31:08.274 | INFO     | __main__:main:378 - 系统启动开始
2025-07-24 15:31:08.274 | INFO     | __main__:main:382 - 步骤 1/5: 系统健康检查
2025-07-24 15:31:08.372 | INFO     | __main__:main:386 - 步骤 2/5: 检查依赖
2025-07-24 15:31:09.388 | INFO     | __main__:main:392 - 步骤 3/5: 创建目录
2025-07-24 15:31:09.389 | INFO     | __main__:main:396 - 步骤 4/5: 显示启动信息
2025-07-24 15:31:09.391 | INFO     | __main__:main:400 - 步骤 5/5: 启动主服务器
2025-07-24 15:31:09.391 | INFO     | __main__:start_optimized_server:286 - 🚀 启动主服务器...
2025-07-24 15:31:09.391 | INFO     | __main__:start_optimized_server:292 - 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-24 15:31:09.392 | INFO     | __main__:start_optimized_server:293 - 📊 使用统一服务器架构
2025-07-24 15:31:09.392 | INFO     | __main__:start_optimized_server:294 - 🔗 API服务: http://0.0.0.0:5003
2025-07-24 15:31:09.392 | INFO     | __main__:start_optimized_server:295 - 📖 API文档: http://0.0.0.0:5003/docs
2025-07-24 15:31:09.392 | INFO     | __main__:start_optimized_server:298 - 📡 WebSocket支持: 已集成
2025-07-24 15:31:09.392 | INFO     | __main__:start_optimized_server:299 - 🔧 实时监控: 已启用
2025-07-24 15:31:09.392 | INFO     | __main__:start_optimized_server:300 - 🤖 DeepSeek AI: 已集成
2025-07-24 15:31:09.393 | INFO     | __main__:start_optimized_server:301 - 🔍 RAG检索: 已启用
2025-07-24 15:31:09.393 | INFO     | __main__:start_optimized_server:302 - 📊 数据处理: 已启用
2025-07-24 15:31:19.500 | WARNING  | data_processing.modern_chroma_manager:<module>:17 - ChromaDB不可用: No module named 'chromadb'
2025-07-24 15:31:19.500 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:31:19.731 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:31:20.223 | INFO     | data_processing.advanced_retrieval_optimizer:_init_jieba:142 - PaddlePaddle未安装，使用jieba默认模式
2025-07-24 15:31:20.277 | INFO     | data_processing.vector_processor:<module>:27 - 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-24 15:31:20.278 | WARNING  | data_processing.vector_processor:<module>:46 - Chroma未安装，将使用FAISS作为向量数据库
2025-07-24 15:31:20.302 | INFO     | __main__:start_optimized_server:308 - 📊 实时监控系统已启动
2025-07-24 15:31:20.302 | INFO     | __main__:start_optimized_server:313 - 🔗 启动Flask Web界面服务
2025-07-24 15:31:20.302 | INFO     | __main__:start_flask_server:66 - 正在启动Flask Web界面服务...
2025-07-24 15:31:20.308 | WARNING  | __main__:start_flask_server:95 - SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-24 15:33:46.068 | INFO     | __main__:main:437 - 系统启动流程结束
2025-07-24 15:33:49.269 | INFO     | __main__:main:378 - 系统启动开始
2025-07-24 15:33:49.270 | INFO     | __main__:main:382 - 步骤 1/5: 系统健康检查
2025-07-24 15:33:49.358 | INFO     | __main__:main:386 - 步骤 2/5: 检查依赖
2025-07-24 15:33:50.333 | INFO     | __main__:main:392 - 步骤 3/5: 创建目录
2025-07-24 15:33:50.336 | INFO     | __main__:main:396 - 步骤 4/5: 显示启动信息
2025-07-24 15:33:50.340 | INFO     | __main__:main:400 - 步骤 5/5: 启动主服务器
2025-07-24 15:33:50.340 | INFO     | __main__:start_optimized_server:286 - 🚀 启动主服务器...
2025-07-24 15:33:50.340 | INFO     | __main__:start_optimized_server:292 - 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-24 15:33:50.341 | INFO     | __main__:start_optimized_server:293 - 📊 使用统一服务器架构
2025-07-24 15:33:50.341 | INFO     | __main__:start_optimized_server:294 - 🔗 API服务: http://0.0.0.0:5003
2025-07-24 15:33:50.341 | INFO     | __main__:start_optimized_server:295 - 📖 API文档: http://0.0.0.0:5003/docs
2025-07-24 15:33:50.341 | INFO     | __main__:start_optimized_server:298 - 📡 WebSocket支持: 已集成
2025-07-24 15:33:50.342 | INFO     | __main__:start_optimized_server:299 - 🔧 实时监控: 已启用
2025-07-24 15:33:50.342 | INFO     | __main__:start_optimized_server:300 - 🤖 DeepSeek AI: 已集成
2025-07-24 15:33:50.342 | INFO     | __main__:start_optimized_server:301 - 🔍 RAG检索: 已启用
2025-07-24 15:33:50.343 | INFO     | __main__:start_optimized_server:302 - 📊 数据处理: 已启用
2025-07-24 15:33:58.342 | WARNING  | data_processing.modern_chroma_manager:<module>:17 - ChromaDB不可用: No module named 'chromadb'
2025-07-24 15:33:58.342 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:33:58.666 | ERROR    | data_processing.modern_chroma_manager:__init__:34 - ChromaDB不可用，无法初始化
2025-07-24 15:33:59.241 | INFO     | data_processing.advanced_retrieval_optimizer:_init_jieba:142 - PaddlePaddle未安装，使用jieba默认模式
2025-07-24 15:33:59.307 | INFO     | data_processing.vector_processor:<module>:27 - 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-24 15:33:59.308 | WARNING  | data_processing.vector_processor:<module>:46 - Chroma未安装，将使用FAISS作为向量数据库
2025-07-24 15:33:59.345 | INFO     | __main__:start_optimized_server:308 - 📊 实时监控系统已启动
2025-07-24 15:33:59.345 | INFO     | __main__:start_optimized_server:313 - 🔗 启动Flask Web界面服务
2025-07-24 15:33:59.345 | INFO     | __main__:start_flask_server:66 - 正在启动Flask Web界面服务...
2025-07-24 15:33:59.350 | WARNING  | __main__:start_flask_server:95 - SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
