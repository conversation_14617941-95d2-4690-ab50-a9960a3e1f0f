# 故障分类器训练配置

# 模型配置
model:
  model_type: "transformer_classifier"
  base_model: "bert-base-chinese"
  num_labels: 15  # 故障类型数量
  hidden_dropout_prob: 0.1
  attention_probs_dropout_prob: 0.1
  
  # 分类标签
  labels:
    equipment_types:
      - "transformer"      # 变压器
      - "circuit_breaker"   # 断路器
      - "disconnector"      # 隔离开关
      - "current_transformer" # 电流互感器
      - "voltage_transformer" # 电压互感器
    
    fault_types:
      - "insulation_fault"  # 绝缘故障
      - "mechanical_fault"  # 机械故障
      - "electrical_fault"  # 电气故障
      - "thermal_fault"     # 热故障
      - "protection_fault"  # 保护故障
    
    severity_levels:
      - "critical"          # 严重
      - "major"            # 重要
      - "minor"            # 一般
      - "warning"          # 警告
      - "normal"           # 正常

# 训练配置
training:
  batch_size: 16
  learning_rate: 3e-5
  num_epochs: 20
  warmup_ratio: 0.1
  weight_decay: 0.01
  
  # 早停配置
  early_stopping:
    patience: 5
    min_delta: 0.001
    
  # 类别平衡
  class_weights: "balanced"  # 自动计算类别权重
  focal_loss:
    enable: true
    alpha: 0.25
    gamma: 2.0

# 数据配置
data:
  train_data_path: "training/datasets/processed/fault_train.jsonl"
  val_data_path: "training/datasets/processed/fault_val.jsonl"
  test_data_path: "training/datasets/processed/fault_test.jsonl"
  
  # 数据预处理
  preprocessing:
    max_length: 512
    truncation: true
    padding: "max_length"
    
  # 数据增强
  augmentation:
    enable: true
    techniques:
      - "back_translation"
      - "synonym_replacement"
      - "contextual_word_embedding"
    augmentation_ratio: 0.2

# 评估配置
evaluation:
  eval_steps: 200
  save_steps: 500
  logging_steps: 50
  metric_for_best_model: "eval_f1_macro"
  greater_is_better: true
  
  # 评估指标
  metrics:
    - "accuracy"
    - "precision_macro"
    - "recall_macro"
    - "f1_macro"
    - "f1_weighted"
    - "confusion_matrix"

# 输出配置
output:
  output_dir: "training/checkpoints/classifier"
  logging_dir: "training/logs/classifier"
  save_total_limit: 5
  load_best_model_at_end: true

# 硬件配置
hardware:
  use_cuda: true
  fp16: true
  gradient_checkpointing: true
  dataloader_num_workers: 4

# 故障诊断特定配置
fault_diagnosis:
  # 多标签分类
  multi_label: true
  threshold: 0.5
  
  # 层次分类
  hierarchical_classification:
    enable: true
    hierarchy_file: "training/configs/fault_hierarchy.json"
    
  # 领域知识集成
  domain_knowledge:
    enable: true
    knowledge_graph_path: "knowledge_base/fault_knowledge_graph.json"
    
  # 不确定性估计
  uncertainty_estimation:
    enable: true
    method: "monte_carlo_dropout"
    num_samples: 10
