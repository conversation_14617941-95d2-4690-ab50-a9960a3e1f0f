{"validation_timestamp": "2025-07-23T09:27:06.174971", "directory_structure": {"01_raw": true, "01_raw/equipment": false, "01_raw/fault_cases": true, "01_raw/sensor_data": true, "01_raw/maintenance_logs": false, "01_raw/inspection_reports": false, "01_raw/uploads": true, "02_processed": true, "02_processed/cleaned": true, "02_processed/annotated": true, "02_processed/structured": true, "02_processed/validated": true, "03_enhanced": true, "03_enhanced/generated": true, "03_enhanced/integrated": true, "03_enhanced/knowledge_base": true, "03_enhanced/training_sets": true, "04_production": true, "04_production/active": true, "04_production/cached": true, "04_production/indexed": true, "04_production/backups": true, "05_outputs": true, "05_outputs/reports": true, "05_outputs/exports": true, "05_outputs/visualizations": true, "05_outputs/logs": true, "metadata": true, "metadata/schemas": true, "metadata/catalogs": true, "metadata/lineage": true, "metadata/quality": true}, "data_integrity": {"total_files": 143, "total_size_mb": 29.04, "file_types": {".md": 38, ".json": 90, ".jpg": 3, ".txt": 8, ".csv": 3, ".xml": 1}, "corrupted_files": ["02_processed\\annotated\\img_8807456a-c0e6-4100-bd28-401cba955f82.json", "02_processed\\annotated\\img_a75dd454-8e20-48b0-b1c9-2b505a28371c.json", "02_processed\\annotated\\img_b0e1242b-a062-4e2a-91fb-74dea2914e93.json", "04_production\\cached\\detailed_test_results.json", "05_outputs\\logs\\detailed_test_results.json"], "empty_directories": ["02_processed\\cleaned", "04_production\\active\\equipment\\status_logs", "01_raw\\uploads\\temp", "01_raw\\uploads\\documents", "01_raw\\uploads\\images"], "large_files": []}, "migration_validation": {"status": "skipped", "reason": "no_backup"}, "code_compatibility": {"files_to_update": ["start_project.py", "langchain_modules\\tools\\equipment_tool.py", "ui\\app.py", "data_processing\\data_standardizer.py", "data_processing\\annotation_manager.py", "data_processing\\data_cleaner.py", "core\\equipment_manager.py", "core\\unified_data_config.py", "scripts\\setup.py", "scripts\\data_integration_baiyin.py", "scripts\\enhanced_data_processor.py", "scripts\\data_preparation_baiyin.py", "scripts\\build_vector_database.py", "scripts\\generate_baiyin_power_data.py", "scripts\\validate_data_migration.py", "scripts\\update_path_references.py", "data\\03_enhanced\\integrated\\baiyin_integrated_database.json", "data\\04_production\\cached\\text\\sample_ocr_result_001.json", "data\\04_production\\cached\\text\\sample_ocr_result_002.json", "data\\04_production\\cached\\text\\sample_ocr_result_003.json", "data\\04_production\\cached\\images\\sample_enhanced_001.json", "data\\04_production\\cached\\images\\sample_annotated_001.json", "data\\04_production\\cached\\images\\ocr_results_001.json", "data\\04_production\\cached\\images\\corrosion_detection_001.json", "data\\metadata\\quality\\migration_validation_report.json", "knowledge_base\\mappings\\enhanced_knowledge_mappings.json", "configs\\config.yaml"], "old_path_references": [{"file": "start_project.py", "old_path": "data/01_raw", "line_count": 1}, {"file": "langchain_modules\\tools\\equipment_tool.py", "old_path": "data/02_processed/structured", "line_count": 4}, {"file": "ui\\app.py", "old_path": "data/02_processed/structured", "line_count": 2}, {"file": "data_processing\\data_standardizer.py", "old_path": "data/01_raw", "line_count": 1}, {"file": "data_processing\\annotation_manager.py", "old_path": "data/04_production/cached", "line_count": 1}, {"file": "data_processing\\data_cleaner.py", "old_path": "data/02_processed/cleaned", "line_count": 1}, {"file": "core\\equipment_manager.py", "old_path": "data/04_production/active/equipment", "line_count": 1}, {"file": "core\\unified_data_config.py", "old_path": "data/01_raw", "line_count": 1}, {"file": "scripts\\setup.py", "old_path": "data/01_raw", "line_count": 1}, {"file": "scripts\\data_integration_baiyin.py", "old_path": "data/02_processed/structured", "line_count": 5}, {"file": "scripts\\enhanced_data_processor.py", "old_path": "data/04_production/cached", "line_count": 2}, {"file": "scripts\\data_preparation_baiyin.py", "old_path": "data/01_raw", "line_count": 1}, {"file": "scripts\\build_vector_database.py", "old_path": "data/02_processed/structured", "line_count": 2}, {"file": "scripts\\generate_baiyin_power_data.py", "old_path": "data/03_enhanced/generated", "line_count": 1}, {"file": "scripts\\validate_data_migration.py", "old_path": "data/01_raw", "line_count": 1}, {"file": "scripts\\update_path_references.py", "old_path": "data/01_raw", "line_count": 4}, {"file": "data\\03_enhanced\\integrated\\baiyin_integrated_database.json", "old_path": "data/02_processed/structured", "line_count": 5}, {"file": "data\\04_production\\cached\\text\\sample_ocr_result_001.json", "old_path": "data/01_raw", "line_count": 1}, {"file": "data\\04_production\\cached\\text\\sample_ocr_result_002.json", "old_path": "data/01_raw", "line_count": 1}, {"file": "data\\04_production\\cached\\text\\sample_ocr_result_003.json", "old_path": "data/01_raw", "line_count": 1}, {"file": "data\\04_production\\cached\\images\\sample_enhanced_001.json", "old_path": "data/01_raw", "line_count": 1}, {"file": "data\\04_production\\cached\\images\\sample_annotated_001.json", "old_path": "data/01_raw", "line_count": 1}, {"file": "data\\04_production\\cached\\images\\ocr_results_001.json", "old_path": "data/04_production/cached", "line_count": 1}, {"file": "data\\04_production\\cached\\images\\corrosion_detection_001.json", "old_path": "data/04_production/cached", "line_count": 1}, {"file": "data\\metadata\\quality\\migration_validation_report.json", "old_path": "data/01_raw", "line_count": 13}, {"file": "knowledge_base\\mappings\\enhanced_knowledge_mappings.json", "old_path": "data/02_processed/structured", "line_count": 6}, {"file": "configs\\config.yaml", "old_path": "data/01_raw", "line_count": 2}], "potential_issues": []}, "overall_score": {"structure_score": 90.6, "integrity_score": 96.5, "compatibility_score": 27.0, "total_score": 71.4}}