# 原始数据目录

此目录用于存储系统的原始输入数据。

## 目录结构

```
data/01_raw/
├── inspection_reports/     # 检查报告原始文件
├── equipment_images/       # 设备图像文件
├── fault_records/         # 故障记录文件
├── operation_logs/        # 运行日志文件
├── waveform_data/         # 波形数据文件
└── documents/             # 其他相关文档
```

## 支持的文件格式

### 文本文件
- `.txt` - 纯文本文件
- `.pdf` - PDF文档
- `.docx` - Word文档
- `.csv` - CSV数据文件
- `.xlsx` - Excel文件

### 图像文件
- `.jpg/.jpeg` - JPEG图像
- `.png` - PNG图像
- `.bmp` - BMP图像
- `.tiff` - TIFF图像

### 数据文件
- `.json` - JSON数据
- `.xml` - XML数据
- `.log` - 日志文件

## 使用说明

1. 将原始数据文件放入相应的子目录
2. 系统会自动扫描并处理这些文件
3. 处理后的数据会存储到 `data/04_production/cached/` 目录
4. 结构化数据会存储到 `data/02_processed/structured/` 目录

## 注意事项

- 确保文件名使用英文或数字，避免特殊字符
- 大文件建议压缩后上传
- 敏感数据请确保已脱敏处理
- 定期清理过期的原始数据文件
