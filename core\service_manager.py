#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一服务管理器
管理所有系统组件的生命周期，提供依赖注入和单例管理
消除全局变量，提升生产环境稳定性
"""

import threading
from typing import Dict, Any, Optional, Type, TypeVar, Callable
from loguru import logger
from contextlib import contextmanager

T = TypeVar('T')


class ServiceManager:
    """统一服务管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._services: Dict[str, Any] = {}
            self._factories: Dict[str, Callable] = {}
            self._singletons: Dict[str, Any] = {}
            self._lock = threading.RLock()
            self._initialized = True
    
    def register_factory(self, name: str, factory: Callable[[], T]) -> None:
        """注册服务工厂"""
        with self._lock:
            self._factories[name] = factory
            logger.debug(f"注册服务工厂: {name}")
    
    def register_singleton(self, name: str, instance: T) -> None:
        """注册单例服务"""
        with self._lock:
            self._singletons[name] = instance
            logger.debug(f"注册单例服务: {name}")
    
    def get_service(self, name: str, create_if_missing: bool = True) -> Optional[Any]:
        """获取服务实例"""
        with self._lock:
            # 首先检查单例
            if name in self._singletons:
                return self._singletons[name]
            
            # 检查已创建的服务
            if name in self._services:
                return self._services[name]
            
            # 使用工厂创建新实例
            if name in self._factories and create_if_missing:
                try:
                    instance = self._factories[name]()
                    self._services[name] = instance
                    logger.debug(f"创建服务实例: {name}")
                    return instance
                except Exception as e:
                    logger.error(f"创建服务失败 {name}: {e}")
                    return None
            
            return None
    
    def get_or_create_singleton(self, name: str, factory: Callable[[], T]) -> T:
        """获取或创建单例"""
        with self._lock:
            if name not in self._singletons:
                try:
                    instance = factory()
                    self._singletons[name] = instance
                    logger.debug(f"创建单例服务: {name}")
                except Exception as e:
                    logger.error(f"创建单例失败 {name}: {e}")
                    raise
            
            return self._singletons[name]
    
    def clear_service(self, name: str) -> None:
        """清理服务"""
        with self._lock:
            self._services.pop(name, None)
            self._singletons.pop(name, None)
            logger.debug(f"清理服务: {name}")
    
    def clear_all(self) -> None:
        """清理所有服务"""
        with self._lock:
            self._services.clear()
            self._singletons.clear()
            logger.info("清理所有服务")
    
    def list_services(self) -> Dict[str, str]:
        """列出所有服务"""
        with self._lock:
            services = {}
            for name in self._singletons:
                services[name] = "singleton"
            for name in self._services:
                if name not in services:
                    services[name] = "instance"
            for name in self._factories:
                if name not in services:
                    services[name] = "factory"
            return services
    
    @contextmanager
    def service_context(self, name: str, factory: Callable[[], T]):
        """服务上下文管理器"""
        instance = None
        try:
            instance = self.get_or_create_singleton(name, factory)
            yield instance
        except Exception as e:
            logger.error(f"服务上下文错误 {name}: {e}")
            raise
        finally:
            # 可以在这里添加清理逻辑
            pass


# 全局服务管理器实例
_service_manager = ServiceManager()


def get_service_manager() -> ServiceManager:
    """获取全局服务管理器"""
    return _service_manager


def register_service_factory(name: str, factory: Callable[[], T]) -> None:
    """注册服务工厂的便捷函数"""
    _service_manager.register_factory(name, factory)


def get_service(name: str, create_if_missing: bool = True) -> Optional[Any]:
    """获取服务的便捷函数"""
    return _service_manager.get_service(name, create_if_missing)


def get_or_create_singleton(name: str, factory: Callable[[], T]) -> T:
    """获取或创建单例的便捷函数"""
    return _service_manager.get_or_create_singleton(name, factory)


# 服务装饰器
def service(name: str):
    """服务装饰器"""
    def decorator(cls):
        def factory():
            return cls()
        register_service_factory(name, factory)
        return cls
    return decorator


def singleton_service(name: str):
    """单例服务装饰器"""
    def decorator(cls):
        def factory():
            return cls()
        
        # 立即创建单例
        instance = factory()
        _service_manager.register_singleton(name, instance)
        return cls
    return decorator
