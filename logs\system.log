2025-07-24 16:39:34 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-24 16:39:34 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-24 16:39:34 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-24 16:39:37 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-24 16:39:37 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 基础HTTP通信
2025-07-24 16:39:46 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ ChromaDB 不可用: No module named 'chromadb'
2025-07-24 16:39:46 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 内存存储
2025-07-24 16:39:46 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-24 16:39:46 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 基础图像功能
2025-07-24 16:39:46 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-24 16:39:46 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-24 16:39:46 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-24 16:39:46 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-24 16:39:46 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-24 16:39:46 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-24 16:39:46 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-24 16:39:46 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 16:39:46 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: equipment_manager
2025-07-24 16:39:46 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-24 16:39:46 | INFO | core.system_initializer:full_initialization:240 | ✅ 系统初始化完成
2025-07-24 16:40:09 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-24 16:40:09 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-24 16:40:09 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-24 16:40:17 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-24 16:40:17 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-24 16:40:17 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-24 16:40:17 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-24 16:40:17 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-24 16:40:18 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-24 16:40:20 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-24 16:40:20 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-24 16:40:20 | ERROR | data_processing.modern_chroma_manager:_init_client:56 | ❌ Chroma客户端初始化失败: expected str, bytes or os.PathLike object, not dict
2025-07-24 16:40:20 | WARNING | data_processing.modern_chroma_manager:_init_client:60 | ⚠️ 使用内存客户端作为回退
2025-07-24 16:40:20 | INFO | data_processing.modern_chroma_manager:_init_collection:79 | 集合 power_system_knowledge 不存在，创建新集合
2025-07-24 16:40:20 | INFO | data_processing.modern_chroma_manager:_init_collection:91 | ✅ 创建新集合: power_system_knowledge
2025-07-24 16:40:21 | INFO | data_processing.advanced_retrieval_optimizer:_init_jieba:142 | PaddlePaddle未安装，使用jieba默认模式
2025-07-24 16:40:21 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'retriever.knowledge_base'
2025-07-24 16:40:21 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-24 16:40:21 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 16:40:21 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: equipment_manager
2025-07-24 16:40:21 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-24 16:40:21 | INFO | core.system_initializer:full_initialization:240 | ✅ 系统初始化完成
2025-07-24 16:55:09 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-24 16:55:09 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-24 16:55:09 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-24 16:55:10 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-24 16:55:10 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 基础HTTP通信
2025-07-24 16:55:19 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ ChromaDB 不可用: No module named 'chromadb'
2025-07-24 16:55:19 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 内存存储
2025-07-24 16:55:19 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-24 16:55:19 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 基础图像功能
2025-07-24 16:55:19 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-24 16:55:19 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-24 16:55:19 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-24 16:55:19 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-24 16:55:19 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-24 16:55:19 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-24 16:55:19 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-24 16:55:19 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 16:55:19 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: equipment_manager
2025-07-24 16:55:19 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-24 16:55:19 | INFO | core.system_initializer:full_initialization:240 | ✅ 系统初始化完成
2025-07-24 16:58:28 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-24 16:58:28 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-24 16:58:29 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-24 16:58:35 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-24 16:58:35 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-24 16:58:35 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-24 16:58:35 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-24 16:58:35 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-24 16:58:36 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-24 16:58:38 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-24 16:58:38 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-24 16:58:39 | ERROR | data_processing.modern_chroma_manager:_init_client:56 | ❌ Chroma客户端初始化失败: expected str, bytes or os.PathLike object, not dict
2025-07-24 16:58:39 | WARNING | data_processing.modern_chroma_manager:_init_client:60 | ⚠️ 使用内存客户端作为回退
2025-07-24 16:58:39 | INFO | data_processing.modern_chroma_manager:_init_collection:79 | 集合 power_system_knowledge 不存在，创建新集合
2025-07-24 16:58:39 | INFO | data_processing.modern_chroma_manager:_init_collection:91 | ✅ 创建新集合: power_system_knowledge
2025-07-24 16:58:39 | INFO | data_processing.advanced_retrieval_optimizer:_init_jieba:142 | PaddlePaddle未安装，使用jieba默认模式
2025-07-24 16:58:39 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'retriever.knowledge_base'
2025-07-24 16:58:39 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-24 16:58:39 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 16:58:39 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: equipment_manager
2025-07-24 16:58:39 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-24 16:58:39 | INFO | core.system_initializer:full_initialization:240 | ✅ 系统初始化完成
2025-07-24 17:31:14 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-24 17:31:14 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-24 17:31:15 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-24 17:31:22 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-24 17:31:22 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-24 17:31:22 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-24 17:31:22 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-24 17:31:22 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-24 17:31:23 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-24 17:31:23 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-24 17:31:25 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'retriever.enhanced_knowledge_base'
2025-07-24 17:31:25 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-24 17:31:25 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 17:31:25 | WARNING | core.system_initializer:_preload_critical_services:187 | ⚠️ 服务预加载失败: equipment_manager
2025-07-24 17:31:25 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-24 17:31:25 | INFO | core.system_initializer:full_initialization:240 | ✅ 系统初始化完成
2025-07-24 17:59:11 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-24 17:59:11 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-24 17:59:11 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:22:13 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:22:13 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:22:13 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:22:17 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:22:17 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:22:17 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:22:17 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:22:17 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:22:18 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:22:18 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:22:22 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:22:22 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:22:22 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:22:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:22:23 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:22:23 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:22:23 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:22:23 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:22:23 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:22:23 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:22:23 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:22:23 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:22:23 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:22:23 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:22:23 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:22:23 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:22:23 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:22:23 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:22:23 | WARNING | __main__:start_optimized_server:262 | ⚠️ 实时监控启动失败: name 'get_processed_data_path' is not defined
2025-07-25 10:22:23 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:22:23 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:22:23 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:22:23 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:22:23 | ERROR | __main__:start_flask_server:104 | Flask服务器启动失败: name 'get_processed_data_path' is not defined
2025-07-25 10:22:23 | ERROR | __main__:start_optimized_server:279 | ❌ 主服务器启动失败: name 'get_processed_data_path' is not defined
2025-07-25 10:22:23 | ERROR | __main__:main:384 | ❌ 系统启动失败: name 'get_processed_data_path' is not defined
2025-07-25 10:22:23 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:24:58 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:24:58 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:24:58 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:25:04 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:25:04 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:25:04 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:25:04 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:25:04 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:25:05 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:25:06 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:25:10 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:25:10 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:25:10 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:25:10 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:25:10 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:25:10 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:25:10 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:25:10 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:25:10 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:25:11 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:25:11 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:25:11 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:25:11 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:25:11 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:25:11 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:25:11 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:25:11 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:25:11 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:25:11 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:25:11 | WARNING | __main__:start_optimized_server:262 | ⚠️ 实时监控启动失败: 'UnifiedConfigManager' object has no attribute 'project_root'
2025-07-25 10:25:11 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:25:11 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:25:11 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:25:11 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:25:11 | ERROR | __main__:start_flask_server:104 | Flask服务器启动失败: 'UnifiedConfigManager' object has no attribute 'project_root'
2025-07-25 10:25:11 | ERROR | __main__:start_optimized_server:279 | ❌ 主服务器启动失败: 'UnifiedConfigManager' object has no attribute 'project_root'
2025-07-25 10:25:11 | ERROR | __main__:main:384 | ❌ 系统启动失败: 'UnifiedConfigManager' object has no attribute 'project_root'
2025-07-25 10:25:11 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:26:06 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:26:06 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:26:06 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:26:10 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:26:10 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:26:10 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:26:10 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:26:10 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:26:11 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:26:12 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:26:15 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:26:15 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:26:15 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:26:15 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:26:15 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:26:15 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:26:15 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:26:15 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:26:15 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:26:16 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:26:16 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:26:16 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:26:16 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:26:16 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:26:16 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:26:16 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:26:16 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:26:16 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:26:16 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:26:17 | WARNING | __main__:start_optimized_server:262 | ⚠️ 实时监控启动失败: View function mapping is overwriting an existing endpoint function: health_check
2025-07-25 10:26:17 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:26:17 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:26:17 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:26:17 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:26:17 | ERROR | __main__:start_flask_server:104 | Flask服务器启动失败: View function mapping is overwriting an existing endpoint function: health_check
2025-07-25 10:26:17 | ERROR | __main__:start_optimized_server:279 | ❌ 主服务器启动失败: View function mapping is overwriting an existing endpoint function: health_check
2025-07-25 10:26:17 | ERROR | __main__:main:384 | ❌ 系统启动失败: View function mapping is overwriting an existing endpoint function: health_check
2025-07-25 10:26:17 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:28:03 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:28:03 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:28:03 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:28:10 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:28:10 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:28:10 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:28:10 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:28:10 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:28:11 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:28:11 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:28:15 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:28:15 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:28:15 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:28:15 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:28:15 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:28:15 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:28:15 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:28:15 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:28:15 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:28:15 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:28:15 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:28:15 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:28:15 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:28:16 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:28:16 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:28:16 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:28:16 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:28:16 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:28:16 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:28:16 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:28:16 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:28:16 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:28:17 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:28:17 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:28:18 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:28:21 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:28:21 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:28:21 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:28:21 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:28:21 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:28:22 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:28:22 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:28:25 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:28:25 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:28:25 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:28:25 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:28:25 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:28:25 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:28:25 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:28:25 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:28:25 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:28:26 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:28:26 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:28:26 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:28:26 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:28:26 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:28:26 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:28:26 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:28:26 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:28:26 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:28:26 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:28:26 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:28:26 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:28:26 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:30:20 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:30:43 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:30:43 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:30:44 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:30:52 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:30:52 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:30:52 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:30:52 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:30:52 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:30:52 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:30:53 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:30:57 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:30:57 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:30:57 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:30:57 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:30:57 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:30:57 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:30:57 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:30:57 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:30:57 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:30:57 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:30:57 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:30:57 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:30:57 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:30:57 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:30:57 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:30:57 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:30:57 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:30:57 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:30:57 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:30:58 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:30:58 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:30:58 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:30:59 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:30:59 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:30:59 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:31:03 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:31:03 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:31:03 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:31:03 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:31:03 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:31:04 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:31:04 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:31:07 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:31:07 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:31:07 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:31:07 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:31:07 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:31:07 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:31:07 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:31:07 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:31:07 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:31:08 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:31:08 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:31:08 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:31:08 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:31:08 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:31:08 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:31:08 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:31:08 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:31:08 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:31:08 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:31:08 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:31:08 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:31:08 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:34:00 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:34:02 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:34:12 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:34:12 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:34:13 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:34:18 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:34:18 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:34:18 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:34:18 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:34:18 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:34:18 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:34:19 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:34:22 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:34:22 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:34:22 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:34:22 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:34:22 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:34:22 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:34:22 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:34:22 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:34:22 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:34:23 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:34:23 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:34:23 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:34:23 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:34:23 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:34:23 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:34:23 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:34:23 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:34:23 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:34:23 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:34:23 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:34:23 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:34:23 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:34:25 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:34:25 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:34:25 | INFO | core.dependency_manager:check_all_dependencies:175 | 🔍 开始依赖检查...
2025-07-25 10:34:28 | WARNING | core.dependency_manager:check_dependency:166 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:34:28 | INFO | core.dependency_manager:check_dependency:169 |    回退方案: 无OCR功能
2025-07-25 10:34:28 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:34:28 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:34:28 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:34:29 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:34:29 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:34:33 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:34:33 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:34:33 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:34:33 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:34:33 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:34:33 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:34:33 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:34:33 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:34:33 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:34:33 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:34:33 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:34:33 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:34:33 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:34:33 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:34:33 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:34:33 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:34:33 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:34:33 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:34:33 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:34:33 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:39:01 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:39:05 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:39:05 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:39:05 | INFO | core.dependency_manager:check_all_dependencies:177 | 🔍 开始依赖检查...
2025-07-25 10:39:10 | WARNING | core.dependency_manager:check_dependency:168 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:39:10 | INFO | core.dependency_manager:check_dependency:171 |    回退方案: 无OCR功能
2025-07-25 10:39:10 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:39:10 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:39:10 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:39:11 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:39:12 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:39:15 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:39:15 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:39:15 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:39:15 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:39:15 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:39:15 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:39:15 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:39:15 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:39:15 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:39:16 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:39:16 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:39:16 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:39:16 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:39:16 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:39:16 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:39:16 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:39:16 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:39:16 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:39:16 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:39:17 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:39:17 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:39:17 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:39:43 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:39:47 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:39:47 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:39:47 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:39:51 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:39:51 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:39:51 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:39:51 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:39:51 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:39:51 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:39:52 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:39:56 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:39:56 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:39:56 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:39:56 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:39:56 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:39:56 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:39:56 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:39:56 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:39:56 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:39:56 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:39:56 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:39:56 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:39:56 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:39:56 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:39:56 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:39:56 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:39:56 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:39:56 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:39:56 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:39:57 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:39:57 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:39:57 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:40:45 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:40:49 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:40:49 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:40:49 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:40:52 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:40:52 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:40:52 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:40:52 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:40:52 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:40:52 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:40:52 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:40:53 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:40:54 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:40:57 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:40:57 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:40:57 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:40:57 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:40:57 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:40:57 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:40:57 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:40:57 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:40:57 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:40:58 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:40:58 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:40:58 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:40:58 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:40:58 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:40:58 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:40:58 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:40:58 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:40:58 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:40:58 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:40:58 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:40:58 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:40:58 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:41:49 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:41:54 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:41:54 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:41:54 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:42:00 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:42:00 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:42:00 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:42:00 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:42:00 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:42:00 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:42:00 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:42:01 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:42:02 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:42:05 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:42:05 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:42:05 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:42:05 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:42:05 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:42:05 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:42:05 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:42:05 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:42:05 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:42:05 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:42:05 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:42:05 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:42:05 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:42:05 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:42:05 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:42:05 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:42:05 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:42:05 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:42:05 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:42:06 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:42:06 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:42:06 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:42:11 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:42:16 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:42:16 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:42:16 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:42:20 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:42:20 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:42:20 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:42:20 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:42:20 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:42:20 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:42:20 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:42:21 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:42:21 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:42:25 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:42:25 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:42:25 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:42:25 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:42:25 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:42:25 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:42:25 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:42:25 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:42:25 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:42:26 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:42:26 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:42:26 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:42:26 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:42:26 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:42:26 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:42:26 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:42:26 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:42:26 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:42:26 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:42:26 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:42:26 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:42:26 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:42:35 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:42:39 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:42:39 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:42:39 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:42:43 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:42:43 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:42:43 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:42:43 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:42:43 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:42:43 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:42:43 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:42:43 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:42:44 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:42:47 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:42:47 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:42:47 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:42:47 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:42:47 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:42:47 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:42:47 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:42:47 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:42:47 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:42:48 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:42:48 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:42:48 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:42:48 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:42:48 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:42:48 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:42:48 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:42:48 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:42:48 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:42:48 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:42:48 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:42:48 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:42:48 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:42:57 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:43:00 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:43:00 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:43:00 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:43:05 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:43:05 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:43:05 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:43:05 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:43:05 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:43:05 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:43:05 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:43:06 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:43:06 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:43:09 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:43:09 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:43:09 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:43:09 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:43:09 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:43:09 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:43:09 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:43:09 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:43:09 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:43:10 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:43:10 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:43:10 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:43:10 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:43:10 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:43:10 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:43:10 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:43:10 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:43:10 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:43:10 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:43:11 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:43:11 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:43:11 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:43:28 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:43:32 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:43:32 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:43:32 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:43:36 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:43:36 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:43:36 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:43:36 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:43:36 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:43:36 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:43:36 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:43:37 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:43:38 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:43:42 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:43:42 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:43:42 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:43:42 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:43:42 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:43:42 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:43:42 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:43:42 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:43:42 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:43:42 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:43:42 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:43:42 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:43:42 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:43:42 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:43:42 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:43:42 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:43:42 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:43:42 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:43:42 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:43:43 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:43:43 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:43:43 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:43:50 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:43:53 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:43:53 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:43:53 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:43:58 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:43:58 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:43:58 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:43:58 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:43:58 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:43:58 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:43:58 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:43:58 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:43:59 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:44:02 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['ch_sim', 'en']
2025-07-25 10:44:02 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:44:02 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:44:02 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:44:02 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:44:02 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:44:02 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:44:02 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:44:02 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:44:03 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:44:03 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:44:03 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:44:03 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:44:03 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:44:03 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:44:03 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:44:03 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:44:03 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:44:03 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:44:03 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:44:03 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:44:03 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:44:13 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:44:18 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:44:18 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:44:18 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:44:22 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:44:22 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:44:22 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:44:22 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:44:22 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:44:22 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:44:22 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:44:22 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:44:23 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:44:26 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:44:26 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:44:26 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:44:26 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:44:26 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:44:26 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:44:26 | INFO | core.system_initializer:print_startup_banner:200 | 
======================================================================
2025-07-25 10:44:26 | INFO | core.system_initializer:print_startup_banner:201 | 🚀 故障分析智能助手系统
2025-07-25 10:44:26 | INFO | core.system_initializer:print_startup_banner:202 | ======================================================================
2025-07-25 10:44:26 | INFO | core.system_initializer:print_startup_banner:203 | 📊 版本: 1.0.0
2025-07-25 10:44:26 | INFO | core.system_initializer:print_startup_banner:204 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-25 10:44:26 | INFO | core.system_initializer:print_startup_banner:205 | 🔧 运行模式: 调试模式
2025-07-25 10:44:26 | INFO | core.system_initializer:print_startup_banner:206 | 📁 工作目录: G:\my-dl-dmx
2025-07-25 10:44:26 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:44:26 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:44:26 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:44:27 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:44:27 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:44:27 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:44:27 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:44:27 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:44:27 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:44:27 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:44:27 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:44:27 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:44:27 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:44:27 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:44:27 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:44:27 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:46:20 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:46:23 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:46:23 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:46:23 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:46:27 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:46:27 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:46:27 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:46:27 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:46:27 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:46:27 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:46:27 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:46:28 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:46:28 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:46:31 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:46:31 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:46:31 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:46:32 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:46:32 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:46:32 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:46:32 | INFO | core.system_initializer:print_startup_banner:200 | 
======================================================================
2025-07-25 10:46:32 | INFO | core.system_initializer:print_startup_banner:201 | 🚀 故障分析智能助手系统
2025-07-25 10:46:32 | INFO | core.system_initializer:print_startup_banner:202 | ======================================================================
2025-07-25 10:46:32 | INFO | core.system_initializer:print_startup_banner:203 | 📊 版本: 1.0.0
2025-07-25 10:46:32 | INFO | core.system_initializer:print_startup_banner:204 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-25 10:46:32 | INFO | core.system_initializer:print_startup_banner:205 | 🔧 运行模式: 调试模式
2025-07-25 10:46:32 | INFO | core.system_initializer:print_startup_banner:206 | 📁 工作目录: G:\my-dl-dmx
2025-07-25 10:46:32 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:46:32 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:46:32 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:46:32 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:46:32 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:46:32 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:46:32 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:46:32 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:46:32 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:46:32 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:46:32 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:46:32 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:46:32 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:46:33 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:46:33 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:46:33 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:46:52 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:46:56 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 10:46:56 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 10:46:56 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 10:47:00 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddle'
2025-07-25 10:47:00 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, Flask-SocketIO, FastAPI, Uvicorn
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers, ChromaDB, OpenCV
2025-07-25 10:47:00 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: PaddleOCR
2025-07-25 10:47:00 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 10:47:00 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 10:47:00 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 10:47:00 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 10:47:01 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 10:47:02 | INFO | data_processing.ocr_processor:<module>:27 | PaddleOCR未安装，将使用EasyOCR或Tesseract
2025-07-25 10:47:05 | INFO | data_processing.ocr_processor:_init_ocr_engines:88 | EasyOCR初始化成功，支持语言: ['en', 'ch_sim']
2025-07-25 10:47:05 | INFO | langchain_modules.prompts.prompt_manager:_load_prompt_templates:58 | 加载了 11 个提示词模板
2025-07-25 10:47:05 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:47:05 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:47:05 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 10:47:05 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 10:47:05 | INFO | core.system_initializer:print_startup_banner:200 | 
======================================================================
2025-07-25 10:47:05 | INFO | core.system_initializer:print_startup_banner:201 | 🚀 故障分析智能助手系统
2025-07-25 10:47:05 | INFO | core.system_initializer:print_startup_banner:202 | ======================================================================
2025-07-25 10:47:05 | INFO | core.system_initializer:print_startup_banner:203 | 📊 版本: 1.0.0
2025-07-25 10:47:05 | INFO | core.system_initializer:print_startup_banner:204 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-25 10:47:05 | INFO | core.system_initializer:print_startup_banner:205 | 🔧 运行模式: 调试模式
2025-07-25 10:47:05 | INFO | core.system_initializer:print_startup_banner:206 | 📁 工作目录: G:\my-dl-dmx
2025-07-25 10:47:05 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 10:47:05 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 10:47:05 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 10:47:05 | INFO | data_processing.modern_chroma_manager:_init_client:53 | ✅ 现代Chroma客户端初始化成功: embeddings/modern_chroma_store
2025-07-25 10:47:05 | INFO | data_processing.modern_chroma_manager:_init_collection:75 | ✅ 获取现有集合: power_system_knowledge
2025-07-25 10:47:05 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 10:47:05 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 10:47:05 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 10:47:05 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 10:47:05 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 10:47:05 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 10:47:05 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 10:47:05 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 10:47:06 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 10:47:06 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 10:47:06 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 10:48:10 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 10:48:13 | INFO | __main__:main:396 | 系统启动流程结束
2025-07-25 13:40:09 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 13:40:09 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 13:40:09 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 13:40:10 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-25 13:40:10 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-25 13:40:22 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ ChromaDB 不可用: No module named 'chromadb'
2025-07-25 13:40:22 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 内存存储
2025-07-25 13:40:22 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-25 13:40:22 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-25 13:40:22 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-25 13:40:22 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-25 13:40:22 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers
2025-07-25 13:40:22 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: ChromaDB, OpenCV, PaddleOCR
2025-07-25 13:40:22 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 13:40:22 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 13:40:22 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 13:40:22 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 13:40:23 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 13:40:23 | WARNING | data_processing.vector_processor:<module>:46 | Chroma未安装，将使用FAISS作为向量数据库
2025-07-25 13:40:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-25 13:40:23 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-25 13:40:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 13:40:23 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 13:40:23 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:200 | 
======================================================================
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:201 | 🚀 故障分析智能助手系统
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:202 | ======================================================================
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:203 | 📊 版本: 1.0.0
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:204 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:205 | 🔧 运行模式: 调试模式
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:206 | 📁 工作目录: G:\my-dl-dmx
2025-07-25 13:40:23 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-25 13:40:23 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:210 | 📡 WebSocket支持: ❌
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:211 | 🤖 AI分析: ✅
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:213 | 📊 数据处理: ✅
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:214 | 🖼️ 图像处理: ❌
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:215 | 📝 OCR识别: ❌
2025-07-25 13:40:23 | INFO | core.system_initializer:print_startup_banner:217 | ======================================================================
2025-07-25 13:40:23 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 13:40:23 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 13:40:23 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 13:40:24 | WARNING | data_processing.modern_chroma_manager:<module>:17 | ChromaDB不可用: No module named 'chromadb'
2025-07-25 13:40:24 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-25 13:40:24 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 13:40:24 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 13:40:24 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 13:40:24 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 13:40:24 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 13:40:24 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 13:40:24 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 13:40:24 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 13:40:24 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 13:40:24 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 13:40:24 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 13:40:24 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-25 13:40:26 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 13:40:26 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 13:40:26 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 13:40:26 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-25 13:40:26 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-25 13:50:30 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 13:50:30 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 13:50:30 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 13:50:32 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-25 13:50:32 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-25 13:50:44 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ ChromaDB 不可用: No module named 'chromadb'
2025-07-25 13:50:44 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 内存存储
2025-07-25 13:50:44 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-25 13:50:44 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-25 13:50:44 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-25 13:50:44 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-25 13:50:44 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers
2025-07-25 13:50:44 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: ChromaDB, OpenCV, PaddleOCR
2025-07-25 13:50:44 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 13:50:44 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 13:50:44 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 13:50:44 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 13:50:45 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 13:50:45 | WARNING | data_processing.vector_processor:<module>:46 | Chroma未安装，将使用FAISS作为向量数据库
2025-07-25 13:50:45 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-25 13:50:45 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-25 13:50:45 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 13:50:45 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 13:50:45 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:200 | 
======================================================================
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:201 | 🚀 故障分析智能助手系统
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:202 | ======================================================================
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:203 | 📊 版本: 1.0.0
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:204 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:205 | 🔧 运行模式: 调试模式
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:206 | 📁 工作目录: G:\my-dl-dmx
2025-07-25 13:50:45 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-25 13:50:45 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:210 | 📡 WebSocket支持: ❌
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:211 | 🤖 AI分析: ✅
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:213 | 📊 数据处理: ✅
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:214 | 🖼️ 图像处理: ❌
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:215 | 📝 OCR识别: ❌
2025-07-25 13:50:45 | INFO | core.system_initializer:print_startup_banner:217 | ======================================================================
2025-07-25 13:50:45 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 13:50:45 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 13:50:45 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 13:50:45 | WARNING | data_processing.modern_chroma_manager:<module>:17 | ChromaDB不可用: No module named 'chromadb'
2025-07-25 13:50:45 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-25 13:50:45 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 13:50:45 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 13:50:46 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 13:50:46 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 13:50:46 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 13:50:46 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 13:50:46 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 13:50:46 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 13:50:46 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 13:50:46 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 13:50:46 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 13:50:46 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-25 13:50:47 | INFO | core.system_initializer:setup_logging:127 | ✅ 日志系统配置完成
2025-07-25 13:50:47 | INFO | core.system_initializer:check_system_health:32 | 🔍 执行系统健康检查...
2025-07-25 13:50:48 | INFO | core.dependency_manager:check_all_dependencies:226 | 🔍 开始依赖检查...
2025-07-25 13:50:48 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ Flask-SocketIO 不可用: No module named 'flask_socketio'
2025-07-25 13:50:48 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础HTTP通信
2025-07-25 13:50:55 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ ChromaDB 不可用: No module named 'chromadb'
2025-07-25 13:50:55 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 内存存储
2025-07-25 13:50:55 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-25 13:50:55 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-25 13:50:55 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ PaddleOCR 不可用: No module named 'paddleocr'
2025-07-25 13:50:55 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 无OCR功能
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:272 | 
============================================================
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:273 | 📋 依赖检查报告
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:274 | ============================================================
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 关键依赖:
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Flask, Loguru, Requests
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 重要依赖:
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: Pandas, NumPy, Flask-CORS, FastAPI, Uvicorn
2025-07-25 13:50:55 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: Flask-SocketIO
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:281 | 
🔧 可选依赖:
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:283 |   ✅ 可用: LangChain, Sentence Transformers
2025-07-25 13:50:55 | WARNING | core.dependency_manager:_print_dependency_report:286 |   ⚠️ 缺失: ChromaDB, OpenCV, PaddleOCR
2025-07-25 13:50:55 | INFO | core.dependency_manager:_print_dependency_report:288 | 
============================================================
2025-07-25 13:50:55 | INFO | core.system_initializer:create_directories:81 | 📁 创建目录结构...
2025-07-25 13:50:55 | INFO | core.system_initializer:create_directories:96 | ✅ 目录结构创建完成
2025-07-25 13:50:55 | INFO | core.system_initializer:initialize_core_services:135 | 🔧 初始化核心服务...
2025-07-25 13:50:56 | INFO | data_processing.vector_processor:<module>:27 | 临时禁用SentenceTransformers，使用TF-IDF向量化器
2025-07-25 13:50:56 | WARNING | data_processing.vector_processor:<module>:46 | Chroma未安装，将使用FAISS作为向量数据库
2025-07-25 13:50:56 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-25 13:50:56 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: fault_analyzer
2025-07-25 13:50:56 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 13:50:56 | WARNING | core.system_initializer:_preload_critical_services:192 | ⚠️ 服务预加载失败: equipment_manager
2025-07-25 13:50:56 | INFO | core.system_initializer:initialize_core_services:144 | ✅ 核心服务初始化完成
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:200 | 
======================================================================
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:201 | 🚀 故障分析智能助手系统
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:202 | ======================================================================
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:203 | 📊 版本: 1.0.0
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:204 | 🌐 服务地址: http://0.0.0.0:5002
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:205 | 🔧 运行模式: 调试模式
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:206 | 📁 工作目录: G:\my-dl-dmx
2025-07-25 13:50:56 | WARNING | core.dependency_manager:_handle_dependency_failure:206 | ⚠️ OpenCV 不可用: No module named 'cv2'
2025-07-25 13:50:56 | INFO | core.dependency_manager:_handle_dependency_failure:217 |    回退方案: 基础图像功能
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:210 | 📡 WebSocket支持: ❌
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:211 | 🤖 AI分析: ✅
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:213 | 📊 数据处理: ✅
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:214 | 🖼️ 图像处理: ❌
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:215 | 📝 OCR识别: ❌
2025-07-25 13:50:56 | INFO | core.system_initializer:print_startup_banner:217 | ======================================================================
2025-07-25 13:50:56 | INFO | core.system_initializer:full_initialization:245 | ✅ 系统初始化完成
2025-07-25 13:50:56 | INFO | __main__:main:338 | 🚀 启动主服务器
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:238 | 🚀 启动主服务器...
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:244 | 🌐 主服务器启动在 http://0.0.0.0:5002
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:245 | 📊 使用统一服务器架构
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:246 | 🔗 API服务: http://0.0.0.0:5003
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:247 | 📖 API文档: http://0.0.0.0:5003/docs
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:250 | 📡 WebSocket支持: 已集成
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:251 | 🔧 实时监控: 已启用
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:252 | 🤖 DeepSeek AI: 已集成
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:253 | 🔍 RAG检索: 已启用
2025-07-25 13:50:56 | INFO | __main__:start_optimized_server:254 | 📊 数据处理: 已启用
2025-07-25 13:50:56 | WARNING | data_processing.modern_chroma_manager:<module>:17 | ChromaDB不可用: No module named 'chromadb'
2025-07-25 13:50:56 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-25 13:50:56 | INFO | core.production_optimizer:apply_all_optimizations:259 | 🚀 应用 development 环境优化...
2025-07-25 13:50:56 | INFO | core.production_optimizer:optimize_memory_usage:105 | 🧠 优化内存使用...
2025-07-25 13:50:56 | INFO | core.production_optimizer:optimize_memory_usage:122 | ✅ 内存优化完成
2025-07-25 13:50:56 | INFO | core.production_optimizer:optimize_threading:132 | 🧵 优化线程配置...
2025-07-25 13:50:56 | INFO | core.production_optimizer:optimize_threading:140 | ✅ 线程优化完成
2025-07-25 13:50:56 | INFO | core.production_optimizer:optimize_logging:150 | 📝 优化日志配置...
2025-07-25 13:50:56 | INFO | core.production_optimizer:optimize_logging:181 | ✅ 日志优化完成
2025-07-25 13:50:56 | INFO | core.production_optimizer:apply_all_optimizations:265 | ✅ 所有优化应用完成
2025-07-25 13:50:57 | INFO | __main__:start_optimized_server:260 | 📊 实时监控系统已启动
2025-07-25 13:50:57 | INFO | __main__:start_optimized_server:265 | 🔗 启动Flask Web界面服务
2025-07-25 13:50:57 | INFO | __main__:start_flask_server:66 | 正在启动Flask Web界面服务...
2025-07-25 13:50:57 | WARNING | __main__:start_flask_server:95 | SocketIO启动失败，使用标准Flask: run_simple() got an unexpected keyword argument 'allow_unsafe_werkzeug'
2025-07-25 13:51:13 | INFO | __main__:main:396 | 系统启动流程结束
