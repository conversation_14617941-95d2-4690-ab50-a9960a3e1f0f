# 图像知识库目录

此目录用于存储电力设备相关的图像资料，支持多模态知识检索。

## 目录结构

```
knowledge_base/images/
├── equipment/            # 设备图像
│   ├── transformers/         # 变压器图像
│   │   ├── normal/          # 正常状态
│   │   ├── defects/         # 缺陷图像
│   │   └── maintenance/     # 维护图像
│   ├── breakers/            # 断路器图像
│   ├── cables/              # 电缆图像
│   ├── insulators/          # 绝缘子图像
│   └── protection_devices/  # 保护设备图像
├── defects/              # 缺陷图像库
│   ├── corrosion/           # 腐蚀缺陷
│   ├── cracks/              # 裂纹缺陷
│   ├── deformation/         # 变形缺陷
│   ├── discharge/           # 放电缺陷
│   └── contamination/       # 污染缺陷
├── thermal/              # 热成像图像
│   ├── normal_thermal/      # 正常热成像
│   ├── abnormal_thermal/    # 异常热成像
│   └── analysis_results/    # 分析结果
├── waveforms/            # 波形图像
│   ├── voltage_waveforms/   # 电压波形
│   ├── current_waveforms/   # 电流波形
│   └── fault_waveforms/     # 故障波形
├── diagrams/             # 图表图像
│   ├── circuit_diagrams/    # 电路图
│   ├── layout_diagrams/     # 布置图
│   └── flow_charts/         # 流程图
└── reference/            # 参考图像
    ├── standards/           # 标准图像
    ├── comparisons/         # 对比图像
    └── examples/            # 示例图像
```

## 支持的图像格式

### 标准格式
- `.jpg/.jpeg` - JPEG格式（推荐用于照片）
- `.png` - PNG格式（推荐用于图表）
- `.bmp` - BMP格式
- `.tiff/.tif` - TIFF格式（高质量图像）
- `.webp` - WebP格式（现代浏览器）

### 特殊格式
- `.dcm` - DICOM医学图像格式
- `.raw` - 原始图像数据
- `.hdr` - 高动态范围图像

## 图像命名规范

### 命名格式
```
[设备类型]_[状态]_[视角]_[日期]_[序号].[扩展名]

示例：
- transformer_normal_front_20240101_001.jpg
- breaker_defect_side_20240115_003.png
- cable_thermal_top_20240120_002.jpg
```

### 状态代码
- `normal` - 正常状态
- `defect` - 缺陷状态
- `maintenance` - 维护状态
- `fault` - 故障状态
- `test` - 测试状态

### 视角代码
- `front` - 正面
- `back` - 背面
- `side` - 侧面
- `top` - 顶部
- `bottom` - 底部
- `detail` - 细节
- `overview` - 全景

## 图像元数据

每个图像文件应包含对应的元数据文件（.json格式）：

```json
{
  "filename": "transformer_normal_front_20240101_001.jpg",
  "equipment_info": {
    "type": "transformer",
    "model": "SZ11-50000/110",
    "manufacturer": "某某电气",
    "location": "110kV变电站"
  },
  "image_info": {
    "resolution": "1920x1080",
    "format": "JPEG",
    "size": "2.5MB",
    "capture_date": "2024-01-01T10:30:00Z",
    "camera": "Canon EOS R5"
  },
  "content_description": {
    "main_subject": "110kV主变压器",
    "condition": "正常运行状态",
    "visible_components": ["油箱", "套管", "冷却器", "保护装置"],
    "defects": [],
    "notes": "设备外观良好，无明显异常"
  },
  "analysis_results": {
    "defect_detection": {
      "has_defects": false,
      "confidence": 0.95,
      "detected_objects": ["transformer", "bushings", "cooling_system"]
    },
    "thermal_analysis": {
      "max_temperature": 45.2,
      "min_temperature": 22.1,
      "avg_temperature": 35.6,
      "hot_spots": []
    }
  },
  "keywords": ["变压器", "正常状态", "110kV", "主设备"],
  "category": "equipment/transformers/normal"
}
```

## 图像质量标准

### 技术要求
- **分辨率**：最低1280x720，推荐1920x1080或更高
- **格式**：JPEG质量85%以上，PNG无损压缩
- **色彩**：sRGB色彩空间，24位真彩色
- **文件大小**：单个文件不超过10MB

### 拍摄要求
- **光照**：充足均匀的光照，避免强烈阴影
- **焦点**：主体清晰，关键部位对焦准确
- **构图**：主体居中，包含必要的背景信息
- **角度**：选择最能展现设备特征的角度

## 图像处理流程

### 预处理步骤
1. **尺寸调整**：统一图像尺寸规格
2. **质量优化**：调整亮度、对比度、饱和度
3. **噪声去除**：去除图像噪声和伪影
4. **格式转换**：转换为标准格式

### 特征提取
1. **目标检测**：识别设备和组件
2. **缺陷检测**：检测可见缺陷
3. **特征向量**：生成图像特征向量
4. **相似度计算**：计算图像相似度

## 使用指南

### 图像上传
1. 按照命名规范命名文件
2. 确保图像质量符合标准
3. 创建对应的元数据文件
4. 放入相应的分类目录

### 图像搜索
- **相似图像搜索**：基于视觉特征的相似度搜索
- **内容搜索**：基于图像内容的语义搜索
- **标签搜索**：基于关键词和标签搜索
- **组合搜索**：多种搜索方式组合

### 图像分析
- **缺陷检测**：自动检测设备缺陷
- **状态评估**：评估设备运行状态
- **对比分析**：与历史图像对比分析
- **趋势分析**：分析设备状态变化趋势

## 维护管理

### 定期维护
- 检查图像文件完整性
- 更新图像元数据信息
- 清理重复和低质量图像
- 备份重要图像资料

### 质量控制
- 定期审核图像质量
- 验证元数据准确性
- 更新分类和标签
- 优化存储结构
