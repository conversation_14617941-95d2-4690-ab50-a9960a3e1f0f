
import time
import threading
from functools import lru_cache, wraps
from typing import Dict, Any, Optional, Callable
from loguru import logger

class OptimizedCache:
    """优化的缓存系统 - 生产环境增强版"""

    def __init__(self, max_size: int = 1000, ttl: int = 3600, name: str = "cache"):
        self.max_size = max_size
        self.ttl = ttl
        self.name = name
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'cleanups': 0
        }

        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        logger.debug(f"缓存 {self.name} 初始化完成 (max_size={max_size}, ttl={ttl}s)")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                if time.time() - entry['timestamp'] < self.ttl:
                    self.access_times[key] = time.time()
                    self._stats['hits'] += 1
                    return entry['value']
                else:
                    # 过期删除
                    del self.cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
                    self._stats['misses'] += 1
            else:
                self._stats['misses'] += 1
            return None
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        with self.lock:
            # 检查容量限制
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = {
                'value': value,
                'timestamp': time.time()
            }
            self.access_times[key] = time.time()
    
    def _evict_lru(self) -> None:
        """淘汰最近最少使用的条目"""
        if not self.access_times:
            return

        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[lru_key]
        del self.access_times[lru_key]
        self._stats['evictions'] += 1
        logger.debug(f"缓存 {self.name} 淘汰条目: {lru_key}")
    
    def _cleanup_loop(self) -> None:
        """清理过期条目的循环"""
        while True:
            time.sleep(300)  # 每5分钟清理一次
            self._cleanup_expired()
    
    def _cleanup_expired(self) -> None:
        """清理过期条目"""
        current_time = time.time()
        with self.lock:
            expired_keys = [
                key for key, entry in self.cache.items()
                if current_time - entry['timestamp'] >= self.ttl
            ]
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]

            if expired_keys:
                self._stats['cleanups'] += 1
                logger.debug(f"缓存 {self.name} 清理过期条目: {len(expired_keys)} 个")

    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            logger.info(f"缓存 {self.name} 已清空")

    def size(self) -> int:
        """获取缓存大小"""
        with self.lock:
            return len(self.cache)

    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0.0

            return {
                'name': self.name,
                'size': len(self.cache),
                'max_size': self.max_size,
                'ttl': self.ttl,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'hit_rate': hit_rate,
                'evictions': self._stats['evictions'],
                'cleanups': self._stats['cleanups']
            }

    def cached(self, ttl: Optional[int] = None, key_func: Optional[Callable] = None):
        """缓存装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = self._generate_cache_key(func.__name__, args, kwargs)

                # 尝试从缓存获取
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"缓存命中: {cache_key}")
                    return cached_result

                # 执行函数并缓存结果
                logger.debug(f"缓存未命中，执行函数: {cache_key}")
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl)
                return result

            return wrapper
        return decorator

    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成优化的缓存键"""
        import hashlib
        import json

        # 创建键的组成部分
        key_parts = [func_name]

        # 处理位置参数
        if args:
            # 对于复杂对象，尝试序列化
            serialized_args = []
            for arg in args:
                try:
                    if hasattr(arg, '__dict__'):
                        # 对象类型，使用其字典表示
                        serialized_args.append(str(sorted(arg.__dict__.items())))
                    elif isinstance(arg, (dict, list)):
                        # 字典或列表，使用JSON序列化
                        serialized_args.append(json.dumps(arg, sort_keys=True, default=str))
                    else:
                        # 基本类型，直接转字符串
                        serialized_args.append(str(arg))
                except:
                    # 序列化失败，使用对象ID
                    serialized_args.append(f"obj_{id(arg)}")

            key_parts.append("args:" + "|".join(serialized_args))

        # 处理关键字参数
        if kwargs:
            try:
                sorted_kwargs = json.dumps(kwargs, sort_keys=True, default=str)
                key_parts.append("kwargs:" + sorted_kwargs)
            except:
                # JSON序列化失败，使用字符串表示
                sorted_items = sorted(kwargs.items())
                key_parts.append("kwargs:" + str(sorted_items))

        # 生成最终键
        full_key = "|".join(key_parts)

        # 如果键太长，使用哈希
        if len(full_key) > 200:
            hash_key = hashlib.md5(full_key.encode('utf-8')).hexdigest()
            return f"{self.name}:{func_name}:{hash_key}"
        else:
            return f"{self.name}:{full_key}"


# 全局缓存实例 - 生产环境优化
embedding_cache = OptimizedCache(max_size=1000, ttl=3600, name="embedding")
search_cache = OptimizedCache(max_size=500, ttl=1800, name="search")
model_cache = OptimizedCache(max_size=10, ttl=7200, name="model")
analysis_cache = OptimizedCache(max_size=200, ttl=1800, name="analysis")


def get_cache_stats() -> Dict[str, Dict[str, Any]]:
    """获取所有缓存的统计信息"""
    return {
        'embedding': embedding_cache.stats(),
        'search': search_cache.stats(),
        'model': model_cache.stats(),
        'analysis': analysis_cache.stats()
    }


def clear_all_caches() -> None:
    """清空所有缓存"""
    embedding_cache.clear()
    search_cache.clear()
    model_cache.clear()
    analysis_cache.clear()
    logger.info("所有缓存已清空")
