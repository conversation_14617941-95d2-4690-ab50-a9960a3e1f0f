#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用工具模块
提供项目中常用的工具函数，消除重复代码
"""

import hashlib
import re
from typing import List, Dict, Any, Set, Optional, Union
from loguru import logger


class DeduplicationUtils:
    """去重工具类"""
    
    @staticmethod
    def deduplicate_by_content(items: List[Dict[str, Any]], 
                              content_key: str = 'content',
                              hash_length: int = 200) -> List[Dict[str, Any]]:
        """
        基于内容去重
        
        Args:
            items: 要去重的项目列表
            content_key: 内容字段名
            hash_length: 用于哈希的内容长度
            
        Returns:
            去重后的项目列表
        """
        seen_hashes = set()
        unique_items = []
        
        for item in items:
            content = item.get(content_key, '')
            if isinstance(content, str):
                # 使用内容的前N个字符生成哈希
                content_hash = hash(content[:hash_length].strip())
                
                if content_hash not in seen_hashes:
                    seen_hashes.add(content_hash)
                    unique_items.append(item)
        
        return unique_items
    
    @staticmethod
    def deduplicate_by_hash(items: List[Dict[str, Any]], 
                           content_key: str = 'content') -> List[Dict[str, Any]]:
        """
        基于MD5哈希去重
        
        Args:
            items: 要去重的项目列表
            content_key: 内容字段名
            
        Returns:
            去重后的项目列表
        """
        seen_hashes = set()
        unique_items = []
        
        for item in items:
            content = item.get(content_key, '')
            if isinstance(content, str):
                content_hash = hashlib.md5(content.encode()).hexdigest()
                
                if content_hash not in seen_hashes:
                    seen_hashes.add(content_hash)
                    unique_items.append(item)
        
        return unique_items
    
    @staticmethod
    def deduplicate_by_source(items: List[Dict[str, Any]], 
                             source_key: str = 'source') -> List[Dict[str, Any]]:
        """
        基于来源去重
        
        Args:
            items: 要去重的项目列表
            source_key: 来源字段名
            
        Returns:
            去重后的项目列表
        """
        seen_sources = set()
        unique_items = []
        
        for item in items:
            source = item.get(source_key, '')
            if source not in seen_sources:
                seen_sources.add(source)
                unique_items.append(item)
        
        return unique_items


class TextUtils:
    """文本处理工具类"""
    
    @staticmethod
    def calculate_relevance(query: str, content: str, 
                          keywords: Optional[List[str]] = None) -> float:
        """
        计算查询与内容的相关性
        
        Args:
            query: 查询字符串
            content: 内容字符串
            keywords: 专业关键词列表
            
        Returns:
            相关性分数 (0.0-1.0)
        """
        if not query or not content:
            return 0.0
        
        query_lower = query.lower()
        content_lower = content.lower()
        
        # 默认电力专业关键词
        if keywords is None:
            keywords = [
                '变压器', '断路器', '故障', '保护', '差动', '电缆', 
                '隔离开关', '白银', '电站', '渗油', '拒动', '短路', 
                '接地', '过载', '跳闸', '动作', '误动', '套管'
            ]
        
        score = 0.0
        
        # 1. 直接字符串包含检查
        if query_lower in content_lower:
            score += 0.4
        
        # 2. 关键词匹配得分
        keyword_score = 0.0
        for keyword in keywords:
            if keyword in query_lower and keyword in content_lower:
                keyword_score += 0.05
        
        score += min(keyword_score, 0.3)  # 最多30%
        
        # 3. 词汇重叠度
        query_words = set(query_lower.split())
        content_words = set(content_lower.split())
        overlap = len(query_words & content_words)
        overlap_score = overlap / max(len(query_words), 1) * 0.3
        score += overlap_score
        
        return min(score, 1.0)
    
    @staticmethod
    def normalize_text(text: str) -> str:
        """
        标准化文本
        
        Args:
            text: 原始文本
            
        Returns:
            标准化后的文本
        """
        if not text:
            return text
        
        # 去除多余空白
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 标准化标点符号
        text = text.replace('，', ',').replace('。', '.').replace('；', ';')
        
        # 标准化引号
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        
        return text
    
    @staticmethod
    def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
        """
        提取关键词
        
        Args:
            text: 文本内容
            max_keywords: 最大关键词数量
            
        Returns:
            关键词列表
        """
        if not text:
            return []
        
        # 电力专业术语词典
        power_terms = [
            '变压器', '断路器', '隔离开关', '电缆', '母线', '套管',
            '保护装置', '继电保护', '差动保护', '距离保护',
            '故障', '短路', '接地', '过载', '跳闸', '拒动', '误动',
            '电压', '电流', '功率', '频率', '阻抗', '电阻',
            '检修', '维护', '试验', '巡检', '监测', '诊断'
        ]
        
        # 提取文本中的专业术语
        keywords = []
        text_lower = text.lower()
        
        for term in power_terms:
            if term in text and term not in keywords:
                keywords.append(term)
                if len(keywords) >= max_keywords:
                    break
        
        return keywords


class QualityUtils:
    """质量评估工具类"""
    
    @staticmethod
    def calculate_content_quality(content: str, 
                                 structured_info: Optional[Dict] = None) -> float:
        """
        计算内容质量分数
        
        Args:
            content: 内容文本
            structured_info: 结构化信息
            
        Returns:
            质量分数 (0.0-100.0)
        """
        if not content:
            return 0.0
        
        score = 0.0
        
        # 长度评分 (0-20分)
        length = len(content)
        if length > 1000:
            score += 20
        elif length > 500:
            score += 15
        elif length > 200:
            score += 10
        elif length > 100:
            score += 5
        
        # 专业术语密度评分 (0-30分)
        technical_terms = [
            '变压器', '断路器', '故障', '保护', '电缆', '母线',
            '电压', '电流', '功率', '检修', '维护', '试验'
        ]
        term_count = sum(1 for term in technical_terms if term in content)
        score += min(term_count * 3, 30)
        
        # 结构完整性评分 (0-30分)
        structure_indicators = ['故障现象', '故障分析', '处理措施', '经验总结', '预防措施']
        structure_count = sum(1 for indicator in structure_indicators if indicator in content)
        score += structure_count * 6
        
        # 数据完整性评分 (0-20分)
        if structured_info:
            data_fields = ['设备类型', '故障类型', '处理时间', '责任单位']
            data_count = sum(1 for field in data_fields if structured_info.get(field))
            score += data_count * 5
        
        return min(score, 100.0)
    
    @staticmethod
    def passes_quality_threshold(content: str, 
                                score: float,
                                min_length: int = 50,
                                min_score: float = 30.0,
                                min_terms: int = 2) -> bool:
        """
        检查内容是否通过质量阈值
        
        Args:
            content: 内容文本
            score: 质量分数
            min_length: 最小长度
            min_score: 最小分数
            min_terms: 最少专业术语数量
            
        Returns:
            是否通过质量检查
        """
        # 长度检查
        if len(content) < min_length:
            return False
        
        # 分数检查
        if score < min_score:
            return False
        
        # 专业术语检查
        technical_terms = ['变压器', '断路器', '母线', '电缆', '故障', '保护', '电压', '电流']
        term_count = sum(1 for term in technical_terms if term in content)
        if term_count < min_terms:
            return False
        
        return True


class CacheUtils:
    """缓存工具类"""
    
    @staticmethod
    def generate_cache_key(*args, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            缓存键字符串
        """
        # 将所有参数转换为字符串并连接
        key_parts = []
        
        for arg in args:
            key_parts.append(str(arg))
        
        for key, value in sorted(kwargs.items()):
            key_parts.append(f"{key}={value}")
        
        key_string = "_".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()


# 便捷函数
def deduplicate_results(results: List[Dict[str, Any]], 
                       method: str = 'content',
                       **kwargs) -> List[Dict[str, Any]]:
    """
    去重结果的便捷函数
    
    Args:
        results: 结果列表
        method: 去重方法 ('content', 'hash', 'source')
        **kwargs: 其他参数
        
    Returns:
        去重后的结果列表
    """
    if method == 'content':
        return DeduplicationUtils.deduplicate_by_content(results, **kwargs)
    elif method == 'hash':
        return DeduplicationUtils.deduplicate_by_hash(results, **kwargs)
    elif method == 'source':
        return DeduplicationUtils.deduplicate_by_source(results, **kwargs)
    else:
        logger.warning(f"未知的去重方法: {method}，使用默认方法")
        return DeduplicationUtils.deduplicate_by_content(results, **kwargs)


def calculate_relevance(query: str, content: str, **kwargs) -> float:
    """计算相关性的便捷函数"""
    return TextUtils.calculate_relevance(query, content, **kwargs)


def normalize_text(text: str) -> str:
    """文本标准化的便捷函数"""
    return TextUtils.normalize_text(text)


def calculate_quality_score(content: str, **kwargs) -> float:
    """质量评分的便捷函数"""
    return QualityUtils.calculate_content_quality(content, **kwargs)
