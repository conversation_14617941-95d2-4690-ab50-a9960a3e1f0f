"""
文档问答推理链

基于知识库的文档问答系统
"""

from typing import Dict, Any, List, Optional
from langchain.chains.base import Chain
from langchain.llms.base import BaseLLM
from pydantic import Field
from loguru import logger

from retriever.unified_professional_retriever import get_unified_retriever as KnowledgeBase
from ..prompts.prompt_manager import PromptManager


class DocumentQAChain(Chain):
    """文档问答推理链"""
    
    llm: BaseLLM = Field()
    knowledge_base: Any = Field()
    prompt_manager: PromptManager = Field()
    config: Dict[str, Any] = Field(default_factory=dict)
    
    input_key: str = "question"
    output_key: str = "answer"
    
    def __init__(self, llm: BaseLLM, knowledge_base: KnowledgeBase, 
                 prompt_manager: PromptManager, config: Dict[str, Any], **kwargs):
        super().__init__(
            llm=llm, 
            knowledge_base=knowledge_base, 
            prompt_manager=prompt_manager, 
            config=config, 
            **kwargs
        )
    
    @property
    def input_keys(self) -> List[str]:
        """输入键"""
        return [self.input_key]
    
    @property
    def output_keys(self) -> List[str]:
        """输出键"""
        return [self.output_key]
    
    def _call(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行文档问答"""
        try:
            question = inputs[self.input_key]
            
            # 搜索相关文档
            search_results = self.knowledge_base.search(
                query=question,
                search_type="multimodal",
                top_k=self.config.get("top_k", 5)
            )
            
            # 提取文档内容
            document_content = self._extract_document_content(search_results)
            
            if not document_content:
                return {self.output_key: "抱歉，我在知识库中没有找到相关信息来回答您的问题。"}
            
            # 生成回答
            answer = self._generate_answer(question, document_content)
            
            return {self.output_key: answer}
            
        except Exception as e:
            logger.error(f"文档问答执行失败: {str(e)}")
            return {self.output_key: f"问答处理失败: {str(e)}"}
    
    def _extract_document_content(self, search_results: Dict[str, Any]) -> str:
        """提取文档内容"""
        try:
            content_parts = []
            
            results = search_results.get("results", {})
            
            # 处理文本结果
            text_results = results.get("text", [])
            for i, result in enumerate(text_results[:3]):  # 最多使用3个文本结果
                content = result.get("content", "")
                source = result.get("metadata", {}).get("source", f"文档{i+1}")
                score = result.get("score", 0)
                
                if content:
                    content_parts.append(f"[来源: {source}, 相关度: {score:.2f}]\n{content}")
            
            # 处理图像结果（OCR文本）
            image_results = results.get("images", [])
            for i, result in enumerate(image_results[:2]):  # 最多使用2个图像结果
                ocr_text = result.get("ocr_text", "")
                filename = result.get("filename", f"图像{i+1}")
                score = result.get("score", 0)
                
                if ocr_text:
                    content_parts.append(f"[图像来源: {filename}, 相关度: {score:.2f}]\n{ocr_text}")
            
            return "\n\n".join(content_parts)
            
        except Exception as e:
            logger.error(f"提取文档内容失败: {str(e)}")
            return ""
    
    def _generate_answer(self, question: str, document_content: str) -> str:
        """生成回答"""
        try:
            # 使用文档问答模板
            prompt = self.prompt_manager.format_prompt(
                "document_qa",
                document_content=document_content,
                question=question
            )
            
            if not prompt:
                # 如果模板不存在，使用简单格式
                prompt = f"""基于以下文档内容回答问题：

文档内容：
{document_content}

问题：{question}

请根据文档内容准确回答，如果文档中没有相关信息，请明确说明。

回答："""
            
            # 调用LLM生成回答
            response = self.llm.invoke(prompt)
            
            return response.content if hasattr(response, 'content') else str(response)
            
        except Exception as e:
            logger.error(f"生成回答失败: {str(e)}")
            return f"生成回答时出现错误: {str(e)}"
    
    def ask_question(self, question: str, search_type: str = "multimodal") -> Dict[str, Any]:
        """
        问答接口
        
        Args:
            question: 用户问题
            search_type: 搜索类型
            
        Returns:
            问答结果
        """
        try:
            # 执行问答
            result = self._call({self.input_key: question})
            
            # 获取搜索结果用于引用
            search_results = self.knowledge_base.search(
                query=question,
                search_type=search_type,
                top_k=self.config.get("top_k", 5)
            )
            
            # 提取引用信息
            references = self._extract_references(search_results)
            
            return {
                "success": True,
                "question": question,
                "answer": result[self.output_key],
                "references": references,
                "search_type": search_type
            }
            
        except Exception as e:
            logger.error(f"问答接口执行失败: {str(e)}")
            return {
                "success": False,
                "question": question,
                "error": str(e)
            }
    
    def _extract_references(self, search_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取引用信息"""
        try:
            references = []
            
            results = search_results.get("results", {})
            
            # 文本引用
            text_results = results.get("text", [])
            for result in text_results[:3]:
                ref = {
                    "type": "text",
                    "source": result.get("metadata", {}).get("source", "unknown"),
                    "score": result.get("score", 0),
                    "content_preview": result.get("content", "")[:100] + "..."
                }
                references.append(ref)
            
            # 图像引用
            image_results = results.get("images", [])
            for result in image_results[:2]:
                ref = {
                    "type": "image",
                    "source": result.get("filename", "unknown"),
                    "score": result.get("score", 0),
                    "ocr_preview": result.get("ocr_text", "")[:100] + "..." if result.get("ocr_text") else ""
                }
                references.append(ref)
            
            return references
            
        except Exception as e:
            logger.error(f"提取引用信息失败: {str(e)}")
            return []
    
    def batch_qa(self, questions: List[str]) -> List[Dict[str, Any]]:
        """
        批量问答
        
        Args:
            questions: 问题列表
            
        Returns:
            问答结果列表
        """
        results = []
        
        for question in questions:
            try:
                result = self.ask_question(question)
                results.append(result)
                
            except Exception as e:
                logger.error(f"批量问答中的问题处理失败: {question}, 错误: {str(e)}")
                results.append({
                    "success": False,
                    "question": question,
                    "error": str(e)
                })
        
        return results
