# 文本知识库目录

此目录用于存储电力系统相关的文本知识库文件。

## 目录结构

```
knowledge_base/text/
├── manuals/              # 设备手册
│   ├── transformer_manuals/    # 变压器手册
│   ├── breaker_manuals/       # 断路器手册
│   ├── cable_manuals/         # 电缆手册
│   └── protection_manuals/    # 保护设备手册
├── standards/            # 标准规范
│   ├── national_standards/    # 国家标准
│   ├── industry_standards/    # 行业标准
│   └── company_standards/     # 企业标准
├── procedures/           # 操作规程
│   ├── maintenance_procedures/ # 维护规程
│   ├── operation_procedures/  # 操作规程
│   └── emergency_procedures/  # 应急规程
├── technical_docs/       # 技术文档
│   ├── fault_diagnosis/       # 故障诊断
│   ├── testing_methods/       # 试验方法
│   └── analysis_reports/      # 分析报告
├── case_studies/         # 案例研究
│   ├── fault_cases/          # 故障案例
│   ├── maintenance_cases/    # 维护案例
│   └── upgrade_cases/        # 改造案例
└── reference/            # 参考资料
    ├── glossary/             # 术语词汇
    ├── formulas/             # 计算公式
    └── tables/               # 参考表格
```

## 支持的文件格式

### 文档格式
- `.txt` - 纯文本文件
- `.md` - Markdown文档
- `.pdf` - PDF文档（需OCR处理）
- `.docx` - Word文档
- `.html` - HTML文档

### 数据格式
- `.json` - 结构化数据
- `.csv` - 表格数据
- `.xml` - XML数据

## 文件命名规范

### 命名格式
```
[类别]_[设备类型]_[文档类型]_[版本]_[日期].[扩展名]

示例：
- manual_transformer_operation_v2.1_20240101.pdf
- standard_GB_testing_v1.0_20231201.txt
- procedure_maintenance_routine_v3.2_20240115.docx
```

### 类别代码
- `MAN` - 手册 (Manual)
- `STD` - 标准 (Standard)  
- `PROC` - 规程 (Procedure)
- `TECH` - 技术文档 (Technical)
- `CASE` - 案例 (Case Study)
- `REF` - 参考 (Reference)

## 内容组织规范

### 文档结构
每个文档应包含以下元数据：

```yaml
---
title: "文档标题"
category: "文档类别"
equipment_type: "设备类型"
version: "版本号"
date: "创建日期"
author: "作者"
keywords: ["关键词1", "关键词2"]
description: "文档描述"
---
```

### 内容标准
1. **标题层次**：使用标准的标题层次结构
2. **术语统一**：使用标准化的专业术语
3. **格式一致**：保持文档格式的一致性
4. **引用规范**：正确引用相关标准和文献

## 知识提取规则

### 自动提取内容
- 设备参数和规格
- 故障现象和原因
- 诊断方法和步骤
- 维护要求和周期
- 安全注意事项

### 关键信息标记
```markdown
**设备型号**：110kV油浸式变压器
**额定容量**：50MVA
**故障现象**：异响、温度升高
**可能原因**：绕组松动、冷却系统故障
**诊断方法**：声学检测、热成像检测
**处理措施**：停机检查、更换冷却油
```

## 质量控制

### 文档审核流程
1. **内容审核**：检查技术内容准确性
2. **格式审核**：确保格式符合规范
3. **版本控制**：管理文档版本更新
4. **定期更新**：根据最新标准更新内容

### 质量标准
- 技术内容准确无误
- 格式规范统一
- 信息完整详细
- 更新及时有效

## 使用指南

### 添加新文档
1. 按照命名规范命名文件
2. 添加完整的元数据信息
3. 确保内容格式规范
4. 放入相应的分类目录

### 搜索和检索
- 支持全文搜索
- 支持按类别筛选
- 支持按设备类型筛选
- 支持按关键词搜索

### 维护更新
- 定期检查文档有效性
- 及时更新过期内容
- 清理重复和无效文档
- 备份重要文档
