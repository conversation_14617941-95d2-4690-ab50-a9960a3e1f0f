# 嵌入模型训练配置

# 模型配置
model:
  base_model: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
  model_name: "power-system-embedding-v1"
  max_seq_length: 512
  embedding_dimension: 384

# 训练配置
training:
  batch_size: 32
  learning_rate: 2e-5
  num_epochs: 10
  warmup_steps: 1000
  weight_decay: 0.01
  gradient_accumulation_steps: 1
  max_grad_norm: 1.0
  
  # 学习率调度
  scheduler:
    type: "cosine"
    num_warmup_steps: 1000
    num_training_steps: 10000

# 数据配置
data:
  train_data_path: "training/datasets/processed/embedding_train.jsonl"
  val_data_path: "training/datasets/processed/embedding_val.jsonl"
  test_data_path: "training/datasets/processed/embedding_test.jsonl"
  
  # 数据增强
  augmentation:
    enable: true
    synonym_replacement: 0.1
    random_insertion: 0.1
    random_swap: 0.1
    random_deletion: 0.1

# 损失函数配置
loss:
  type: "MultipleNegativesRankingLoss"
  scale: 20.0
  similarity_fct: "cos_sim"

# 评估配置
evaluation:
  eval_steps: 500
  save_steps: 1000
  logging_steps: 100
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  
  # 评估指标
  metrics:
    - "cosine_similarity"
    - "euclidean_distance"
    - "manhattan_distance"

# 输出配置
output:
  output_dir: "training/checkpoints/embedding"
  logging_dir: "training/logs/embedding"
  save_total_limit: 3
  load_best_model_at_end: true

# 硬件配置
hardware:
  use_cuda: true
  fp16: true
  dataloader_num_workers: 4
  
# 电力领域特定配置
domain_specific:
  # 电力术语词典
  power_terms_dict: "knowledge_base/mappings/power_terms.json"
  
  # 领域适应策略
  domain_adaptation:
    enable: true
    adaptation_weight: 0.3
    
  # 专业术语保护
  preserve_technical_terms: true
  technical_terms_file: "knowledge_base/mappings/technical_terms.txt"
