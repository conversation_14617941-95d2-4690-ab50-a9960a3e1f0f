# 训练模块

本目录包含模型训练相关的代码、配置和数据。

## 目录结构

```
training/
├── configs/           # 训练配置文件
│   ├── embedding_config.yaml    # 嵌入模型训练配置
│   ├── fault_classifier_config.yaml  # 故障分类器配置
│   └── retrieval_config.yaml    # 检索模型配置
├── datasets/          # 训练数据集
│   ├── fault_cases/   # 故障案例数据
│   ├── equipment_data/ # 设备数据
│   └── processed/     # 预处理后的数据
├── scripts/           # 训练脚本
│   ├── train_embedding.py      # 嵌入模型训练
│   ├── train_classifier.py     # 分类器训练
│   ├── fine_tune_retrieval.py  # 检索模型微调
│   └── evaluate_model.py       # 模型评估
├── checkpoints/       # 模型检查点
│   ├── embedding/     # 嵌入模型检查点
│   ├── classifier/    # 分类器检查点
│   └── retrieval/     # 检索模型检查点
├── logs/             # 训练日志
│   ├── tensorboard/   # TensorBoard日志
│   └── training_logs/ # 文本日志
└── results/          # 训练结果
    ├── metrics/       # 评估指标
    ├── models/        # 最终模型
    └── reports/       # 训练报告
```

## 支持的训练任务

### 1. 嵌入模型微调
- 基于电力领域专业术语的向量表示学习
- 支持中文电力技术文档的语义理解
- 优化故障描述的向量化表示

### 2. 故障分类器训练
- 多类别故障分类模型
- 基于设备类型和故障模式的分类
- 支持不平衡数据集处理

### 3. 检索模型优化
- RAG检索效果优化
- 相关性排序模型训练
- 多模态检索融合

## 使用说明

### 准备训练数据
```bash
# 数据预处理
python scripts/prepare_training_data.py --input data/01_raw --output training/datasets

# 数据验证
python scripts/validate_dataset.py --dataset training/datasets
```

### 开始训练
```bash
# 嵌入模型微调
python scripts/train_embedding.py --config training/configs/embedding_config.yaml

# 故障分类器训练
python scripts/train_classifier.py --config training/configs/fault_classifier_config.yaml

# 检索模型微调
python scripts/fine_tune_retrieval.py --config training/configs/retrieval_config.yaml
```

### 模型评估
```bash
# 评估所有模型
python scripts/evaluate_model.py --model_type all

# 评估特定模型
python scripts/evaluate_model.py --model_type embedding --checkpoint training/checkpoints/embedding/best_model.pt
```

## 配置说明

### 嵌入模型配置
- 基础模型：sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
- 微调策略：领域适应性训练
- 训练数据：电力技术文档和故障案例

### 分类器配置
- 架构：基于Transformer的分类器
- 类别：设备类型、故障类型、严重程度
- 损失函数：加权交叉熵（处理类别不平衡）

### 检索模型配置
- 双塔架构：查询编码器 + 文档编码器
- 负采样策略：困难负样本挖掘
- 评估指标：NDCG@10, MRR, Recall@K

## 注意事项

1. **数据隐私**：确保训练数据已脱敏处理
2. **计算资源**：建议使用GPU进行训练
3. **模型版本**：保持与生产环境的模型版本一致
4. **评估标准**：使用真实业务场景的评估数据

## 模型部署

训练完成的模型可以通过以下方式部署：

```bash
# 导出模型
python scripts/export_model.py --checkpoint training/checkpoints/best_model.pt --output models/

# 更新生产配置
python scripts/update_production_config.py --model_path models/fine_tuned_model
```
