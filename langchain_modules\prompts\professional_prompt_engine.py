#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业级提示词工程系统
重构提示词模板和上下文构建策略，达到标准大模型的质量要求
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from loguru import logger


@dataclass
class ContextualInformation:
    """上下文信息结构"""
    equipment_specs: Dict[str, Any]
    fault_analysis: Dict[str, Any]
    technical_parameters: Dict[str, Any]
    historical_cases: List[Dict[str, Any]]
    expert_knowledge: List[str]
    regulatory_standards: Dict[str, Any]
    confidence_score: float


@dataclass
class PromptTemplate:
    """专业提示词模板"""
    template_id: str
    name: str
    description: str
    system_prompt: str
    context_structure: str
    user_prompt_template: str
    expected_output_format: str
    quality_requirements: Dict[str, Any]
    model_specific_optimizations: Dict[str, str]


class ProfessionalPromptEngine:
    """专业级提示词工程引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # 专业领域知识库
        self.domain_knowledge = self._build_domain_knowledge()
        
        # 提示词模板库
        self.prompt_templates = self._build_professional_templates()
        
        # 上下文构建策略
        self.context_strategies = self._define_context_strategies()
        
        # 质量评估标准
        self.quality_standards = self._define_quality_standards()
        
        logger.info("专业级提示词工程引擎初始化完成")
    
    def _build_domain_knowledge(self) -> Dict[str, Any]:
        """构建专业领域知识库"""
        return {
            "power_system_fundamentals": {
                "voltage_levels": ["1000kV", "800kV", "500kV", "330kV", "220kV", "110kV", "35kV", "10kV"],
                "equipment_categories": ["发电设备", "输电设备", "变电设备", "配电设备", "用电设备"],
                "protection_principles": ["选择性", "速动性", "灵敏性", "可靠性"],
                "fault_classification": ["永久性故障", "瞬时性故障", "间歇性故障"]
            },
            "technical_standards": {
                "national_standards": ["GB/T", "DL/T", "Q/GDW"],
                "international_standards": ["IEC", "IEEE", "CIGRE"],
                "industry_practices": ["运行规程", "检修规程", "试验规程"]
            },
            "analysis_methodologies": {
                "fault_diagnosis": ["现象分析", "原因分析", "机理分析", "影响分析"],
                "risk_assessment": ["概率分析", "后果分析", "风险等级", "控制措施"],
                "optimization_approaches": ["技术优化", "经济优化", "运行优化", "管理优化"]
            },
            "professional_terminology": {
                "equipment_terms": ["变压器", "断路器", "隔离开关", "互感器", "避雷器"],
                "fault_terms": ["短路", "接地", "断线", "过载", "绝缘击穿"],
                "protection_terms": ["差动保护", "距离保护", "过流保护", "接地保护"],
                "measurement_terms": ["有功功率", "无功功率", "电压", "电流", "频率"]
            }
        }
    
    def _build_professional_templates(self) -> Dict[str, PromptTemplate]:
        """构建专业提示词模板库"""
        templates = {}
        
        # DeepSeek-R1 专业故障分析模板
        templates["deepseek_r1_professional"] = PromptTemplate(
            template_id="deepseek_r1_professional",
            name="DeepSeek-R1专业故障分析",
            description="针对DeepSeek-R1模型优化的专业电力故障分析模板",
            system_prompt="""你是白银市电力系统首席故障诊断专家，拥有25年变电站运维经验，精通IEC 61850、IEEE C37系列标准。

专业要求：
- 严格遵循电力系统分析方法论
- 基于提供的技术资料进行深度分析
- 运用概率推理和因果分析
- 提供可操作的专业建议
- 确保分析的科学性和实用性

分析框架：
1. 设备技术状态评估
2. 故障机理深度分析  
3. 多因素综合诊断
4. 风险评估与控制
5. 优化改进建议""",
            context_structure="""## 技术背景资料
{technical_specifications}

## 设备运行数据
{operational_data}

## 故障现象记录
{fault_phenomena}

## 历史案例参考
{historical_cases}

## 专家知识库
{expert_knowledge}""",
            user_prompt_template="""<think>
请运用专业知识进行系统性分析：

1. 技术状态评估
   - 设备基本参数分析
   - 运行工况评估
   - 异常指标识别
   - 趋势变化分析

2. 故障机理分析
   - 物理过程分析
   - 电气特性变化
   - 机械状态评估
   - 环境因素影响

3. 多因素诊断
   - 主要原因识别
   - 次要因素分析
   - 相互作用评估
   - 概率权重分配

4. 风险评估
   - 故障发展趋势
   - 潜在影响范围
   - 安全风险等级
   - 经济损失评估

5. 解决方案制定
   - 应急处理措施
   - 根本解决方案
   - 实施优先级
   - 资源需求分析
</think>

<answer>
基于专业分析，提供结构化诊断结论：

【设备技术评估】
- 设备型号规格：
- 技术状态等级：
- 关键参数偏差：

【故障机理分析】
- 故障类型定性：
- 发生机理解释：
- 关键影响因素：

【综合诊断结论】
- 主要故障原因：
- 次要影响因素：
- 故障严重程度：

【处理方案建议】
- 紧急处置措施：
- 根本解决方案：
- 实施时间安排：

【风险控制建议】
- 监控重点项目：
- 预防改进措施：
- 长期优化方向：
</answer>

请对以下电力系统问题进行专业诊断：{query}""",
            expected_output_format="结构化专业分析报告",
            quality_requirements={
                "technical_accuracy": 0.95,
                "logical_coherence": 0.90,
                "practical_applicability": 0.85,
                "professional_depth": 0.90
            },
            model_specific_optimizations={
                "deepseek_r1": "强化推理链条，增加思考过程可视化",
                "deepseek_v3": "优化输出结构，提升专业表达"
            }
        )
        
        # DeepSeek-V3 专业分析模板
        templates["deepseek_v3_professional"] = PromptTemplate(
            template_id="deepseek_v3_professional",
            name="DeepSeek-V3专业分析",
            description="针对DeepSeek-V3模型优化的专业分析模板",
            system_prompt="""你是白银市电力系统资深技术专家，具备深厚的理论基础和丰富的实践经验。

专业标准：
- 遵循国家电网技术标准
- 基于科学的分析方法
- 提供精确的技术判断
- 确保建议的可操作性

分析要求：
- 技术分析要深入透彻
- 逻辑推理要严密清晰
- 专业表达要准确规范
- 实用建议要具体可行""",
            context_structure="""## 专业技术资料
{professional_data}

## 设备状态信息  
{equipment_status}

## 运行监测数据
{monitoring_data}

## 相关标准规范
{technical_standards}""",
            user_prompt_template="""基于提供的专业资料，请进行深度技术分析：

{query}

请按照以下专业框架进行分析：

**技术状态评估**
从设备参数、运行工况、性能指标等维度进行全面评估

**故障机理分析**  
运用电力系统理论，深入分析故障发生的物理过程和技术原因

**影响因素识别**
系统梳理内在因素、外在条件、环境影响等多重因素

**解决方案制定**
提出技术可行、经济合理、安全可靠的处理方案

**预防改进建议**
从技术、管理、运维等角度提出系统性改进措施

要求：使用专业术语，保持逻辑清晰，提供具体可操作的建议。""",
            expected_output_format="专业技术分析报告",
            quality_requirements={
                "technical_precision": 0.95,
                "analytical_depth": 0.90,
                "solution_practicality": 0.85,
                "expression_clarity": 0.90
            },
            model_specific_optimizations={
                "deepseek_v3": "优化专业表达，增强逻辑结构"
            }
        )
        
        return templates
    
    def _define_context_strategies(self) -> Dict[str, Any]:
        """定义上下文构建策略"""
        return {
            "hierarchical_organization": {
                "priority_levels": ["critical", "important", "supplementary"],
                "information_types": ["technical_specs", "operational_data", "historical_cases", "expert_knowledge"],
                "relevance_thresholds": {"high": 0.8, "medium": 0.6, "low": 0.4}
            },
            "semantic_enhancement": {
                "keyword_expansion": True,
                "concept_mapping": True,
                "relationship_analysis": True,
                "context_enrichment": True
            },
            "quality_filtering": {
                "min_relevance_score": 0.5,
                "max_context_length": 4000,
                "diversity_requirement": 0.7,
                "technical_depth_threshold": 0.6
            }
        }
    
    def _define_quality_standards(self) -> Dict[str, Any]:
        """定义质量评估标准"""
        return {
            "context_quality": {
                "relevance_score": 0.8,
                "completeness_score": 0.85,
                "accuracy_score": 0.95,
                "diversity_score": 0.7
            },
            "prompt_effectiveness": {
                "clarity_score": 0.9,
                "specificity_score": 0.85,
                "actionability_score": 0.8,
                "professional_level": 0.9
            },
            "output_requirements": {
                "technical_accuracy": 0.95,
                "logical_coherence": 0.9,
                "practical_value": 0.85,
                "professional_depth": 0.9
            }
        }
    
    def build_professional_context(self, query: str, retrieval_results: List[Dict[str, Any]]) -> ContextualInformation:
        """构建专业级上下文信息"""
        try:
            logger.info(f"构建专业上下文: {query[:50]}...")
            
            # 分析查询意图
            query_analysis = self._analyze_query_intent(query)
            
            # 筛选和组织检索结果
            organized_results = self._organize_retrieval_results(retrieval_results, query_analysis)
            
            # 提取关键技术信息
            technical_info = self._extract_technical_information(organized_results)
            
            # 构建结构化上下文
            context_info = ContextualInformation(
                equipment_specs=technical_info.get("equipment_specs", {}),
                fault_analysis=technical_info.get("fault_analysis", {}),
                technical_parameters=technical_info.get("technical_parameters", {}),
                historical_cases=technical_info.get("historical_cases", []),
                expert_knowledge=technical_info.get("expert_knowledge", []),
                regulatory_standards=technical_info.get("regulatory_standards", {}),
                confidence_score=self._calculate_context_confidence(organized_results)
            )
            
            logger.info(f"专业上下文构建完成，置信度: {context_info.confidence_score:.2f}")
            return context_info
            
        except Exception as e:
            logger.error(f"专业上下文构建失败: {str(e)}")
            return self._build_fallback_context()
    
    def generate_professional_prompt(self, template_id: str, query: str, context_info: ContextualInformation) -> str:
        """生成专业级提示词"""
        try:
            template = self.prompt_templates.get(template_id)
            if not template:
                logger.error(f"未找到模板: {template_id}")
                return self._generate_fallback_prompt(query)
            
            # 构建结构化上下文字符串
            context_str = self._format_contextual_information(context_info, template.context_structure)
            
            # 生成完整提示词
            if template_id.startswith("deepseek_r1"):
                full_prompt = f"{template.system_prompt}\n\n{context_str}\n\n{template.user_prompt_template.format(query=query)}"
            else:
                full_prompt = template.user_prompt_template.format(
                    professional_data=context_str,
                    equipment_status=self._format_equipment_status(context_info),
                    monitoring_data=self._format_monitoring_data(context_info),
                    technical_standards=self._format_technical_standards(context_info),
                    query=query
                )
            
            logger.info(f"专业提示词生成完成，长度: {len(full_prompt)}")
            return full_prompt
            
        except Exception as e:
            logger.error(f"专业提示词生成失败: {str(e)}")
            return self._generate_fallback_prompt(query)
