2025-07-24 16:39:46 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-24 16:39:46 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 16:40:20 | ERROR | data_processing.modern_chroma_manager:_init_client:56 | ❌ Chroma客户端初始化失败: expected str, bytes or os.PathLike object, not dict
2025-07-24 16:40:21 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'retriever.knowledge_base'
2025-07-24 16:40:21 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 16:55:19 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-24 16:55:19 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 16:58:39 | ERROR | data_processing.modern_chroma_manager:_init_client:56 | ❌ Chroma客户端初始化失败: expected str, bytes or os.PathLike object, not dict
2025-07-24 16:58:39 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'retriever.knowledge_base'
2025-07-24 16:58:39 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-24 17:31:25 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'retriever.enhanced_knowledge_base'
2025-07-24 17:31:25 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:22:22 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:22:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:22:23 | ERROR | __main__:start_flask_server:104 | Flask服务器启动失败: name 'get_processed_data_path' is not defined
2025-07-25 10:22:23 | ERROR | __main__:start_optimized_server:279 | ❌ 主服务器启动失败: name 'get_processed_data_path' is not defined
2025-07-25 10:22:23 | ERROR | __main__:main:384 | ❌ 系统启动失败: name 'get_processed_data_path' is not defined
2025-07-25 10:25:10 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:25:10 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:25:11 | ERROR | __main__:start_flask_server:104 | Flask服务器启动失败: 'UnifiedConfigManager' object has no attribute 'project_root'
2025-07-25 10:25:11 | ERROR | __main__:start_optimized_server:279 | ❌ 主服务器启动失败: 'UnifiedConfigManager' object has no attribute 'project_root'
2025-07-25 10:25:11 | ERROR | __main__:main:384 | ❌ 系统启动失败: 'UnifiedConfigManager' object has no attribute 'project_root'
2025-07-25 10:26:15 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:26:15 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:26:17 | ERROR | __main__:start_flask_server:104 | Flask服务器启动失败: View function mapping is overwriting an existing endpoint function: health_check
2025-07-25 10:26:17 | ERROR | __main__:start_optimized_server:279 | ❌ 主服务器启动失败: View function mapping is overwriting an existing endpoint function: health_check
2025-07-25 10:26:17 | ERROR | __main__:main:384 | ❌ 系统启动失败: View function mapping is overwriting an existing endpoint function: health_check
2025-07-25 10:28:15 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:28:15 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:28:25 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:28:25 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:30:57 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:30:57 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:31:07 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:31:07 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:34:22 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:34:22 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:34:33 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:34:33 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:39:15 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:39:15 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:39:56 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:39:56 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:40:57 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:40:57 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:42:05 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:42:05 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:42:25 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:42:25 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:42:47 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:42:47 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:43:09 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:43:09 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:43:42 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:43:42 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:44:02 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:44:02 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:44:26 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:44:26 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:46:31 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:46:32 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 10:47:05 | ERROR | core.fault_analyzer:_initialize_chains:98 | 推理链初始化失败: 1 validation error for FaultAnalysisChain
llm
  none is not an allowed value (type=type_error.none.not_allowed)
2025-07-25 10:47:05 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 13:40:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-25 13:40:23 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 13:40:24 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-25 13:50:45 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-25 13:50:45 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 13:50:45 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
2025-07-25 13:50:56 | ERROR | core.service_manager:get_service:69 | 创建服务失败 fault_analyzer: No module named 'cv2'
2025-07-25 13:50:56 | ERROR | core.service_manager:get_service:69 | 创建服务失败 equipment_manager: EquipmentManager.__init__() missing 1 required positional argument: 'config'
2025-07-25 13:50:56 | ERROR | data_processing.modern_chroma_manager:__init__:34 | ChromaDB不可用，无法初始化
