#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
案例库维护系统
"""

import os
import time
import json
import hashlib
import schedule
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class MaintenanceTask:
    """维护任务"""
    task_id: str
    task_type: str
    description: str
    scheduled_time: float
    priority: str  # high, medium, low
    status: str  # pending, running, completed, failed
    created_time: float
    completed_time: Optional[float] = None
    error_message: Optional[str] = None

@dataclass
class CaseQualityMetrics:
    """案例质量指标"""
    case_id: str
    completeness_score: float  # 完整性分数
    accuracy_score: float      # 准确性分数
    relevance_score: float     # 相关性分数
    freshness_score: float     # 时效性分数
    usage_score: float         # 使用频率分数
    overall_score: float       # 综合分数
    last_updated: float

class CaseMaintenanceSystem:
    """案例库维护系统"""
    
    def __init__(self, knowledge_base_path: str = "knowledge_base"):
        self.kb_path = Path(knowledge_base_path)
        self.maintenance_path = self.kb_path / "maintenance"
        self.maintenance_path.mkdir(parents=True, exist_ok=True)
        
        # 维护配置
        self.config = {
            'quality_check_interval': 7,      # 质量检查间隔(天)
            'content_update_interval': 30,    # 内容更新间隔(天)
            'backup_interval': 1,             # 备份间隔(天)
            'cleanup_interval': 90,           # 清理间隔(天)
            'min_quality_threshold': 0.6,     # 最低质量阈值
            'max_case_age_days': 365,         # 案例最大年龄(天)
            'min_usage_threshold': 5          # 最低使用次数阈值
        }
        
        # 维护任务队列
        self.maintenance_tasks: List[MaintenanceTask] = []
        self.task_history: List[MaintenanceTask] = []
        
        # 质量指标缓存
        self.quality_metrics: Dict[str, CaseQualityMetrics] = {}
        
        # 文件监控
        self.file_checksums: Dict[str, str] = {}
        
        self._load_maintenance_data()
        self._schedule_maintenance_tasks()

    def start_maintenance_service(self):
        """启动维护服务"""
        logger.info("启动案例库维护服务...")
        
        # 立即执行一次质量检查
        self.schedule_quality_check()
        
        # 启动定时任务
        while True:
            try:
                schedule.run_pending()
                self._process_maintenance_tasks()
                time.sleep(60)  # 每分钟检查一次
                
            except KeyboardInterrupt:
                logger.info("维护服务停止")
                break
            except Exception as e:
                logger.error(f"维护服务异常: {e}")
                time.sleep(60)

    def schedule_quality_check(self, priority: str = "medium"):
        """安排质量检查任务"""
        task = MaintenanceTask(
            task_id=f"quality_check_{int(time.time())}",
            task_type="quality_check",
            description="执行案例库质量检查",
            scheduled_time=time.time(),
            priority=priority,
            status="pending",
            created_time=time.time()
        )
        
        self.maintenance_tasks.append(task)
        logger.info(f"已安排质量检查任务: {task.task_id}")

    def schedule_content_update(self, priority: str = "low"):
        """安排内容更新任务"""
        task = MaintenanceTask(
            task_id=f"content_update_{int(time.time())}",
            task_type="content_update",
            description="检查和更新过时内容",
            scheduled_time=time.time(),
            priority=priority,
            status="pending",
            created_time=time.time()
        )
        
        self.maintenance_tasks.append(task)
        logger.info(f"已安排内容更新任务: {task.task_id}")

    def schedule_backup(self, priority: str = "high"):
        """安排备份任务"""
        task = MaintenanceTask(
            task_id=f"backup_{int(time.time())}",
            task_type="backup",
            description="备份案例库数据",
            scheduled_time=time.time(),
            priority=priority,
            status="pending",
            created_time=time.time()
        )
        
        self.maintenance_tasks.append(task)
        logger.info(f"已安排备份任务: {task.task_id}")

    def schedule_cleanup(self, priority: str = "low"):
        """安排清理任务"""
        task = MaintenanceTask(
            task_id=f"cleanup_{int(time.time())}",
            task_type="cleanup",
            description="清理低质量和过时案例",
            scheduled_time=time.time(),
            priority=priority,
            status="pending",
            created_time=time.time()
        )
        
        self.maintenance_tasks.append(task)
        logger.info(f"已安排清理任务: {task.task_id}")

    def perform_quality_check(self) -> Dict[str, any]:
        """执行质量检查"""
        logger.info("开始执行质量检查...")
        
        results = {
            'total_cases': 0,
            'high_quality_cases': 0,
            'medium_quality_cases': 0,
            'low_quality_cases': 0,
            'issues_found': [],
            'recommendations': []
        }
        
        # 扫描所有案例文件
        case_files = list(self.kb_path.glob("**/*.md"))
        results['total_cases'] = len(case_files)
        
        for case_file in case_files:
            try:
                case_id = case_file.stem
                
                # 读取案例内容
                with open(case_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 计算质量指标
                metrics = self._calculate_quality_metrics(case_id, content, case_file)
                self.quality_metrics[case_id] = metrics
                
                # 分类质量等级
                if metrics.overall_score >= 0.8:
                    results['high_quality_cases'] += 1
                elif metrics.overall_score >= 0.6:
                    results['medium_quality_cases'] += 1
                else:
                    results['low_quality_cases'] += 1
                    results['issues_found'].append({
                        'case_id': case_id,
                        'issue': '质量分数过低',
                        'score': metrics.overall_score
                    })
                
                # 检查具体问题
                issues = self._identify_quality_issues(case_id, content, metrics)
                results['issues_found'].extend(issues)
                
            except Exception as e:
                logger.error(f"检查案例 {case_file} 时出错: {e}")
                results['issues_found'].append({
                    'case_id': case_file.stem,
                    'issue': f'检查失败: {e}',
                    'score': 0.0
                })
        
        # 生成改进建议
        results['recommendations'] = self._generate_quality_recommendations(results)
        
        # 保存质量报告
        self._save_quality_report(results)
        
        logger.info(f"质量检查完成: 总计{results['total_cases']}个案例")
        return results

    def perform_content_update(self) -> Dict[str, any]:
        """执行内容更新"""
        logger.info("开始执行内容更新...")
        
        results = {
            'checked_cases': 0,
            'updated_cases': 0,
            'outdated_cases': [],
            'update_suggestions': []
        }
        
        current_time = time.time()
        max_age = self.config['max_case_age_days'] * 24 * 3600
        
        # 检查所有案例的时效性
        for case_id, metrics in self.quality_metrics.items():
            results['checked_cases'] += 1
            
            case_age = current_time - metrics.last_updated
            
            if case_age > max_age:
                results['outdated_cases'].append({
                    'case_id': case_id,
                    'age_days': case_age / (24 * 3600),
                    'freshness_score': metrics.freshness_score
                })
                
                # 生成更新建议
                suggestions = self._generate_update_suggestions(case_id, metrics)
                results['update_suggestions'].extend(suggestions)
        
        logger.info(f"内容更新检查完成: 发现{len(results['outdated_cases'])}个过时案例")
        return results

    def perform_backup(self) -> Dict[str, any]:
        """执行备份"""
        logger.info("开始执行备份...")
        
        backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = self.maintenance_path / f"backup_{backup_time}"
        backup_dir.mkdir(exist_ok=True)
        
        results = {
            'backup_path': str(backup_dir),
            'backed_up_files': 0,
            'backup_size': 0,
            'success': True,
            'error_message': None
        }
        
        try:
            # 备份案例文件
            import shutil
            
            for case_file in self.kb_path.glob("**/*.md"):
                relative_path = case_file.relative_to(self.kb_path)
                backup_file = backup_dir / relative_path
                backup_file.parent.mkdir(parents=True, exist_ok=True)
                
                shutil.copy2(case_file, backup_file)
                results['backed_up_files'] += 1
                results['backup_size'] += case_file.stat().st_size
            
            # 备份元数据
            metadata_files = list(self.kb_path.glob("metadata/*.json"))
            for metadata_file in metadata_files:
                backup_metadata_dir = backup_dir / "metadata"
                backup_metadata_dir.mkdir(exist_ok=True)
                shutil.copy2(metadata_file, backup_metadata_dir / metadata_file.name)
            
            # 创建备份清单
            manifest = {
                'backup_time': backup_time,
                'total_files': results['backed_up_files'],
                'total_size': results['backup_size'],
                'kb_path': str(self.kb_path)
            }
            
            with open(backup_dir / "manifest.json", 'w', encoding='utf-8') as f:
                json.dump(manifest, f, ensure_ascii=False, indent=2)
            
            logger.info(f"备份完成: {results['backed_up_files']}个文件, {results['backup_size']}字节")
            
        except Exception as e:
            results['success'] = False
            results['error_message'] = str(e)
            logger.error(f"备份失败: {e}")
        
        return results

    def perform_cleanup(self) -> Dict[str, any]:
        """执行清理"""
        logger.info("开始执行清理...")
        
        results = {
            'checked_cases': 0,
            'removed_cases': 0,
            'archived_cases': 0,
            'cleanup_actions': []
        }
        
        min_quality = self.config['min_quality_threshold']
        min_usage = self.config['min_usage_threshold']
        
        cases_to_remove = []
        cases_to_archive = []
        
        for case_id, metrics in self.quality_metrics.items():
            results['checked_cases'] += 1
            
            # 标记低质量案例
            if metrics.overall_score < min_quality:
                if metrics.usage_score < min_usage:
                    cases_to_remove.append(case_id)
                else:
                    cases_to_archive.append(case_id)
        
        # 执行清理操作
        for case_id in cases_to_remove:
            if self._remove_case(case_id):
                results['removed_cases'] += 1
                results['cleanup_actions'].append(f"删除低质量案例: {case_id}")
        
        for case_id in cases_to_archive:
            if self._archive_case(case_id):
                results['archived_cases'] += 1
                results['cleanup_actions'].append(f"归档案例: {case_id}")
        
        logger.info(f"清理完成: 删除{results['removed_cases']}个, 归档{results['archived_cases']}个")
        return results

    def _calculate_quality_metrics(self, case_id: str, content: str, file_path: Path) -> CaseQualityMetrics:
        """计算质量指标"""
        # 完整性分数 (基于内容结构)
        completeness_score = self._calculate_completeness_score(content)
        
        # 准确性分数 (基于内容质量)
        accuracy_score = self._calculate_accuracy_score(content)
        
        # 相关性分数 (基于关键词和标签)
        relevance_score = self._calculate_relevance_score(content)
        
        # 时效性分数 (基于文件修改时间)
        freshness_score = self._calculate_freshness_score(file_path)
        
        # 使用频率分数 (基于访问统计)
        usage_score = self._calculate_usage_score(case_id)
        
        # 综合分数
        overall_score = (
            completeness_score * 0.25 +
            accuracy_score * 0.25 +
            relevance_score * 0.2 +
            freshness_score * 0.15 +
            usage_score * 0.15
        )
        
        return CaseQualityMetrics(
            case_id=case_id,
            completeness_score=completeness_score,
            accuracy_score=accuracy_score,
            relevance_score=relevance_score,
            freshness_score=freshness_score,
            usage_score=usage_score,
            overall_score=overall_score,
            last_updated=time.time()
        )

    def _calculate_completeness_score(self, content: str) -> float:
        """计算完整性分数"""
        required_sections = [
            '故障基本信息', '故障现象', '故障分析', '处理措施', '经验总结'
        ]
        
        score = 0.0
        for section in required_sections:
            if section in content:
                score += 1.0
        
        return score / len(required_sections)

    def _calculate_accuracy_score(self, content: str) -> float:
        """计算准确性分数"""
        # 基于内容长度、技术术语密度等
        content_length = len(content)
        
        # 技术术语
        technical_terms = [
            '变压器', '断路器', '母线', '继电保护', '故障', '电压', '电流',
            '绝缘', '短路', '接地', '谐波', '功率', '频率'
        ]
        
        term_count = sum(1 for term in technical_terms if term in content)
        term_density = term_count / len(technical_terms)
        
        # 长度分数
        length_score = min(content_length / 2000, 1.0)  # 2000字符为满分
        
        return (length_score * 0.6 + term_density * 0.4)

    def _calculate_relevance_score(self, content: str) -> float:
        """计算相关性分数"""
        # 基于白银电网相关内容
        baiyin_keywords = ['白银', '白银电网', '白银变电站', '白银工业园区']
        power_keywords = ['电力', '电网', '变电站', '供电', '配电']
        
        baiyin_score = 1.0 if any(keyword in content for keyword in baiyin_keywords) else 0.5
        power_score = 1.0 if any(keyword in content for keyword in power_keywords) else 0.0
        
        return (baiyin_score * 0.6 + power_score * 0.4)

    def _calculate_freshness_score(self, file_path: Path) -> float:
        """计算时效性分数"""
        try:
            mtime = file_path.stat().st_mtime
            age_days = (time.time() - mtime) / (24 * 3600)
            
            # 30天内为满分，365天后为0分
            if age_days <= 30:
                return 1.0
            elif age_days >= 365:
                return 0.0
            else:
                return 1.0 - (age_days - 30) / 335
                
        except:
            return 0.5

    def _calculate_usage_score(self, case_id: str) -> float:
        """计算使用频率分数"""
        # 这里应该从使用统计中获取数据
        # 暂时返回默认值
        return 0.5

    def _identify_quality_issues(self, case_id: str, content: str, metrics: CaseQualityMetrics) -> List[Dict]:
        """识别质量问题"""
        issues = []
        
        if metrics.completeness_score < 0.8:
            issues.append({
                'case_id': case_id,
                'issue': '内容不完整',
                'score': metrics.completeness_score
            })
        
        if metrics.accuracy_score < 0.6:
            issues.append({
                'case_id': case_id,
                'issue': '内容准确性不足',
                'score': metrics.accuracy_score
            })
        
        if metrics.freshness_score < 0.3:
            issues.append({
                'case_id': case_id,
                'issue': '内容过时',
                'score': metrics.freshness_score
            })
        
        return issues

    def _generate_quality_recommendations(self, results: Dict) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        total_cases = results['total_cases']
        low_quality_cases = results['low_quality_cases']
        
        if low_quality_cases > total_cases * 0.2:
            recommendations.append("建议重点改进低质量案例，提升整体质量水平")
        
        if results['high_quality_cases'] < total_cases * 0.5:
            recommendations.append("建议增加高质量案例的数量")
        
        recommendations.append("定期更新案例内容，保持时效性")
        recommendations.append("完善案例结构，确保包含所有必要章节")
        
        return recommendations

    def _generate_update_suggestions(self, case_id: str, metrics: CaseQualityMetrics) -> List[Dict]:
        """生成更新建议"""
        suggestions = []
        
        if metrics.freshness_score < 0.5:
            suggestions.append({
                'case_id': case_id,
                'type': 'content_update',
                'suggestion': '更新案例内容，添加最新的技术发展和经验'
            })
        
        if metrics.completeness_score < 0.8:
            suggestions.append({
                'case_id': case_id,
                'type': 'structure_improvement',
                'suggestion': '完善案例结构，补充缺失的章节'
            })
        
        return suggestions

    def _remove_case(self, case_id: str) -> bool:
        """删除案例"""
        try:
            case_file = self.kb_path / "text" / f"{case_id}.md"
            if case_file.exists():
                case_file.unlink()
                return True
        except Exception as e:
            logger.error(f"删除案例 {case_id} 失败: {e}")
        
        return False

    def _archive_case(self, case_id: str) -> bool:
        """归档案例"""
        try:
            archive_dir = self.kb_path / "archived"
            archive_dir.mkdir(exist_ok=True)
            
            case_file = self.kb_path / "text" / f"{case_id}.md"
            if case_file.exists():
                import shutil
                shutil.move(str(case_file), str(archive_dir / f"{case_id}.md"))
                return True
        except Exception as e:
            logger.error(f"归档案例 {case_id} 失败: {e}")
        
        return False

    def _save_quality_report(self, results: Dict):
        """保存质量报告"""
        report_file = self.maintenance_path / f"quality_report_{int(time.time())}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

    def _process_maintenance_tasks(self):
        """处理维护任务"""
        # 按优先级排序任务
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        pending_tasks = [t for t in self.maintenance_tasks if t.status == 'pending']
        pending_tasks.sort(key=lambda x: priority_order.get(x.priority, 0), reverse=True)
        
        for task in pending_tasks[:3]:  # 每次最多处理3个任务
            self._execute_maintenance_task(task)

    def _execute_maintenance_task(self, task: MaintenanceTask):
        """执行维护任务"""
        logger.info(f"执行维护任务: {task.task_id} - {task.description}")
        
        task.status = 'running'
        
        try:
            if task.task_type == 'quality_check':
                self.perform_quality_check()
            elif task.task_type == 'content_update':
                self.perform_content_update()
            elif task.task_type == 'backup':
                self.perform_backup()
            elif task.task_type == 'cleanup':
                self.perform_cleanup()
            
            task.status = 'completed'
            task.completed_time = time.time()
            
        except Exception as e:
            task.status = 'failed'
            task.error_message = str(e)
            logger.error(f"维护任务执行失败: {task.task_id} - {e}")
        
        # 移动到历史记录
        self.task_history.append(task)
        self.maintenance_tasks.remove(task)

    def _schedule_maintenance_tasks(self):
        """安排定时维护任务"""
        # 每日备份
        schedule.every().day.at("02:00").do(self.schedule_backup)

        # 每周质量检查
        schedule.every().week.do(self.schedule_quality_check)

        # 每30天内容更新检查
        schedule.every(30).days.do(self.schedule_content_update)

        # 每季度清理
        schedule.every(90).days.do(self.schedule_cleanup)

    def _load_maintenance_data(self):
        """加载维护数据"""
        # 加载质量指标缓存
        quality_file = self.maintenance_path / "quality_metrics.json"
        if quality_file.exists():
            try:
                with open(quality_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # 重建质量指标对象
                # 实现省略...
            except Exception as e:
                logger.error(f"加载质量指标失败: {e}")

# 全局维护系统实例
case_maintenance_system = CaseMaintenanceSystem()
