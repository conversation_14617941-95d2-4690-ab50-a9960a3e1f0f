#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境变量管理器
统一管理环境变量，提供类型转换、默认值、验证等功能
支持生产环境的安全配置管理
"""

import os
from typing import Any, Dict, Optional, Union, List, Callable
from pathlib import Path
from loguru import logger
from dataclasses import dataclass
from enum import Enum


class EnvironmentType(Enum):
    """环境类型"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class EnvironmentVariable:
    """环境变量定义"""
    name: str
    default: Any
    var_type: type
    description: str
    required: bool = False
    validator: Optional[Callable[[Any], bool]] = None
    sensitive: bool = False  # 是否为敏感信息


class EnvironmentManager:
    """环境变量管理器"""
    
    def __init__(self):
        self._variables = self._define_variables()
        self._loaded_values = {}
        self._environment_type = self._detect_environment()
        
    def _define_variables(self) -> Dict[str, EnvironmentVariable]:
        """定义所有环境变量"""
        return {
            # 系统环境
            "ENVIRONMENT": EnvironmentVariable(
                name="ENVIRONMENT",
                default="development",
                var_type=str,
                description="运行环境",
                validator=lambda x: x in ["development", "staging", "production"]
            ),
            "DEBUG": EnvironmentVariable(
                name="DEBUG",
                default=True,
                var_type=bool,
                description="调试模式"
            ),
            "LOG_LEVEL": EnvironmentVariable(
                name="LOG_LEVEL",
                default="INFO",
                var_type=str,
                description="日志级别",
                validator=lambda x: x in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
            ),
            
            # 服务器配置
            "HOST": EnvironmentVariable(
                name="HOST",
                default="0.0.0.0",
                var_type=str,
                description="服务器主机地址"
            ),
            "PORT": EnvironmentVariable(
                name="PORT",
                default=5002,
                var_type=int,
                description="服务器端口"
            ),
            "WORKERS": EnvironmentVariable(
                name="WORKERS",
                default=4,
                var_type=int,
                description="工作进程数"
            ),
            
            # API配置
            "DEEPSEEK_API_KEY": EnvironmentVariable(
                name="DEEPSEEK_API_KEY",
                default="",
                var_type=str,
                description="DeepSeek API密钥",
                required=True,
                sensitive=True,
                validator=lambda x: x.startswith("sk-") if x else False
            ),
            "DEEPSEEK_API_BASE": EnvironmentVariable(
                name="DEEPSEEK_API_BASE",
                default="https://dashscope.aliyuncs.com/compatible-mode/v1",
                var_type=str,
                description="DeepSeek API基础URL",
                validator=lambda x: x.startswith(("http://", "https://"))
            ),
            "DEEPSEEK_R1_MODEL": EnvironmentVariable(
                name="DEEPSEEK_R1_MODEL",
                default="deepseek-r1",
                var_type=str,
                description="DeepSeek R1模型名称"
            ),
            "DEEPSEEK_CHAT_MODEL": EnvironmentVariable(
                name="DEEPSEEK_CHAT_MODEL",
                default="deepseek-v3",
                var_type=str,
                description="DeepSeek聊天模型名称"
            ),
            
            # 数据库配置
            "DATABASE_URL": EnvironmentVariable(
                name="DATABASE_URL",
                default="sqlite:///./fault_diagnosis.db",
                var_type=str,
                description="数据库连接URL"
            ),
            "VECTOR_DB_PATH": EnvironmentVariable(
                name="VECTOR_DB_PATH",
                default="./embeddings/chroma_store",
                var_type=str,
                description="向量数据库路径"
            ),
            
            # 文件上传配置
            "UPLOAD_MAX_SIZE": EnvironmentVariable(
                name="UPLOAD_MAX_SIZE",
                default=100,
                var_type=int,
                description="最大上传文件大小(MB)"
            ),
            "UPLOAD_PATH": EnvironmentVariable(
                name="UPLOAD_PATH",
                default="./uploads",
                var_type=str,
                description="文件上传路径"
            ),
            
            # 安全配置
            "SECRET_KEY": EnvironmentVariable(
                name="SECRET_KEY",
                default="dev-secret-key",
                var_type=str,
                description="应用密钥",
                required=True,
                sensitive=True
            ),
            "CORS_ORIGINS": EnvironmentVariable(
                name="CORS_ORIGINS",
                default="http://localhost:5002,http://127.0.0.1:5002",
                var_type=str,
                description="CORS允许的源"
            ),
            
            # 缓存配置
            "CACHE_TTL": EnvironmentVariable(
                name="CACHE_TTL",
                default=3600,
                var_type=int,
                description="缓存过期时间(秒)"
            ),
            "CACHE_MAX_SIZE": EnvironmentVariable(
                name="CACHE_MAX_SIZE",
                default=1000,
                var_type=int,
                description="缓存最大条目数"
            ),
        }
    
    def _detect_environment(self) -> EnvironmentType:
        """检测当前环境"""
        env = self.get("ENVIRONMENT", "development").lower()
        
        if env in ["prod", "production"]:
            return EnvironmentType.PRODUCTION
        elif env in ["stage", "staging"]:
            return EnvironmentType.STAGING
        else:
            return EnvironmentType.DEVELOPMENT
    
    def get(self, name: str, default: Any = None) -> Any:
        """获取环境变量值"""
        # 如果已经加载过，直接返回
        if name in self._loaded_values:
            return self._loaded_values[name]
        
        # 获取环境变量定义
        var_def = self._variables.get(name)
        if not var_def:
            # 未定义的变量，直接从环境变量获取
            value = os.getenv(name, default)
            self._loaded_values[name] = value
            return value
        
        # 从环境变量获取原始值
        raw_value = os.getenv(var_def.name)
        
        if raw_value is None:
            # 使用默认值
            if var_def.required and default is None:
                logger.error(f"必需的环境变量 {name} 未设置")
                raise ValueError(f"必需的环境变量 {name} 未设置")
            
            value = default if default is not None else var_def.default
        else:
            # 类型转换
            try:
                value = self._convert_type(raw_value, var_def.var_type)
            except (ValueError, TypeError) as e:
                logger.error(f"环境变量 {name} 类型转换失败: {e}")
                value = var_def.default
        
        # 验证
        if var_def.validator and not var_def.validator(value):
            logger.error(f"环境变量 {name} 验证失败: {value}")
            value = var_def.default
        
        self._loaded_values[name] = value
        return value
    
    def _convert_type(self, value: str, target_type: type) -> Any:
        """类型转换"""
        if target_type == bool:
            return value.lower() in ("true", "1", "yes", "on")
        elif target_type == int:
            return int(value)
        elif target_type == float:
            return float(value)
        elif target_type == list:
            return [item.strip() for item in value.split(",")]
        else:
            return value
    
    def get_cors_origins(self) -> List[str]:
        """获取CORS源列表"""
        origins_str = self.get("CORS_ORIGINS")
        return [origin.strip() for origin in origins_str.split(",")]
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self._environment_type == EnvironmentType.PRODUCTION
    
    def is_debug(self) -> bool:
        """是否为调试模式"""
        if self.is_production():
            return False
        return self.get("DEBUG", True)
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            "url": self.get("DATABASE_URL"),
            "echo": self.is_debug(),
            "pool_size": 10 if self.is_production() else 5,
            "max_overflow": 20 if self.is_production() else 10
        }
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return {
            "host": self.get("HOST"),
            "port": self.get("PORT"),
            "workers": self.get("WORKERS"),
            "debug": self.is_debug(),
            "reload": not self.is_production()
        }
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return {
            "deepseek": {
                "api_key": self.get("DEEPSEEK_API_KEY"),
                "base_url": self.get("DEEPSEEK_API_BASE"),
                "r1_model": self.get("DEEPSEEK_R1_MODEL"),
                "chat_model": self.get("DEEPSEEK_CHAT_MODEL"),
                "max_tokens": 8192,
                "temperature": 0.1
            }
        }
    
    def load_from_file(self, file_path: str) -> None:
        """从.env文件加载环境变量"""
        env_file = Path(file_path)
        if not env_file.exists():
            logger.warning(f"环境变量文件不存在: {file_path}")
            return
        
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip().strip('"\'')
                            
                            # 只设置未在系统环境变量中设置的值
                            if key not in os.environ:
                                os.environ[key] = value
            
            logger.info(f"从文件加载环境变量: {file_path}")
            
        except Exception as e:
            logger.error(f"加载环境变量文件失败: {e}")
    
    def get_environment_report(self) -> Dict[str, Any]:
        """获取环境变量报告"""
        report = {
            "environment_type": self._environment_type.value,
            "loaded_variables": {},
            "missing_required": [],
            "validation_errors": []
        }
        
        for name, var_def in self._variables.items():
            try:
                value = self.get(name)
                # 敏感信息脱敏
                if var_def.sensitive and value:
                    display_value = f"{value[:4]}***{value[-4:]}" if len(value) > 8 else "***"
                else:
                    display_value = value
                
                report["loaded_variables"][name] = {
                    "value": display_value,
                    "type": var_def.var_type.__name__,
                    "description": var_def.description,
                    "required": var_def.required,
                    "sensitive": var_def.sensitive
                }
                
            except ValueError as e:
                if var_def.required:
                    report["missing_required"].append(name)
                report["validation_errors"].append(f"{name}: {str(e)}")
        
        return report


# 全局环境管理器实例
_environment_manager = None


def get_environment_manager() -> EnvironmentManager:
    """获取全局环境管理器实例"""
    global _environment_manager
    if _environment_manager is None:
        _environment_manager = EnvironmentManager()
        # 尝试加载.env文件
        _environment_manager.load_from_file(".env")
    return _environment_manager


def get_env(name: str, default: Any = None) -> Any:
    """获取环境变量的便捷函数"""
    return get_environment_manager().get(name, default)


def is_production() -> bool:
    """是否为生产环境的便捷函数"""
    return get_environment_manager().is_production()


def is_debug() -> bool:
    """是否为调试模式的便捷函数"""
    return get_environment_manager().is_debug()
