"""
检索器模块 - 统一检索接口

提供统一的检索策略和知识库管理功能
重构后使用统一的专业级检索器，消除重复代码
"""

# 基础检索器
from .text_retriever import TextRetriever

# 多模态检索器 - 使用增强版本
try:
    from .enhanced_multimodal_retriever import EnhancedMultimodalRetriever
    # 创建别名以保持向后兼容
    MultimodalRetriever = EnhancedMultimodalRetriever
    ENHANCED_MULTIMODAL_AVAILABLE = True
except ImportError:
    # 如果增强版本不可用，创建一个模拟类
    class MockMultimodalRetriever:
        def __init__(self, *args, **kwargs):
            pass
        def search(self, *args, **kwargs):
            return []
    MultimodalRetriever = MockMultimodalRetriever
    ENHANCED_MULTIMODAL_AVAILABLE = False

# 统一专业级检索器 - 主要检索接口
try:
    from .unified_professional_retriever import UnifiedProfessionalRetriever, get_unified_retriever
    # 创建统一检索器别名
    UnifiedRetriever = UnifiedProfessionalRetriever
    KnowledgeBase = UnifiedProfessionalRetriever  # 知识库别名，使用统一检索器
    UNIFIED_RETRIEVER_AVAILABLE = True
    ENHANCED_KB_AVAILABLE = True  # 向后兼容
except ImportError:
    # 如果统一检索器不可用，创建一个模拟类
    class MockUnifiedRetriever:
        def __init__(self, *args, **kwargs):
            self.is_available = False
        def search(self, *args, **kwargs):
            return {"success": False, "results": [], "error": "检索器不可用"}
    UnifiedRetriever = MockUnifiedRetriever
    KnowledgeBase = MockUnifiedRetriever
    UNIFIED_RETRIEVER_AVAILABLE = False
    ENHANCED_KB_AVAILABLE = False

# 增强多模态检索器已在上面处理

__all__ = [
    "TextRetriever",
    "MultimodalRetriever",
    "KnowledgeBase",
    "UnifiedRetriever",
    "UNIFIED_RETRIEVER_AVAILABLE",
    "ENHANCED_KB_AVAILABLE",
    "ENHANCED_MULTIMODAL_AVAILABLE",
    "get_unified_retriever"
]

# 添加可用的检索器到导出列表
if UNIFIED_RETRIEVER_AVAILABLE:
    __all__.append('UnifiedProfessionalRetriever')

if ENHANCED_MULTIMODAL_AVAILABLE:
    __all__.append('EnhancedMultimodalRetriever')


# 工厂函数，用于获取统一检索器实例
def get_unified_retriever(config: dict = None):
    """
    获取统一检索器实例

    Args:
        config: 配置参数

    Returns:
        统一检索器实例
    """
    if not UNIFIED_RETRIEVER_AVAILABLE:
        raise ImportError("统一检索器不可用")

    # 如果已经从模块导入了get_unified_retriever，使用它
    try:
        from .unified_professional_retriever import get_unified_retriever as _get_unified_retriever
        return _get_unified_retriever(config)
    except ImportError:
        return UnifiedProfessionalRetriever(config or {})
