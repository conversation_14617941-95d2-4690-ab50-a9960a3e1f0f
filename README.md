# 故障分析智能助手

基于DeepSeek大语言模型的电力系统故障分析智能助手系统，集成RAG检索增强生成、LangChain框架和FAISS向量数据库，为电力运维人员提供智能化的故障诊断和分析服务。

## 🚀 项目特性

- **智能故障分析**: 基于DeepSeek LLM的故障原因分析和处理建议
- **多模态处理**: 支持文本、图像、OCR识别等多种数据输入
- **RAG检索增强**: 结合知识库进行上下文增强的智能分析
- **设备管理**: 完整的电力设备信息管理系统
- **检查报告解析**: 自动解析现场检查报告和巡视记录
- **运行方式分析**: 电力系统运行方式和倒闸操作分析
- **Web可视化界面**: 现代化的Web前端界面
- **RESTful API**: 完整的API接口支持

## 📋 系统架构

```
故障分析智能助手/
├── api/                    # FastAPI接口层
│   ├── main.py            # 主应用入口
│   ├── models.py          # 数据模型
│   └── routers/           # 路由模块
├── core/                  # 核心业务逻辑
│   ├── fault_analyzer.py  # 故障分析器
│   ├── equipment_manager.py # 设备管理器
│   ├── inspection_parser.py # 检查解析器
│   └── operation_analyzer.py # 运行分析器
├── data_processing/       # 数据处理模块
│   ├── text_processor.py  # 文本处理
│   ├── image_processor.py # 图像处理
│   ├── ocr_processor.py   # OCR处理
│   └── vector_processor.py # 向量处理
├── retriever/            # 检索系统
│   ├── text_retriever.py  # 文本检索
│   ├── enhanced_multimodal_retriever.py # 增强多模态检索
│   ├── enhanced_knowledge_base.py # 增强知识库管理
│   └── unified_professional_retriever.py # 统一专业检索器
├── langchain_modules/    # LangChain集成
│   ├── tools/            # 自定义工具
│   ├── chains/           # 分析链
│   └── prompts/          # 提示模板
├── ui/                   # 前端界面
│   ├── templates/        # HTML模板
│   └── static/           # 静态资源
├── server/               # Web服务器
├── test/                 # 测试文件
├── configs/              # 配置文件
└── requirements.txt      # 依赖包
```

## 🛠️ 技术栈

- **后端框架**: FastAPI, Flask
- **AI框架**: LangChain, DeepSeek LLM
- **向量数据库**: FAISS
- **文本嵌入**: Sentence Transformers
- **OCR引擎**: PaddleOCR, EasyOCR, Tesseract
- **图像处理**: OpenCV, PIL
- **前端**: HTML5, CSS3, JavaScript, Bootstrap
- **数据处理**: Pandas, NumPy
- **测试框架**: Pytest

## 📦 安装部署

### 环境要求

- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd fault-diagnosis-assistant
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，填入必要的配置信息
```

5. **初始化数据库和向量存储**
```bash
python scripts/init_database.py
python scripts/init_vector_store.py
```

### 启动服务

1. **启动API服务**
```bash
cd api
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

2. **启动Web服务**
```bash
cd server
python web_server.py
```

3. **访问系统**
- API文档: http://localhost:8000/docs
- Web界面: http://localhost:3000

## 🔧 配置说明

主要配置文件位于 `configs/config.yaml`：

```yaml
# LLM配置
llm:
  deepseek:
    model_name: "deepseek-chat"
    api_key: "your-api-key"
    base_url: "https://api.deepseek.com"
    max_tokens: 4096
    temperature: 0.7

# 向量数据库配置
vector_db:
  type: "faiss"
  index_path: "./embeddings/faiss_store"
  similarity_threshold: 0.7
  top_k: 10

# 数据处理配置
data_processing:
  ocr:
    engine: "paddleocr"
    languages: ["ch", "en"]
    confidence_threshold: 0.8
```

## 📖 使用指南

### API接口使用

1. **故障分析**
```python
import requests

data = {
    "equipment_info": "110kV主变压器",
    "fault_symptoms": "异响，温度升高",
    "inspection_results": "外观正常，无明显异常",
    "historical_data": "近期无故障记录",
    "analysis_type": "comprehensive"
}

response = requests.post("http://localhost:8000/api/v1/fault/analyze", json=data)
result = response.json()
```

2. **设备管理**
```python
# 添加设备
equipment_data = {
    "name": "1号主变",
    "type": "transformer",
    "location": "110kV变电站",
    "status": "running"
}

response = requests.post("http://localhost:8000/api/v1/equipment/", json=equipment_data)
```

3. **知识库搜索**
```python
search_data = {
    "query": "变压器故障诊断",
    "search_type": "text",
    "top_k": 5
}

response = requests.post("http://localhost:8000/api/v1/knowledge/search", json=search_data)
```

### Web界面使用

1. 访问 http://localhost:3000
2. 在故障分析页面输入设备信息和故障现象
3. 点击"开始分析"获取智能分析结果
4. 在设备管理页面管理电力设备信息
5. 在知识库页面搜索相关技术资料
6. 在文件上传页面处理各类文档和图像

## 🧪 测试

本项目采用分层测试架构，包含API测试、单元测试、集成测试和Web测试。

### 快速开始

```bash
# 使用统一测试运行器运行所有测试
python test/run_tests.py

# 运行特定类型的测试
python test/run_tests.py --type api        # API测试
python test/run_tests.py --type unit       # 单元测试
python test/run_tests.py --type integration # 集成测试
python test/run_tests.py --type web        # Web测试
```

### 使用pytest

```bash
# 运行所有测试
pytest

# 运行特定目录的测试
pytest test/api/          # API测试
pytest test/unit/         # 单元测试
pytest test/integration/  # 集成测试
pytest test/web/          # Web测试

# 运行特定测试文件
pytest test/unit/test_search_comprehensive.py

# 使用标记运行测试
pytest -m unit            # 单元测试
pytest -m integration     # 集成测试

# 生成覆盖率报告
pytest --cov=. --cov-report=html
```

### 测试目录结构

```
test/
├── api/                  # API接口测试
├── integration/          # 集成测试
├── unit/                 # 单元测试
├── web/                  # Web界面测试
├── html/                 # HTML测试页面
├── performance/          # 性能测试
└── utils/                # 测试工具
```

详细测试文档请参考 [test/README.md](test/README.md)

## 📝 开发指南

### 添加新功能

1. 在相应模块中实现核心逻辑
2. 在API层添加接口端点
3. 更新前端界面（如需要）
4. 编写相应的测试用例
5. 更新文档

### 代码规范

- 遵循PEP 8代码风格
- 使用类型注解
- 编写详细的文档字符串
- 保持函数和类的单一职责

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/fault-diagnosis-assistant]

## 🙏 致谢

- DeepSeek AI 提供的大语言模型支持
- LangChain 框架的强大功能
- 开源社区的各种优秀工具和库
